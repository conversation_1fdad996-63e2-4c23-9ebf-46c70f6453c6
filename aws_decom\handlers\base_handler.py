"""
Base resource handler for AWS Resource Decommissioning Tool.
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class BaseResourceHandler(ABC):
    """
    Base class for AWS resource handlers.
    
    Provides common functionality for resource handlers.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the resource handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.config = config_manager
    
    @abstractmethod
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a resource exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        pass
    
    @abstractmethod
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        pass
    
    @abstractmethod
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of resource deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        pass
    
    @abstractmethod
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a resource.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        pass
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a resource.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a resource.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        return {}
    
    def handle_client_error(self, e: ClientError, operation: str) -> Tuple[bool, str]:
        """
        Handle a ClientError from boto3.
        
        Args:
            e: ClientError exception.
            operation: Operation being performed.
            
        Returns:
            Tuple of (success, message).
        """
        error_code = e.response.get('Error', {}).get('Code', '')
        error_message = e.response.get('Error', {}).get('Message', str(e))
        
        # Handle DryRunOperation error (success for dry run)
        if error_code == 'DryRunOperation':
            return True, f"Dry run successful: {error_message}"
        
        # Handle resource not found errors
        if error_code in ['ResourceNotFoundException', 'NoSuchEntity', 'NoSuchBucket', 'NoSuchKey']:
            return False, f"Resource not found: {error_message}"
        
        # Handle dependency violations
        if error_code in ['DependencyViolation', 'ResourceInUse']:
            return False, f"Resource has dependencies: {error_message}"
        
        # Handle permission errors
        if error_code in ['AccessDenied', 'UnauthorizedOperation']:
            return False, f"Permission denied: {error_message}"
        
        # Handle other errors
        self.logger.error(f"Error during {operation}: {error_code} - {error_message}")
        return False, f"Error: {error_code} - {error_message}"
    
    def execute_with_timing(self, func, *args, **kwargs) -> Tuple[Any, float]:
        """
        Execute a function and measure its execution time.
        
        Args:
            func: Function to execute.
            *args: Arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.
            
        Returns:
            Tuple of (function result, execution time in seconds).
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time
