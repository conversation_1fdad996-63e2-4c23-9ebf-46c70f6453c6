"""
AWS Backup Vault handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class BackupVaultHandler(BaseResourceHandler):
    """
    Handler for AWS Backup Vaults.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the Backup Vault handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a Backup Vault exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Check if vault exists
            response = backup.describe_backup_vault(BackupVaultName=vault_name)
            
            if response:
                # Check if vault is locked
                try:
                    lock_response = backup.get_backup_vault_lock_configuration(BackupVaultName=vault_name)
                    if lock_response.get('LockState') == 'LOCKED':
                        return False, f"Backup Vault {vault_name} is locked and cannot be deleted"
                except ClientError as e:
                    # If we get a resource not found error, it means the vault is not locked
                    if e.response.get('Error', {}).get('Code') != 'ResourceNotFoundException':
                        self.logger.warning(f"Error checking vault lock: {e}")
                
                return True, f"Backup Vault {vault_name} exists and can be deleted"
            else:
                return False, f"Backup Vault {vault_name} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating Backup Vault")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a Backup Vault.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Check for recovery points in the vault
            try:
                paginator = backup.get_paginator('list_recovery_points_by_backup_vault')
                page_iterator = paginator.paginate(BackupVaultName=vault_name)
                
                recovery_point_count = 0
                for page in page_iterator:
                    recovery_points = page.get('RecoveryPoints', [])
                    recovery_point_count += len(recovery_points)
                    
                    # Add a sample of recovery points as dependencies
                    if len(dependencies) < 10:  # Limit to 10 sample recovery points
                        for point in recovery_points:
                            if len(dependencies) < 10:
                                recovery_point_arn = point.get('RecoveryPointArn', '')
                                dependencies.append(ResourceDependency(
                                    resource_type="backup:recovery-point",
                                    resource_id=recovery_point_arn,
                                    relationship="stored_in",
                                    blocking=True  # Blocking because vault must be empty
                                ))
                
                if recovery_point_count > 0:
                    self.logger.info(f"Backup Vault {vault_name} contains {recovery_point_count} recovery points")
            except ClientError as e:
                self.logger.warning(f"Error checking recovery points for vault {vault_name}: {e}")
            
            # Check for vault lock
            try:
                lock_response = backup.get_backup_vault_lock_configuration(BackupVaultName=vault_name)
                if lock_response.get('LockState') in ['LOCKED', 'LOCKING']:
                    dependencies.append(ResourceDependency(
                        resource_type="backup:vault-lock",
                        resource_id=f"{vault_name}-lock",
                        relationship="locks",
                        blocking=True  # Blocking because locked vaults cannot be deleted
                    ))
            except ClientError as e:
                # If we get a resource not found error, it means the vault is not locked
                if e.response.get('Error', {}).get('Code') != 'ResourceNotFoundException':
                    self.logger.warning(f"Error checking vault lock: {e}")
            
            # Check for access policies
            try:
                policy_response = backup.get_backup_vault_access_policy(BackupVaultName=vault_name)
                if policy_response.get('Policy'):
                    dependencies.append(ResourceDependency(
                        resource_type="backup:vault-access-policy",
                        resource_id=f"{vault_name}-policy",
                        relationship="attached_to",
                        blocking=False  # Not blocking because policies are deleted with the vault
                    ))
            except ClientError as e:
                # If we get a resource not found error, it means the vault has no access policy
                if e.response.get('Error', {}).get('Code') != 'ResourceNotFoundException':
                    self.logger.warning(f"Error checking vault access policy: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for Backup Vault {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of Backup Vault deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Check if vault exists
            try:
                backup.describe_backup_vault(BackupVaultName=vault_name)
            except ClientError as e:
                return self.handle_client_error(e, "checking if Backup Vault exists")
            
            # Check for vault lock
            try:
                lock_response = backup.get_backup_vault_lock_configuration(BackupVaultName=vault_name)
                if lock_response.get('LockState') == 'LOCKED':
                    return False, f"Backup Vault {vault_name} is locked and cannot be deleted"
            except ClientError as e:
                # If we get a resource not found error, it means the vault is not locked
                if e.response.get('Error', {}).get('Code') != 'ResourceNotFoundException':
                    self.logger.warning(f"Error checking vault lock: {e}")
            
            # Check for recovery points
            try:
                paginator = backup.get_paginator('list_recovery_points_by_backup_vault')
                page_iterator = paginator.paginate(BackupVaultName=vault_name, MaxResults=1)
                
                for page in page_iterator:
                    if page.get('RecoveryPoints'):
                        # Vault is not empty
                        force_delete = self.config.get("resources.services.backup.force_vault_deletion", False)
                        if not force_delete:
                            return False, f"Backup Vault {vault_name} is not empty and force_vault_deletion is not enabled"
                        else:
                            return True, f"Dry run successful: Would delete all recovery points and then delete Backup Vault {vault_name}"
            except ClientError as e:
                self.logger.warning(f"Error checking recovery points for vault {vault_name}: {e}")
            
            return True, f"Dry run successful: Would delete Backup Vault {vault_name}"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of Backup Vault")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a Backup Vault.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Check if vault exists
            try:
                backup.describe_backup_vault(BackupVaultName=vault_name)
            except ClientError as e:
                return self.handle_client_error(e, "checking if Backup Vault exists")
            
            # Check for vault lock
            try:
                lock_response = backup.get_backup_vault_lock_configuration(BackupVaultName=vault_name)
                if lock_response.get('LockState') == 'LOCKED':
                    return False, f"Backup Vault {vault_name} is locked and cannot be deleted"
            except ClientError as e:
                # If we get a resource not found error, it means the vault is not locked
                if e.response.get('Error', {}).get('Code') != 'ResourceNotFoundException':
                    self.logger.warning(f"Error checking vault lock: {e}")
            
            # Check for recovery points
            recovery_points = []
            try:
                paginator = backup.get_paginator('list_recovery_points_by_backup_vault')
                page_iterator = paginator.paginate(BackupVaultName=vault_name)
                
                for page in page_iterator:
                    recovery_points.extend(page.get('RecoveryPoints', []))
            except ClientError as e:
                self.logger.warning(f"Error checking recovery points for vault {vault_name}: {e}")
            
            if recovery_points:
                # Vault is not empty
                force_delete = self.config.get("resources.services.backup.force_vault_deletion", False)
                if not force_delete:
                    return False, f"Backup Vault {vault_name} is not empty and force_vault_deletion is not enabled"
                else:
                    # Delete all recovery points
                    self.logger.info(f"Deleting {len(recovery_points)} recovery points from vault {vault_name}")
                    
                    for point in recovery_points:
                        recovery_point_arn = point.get('RecoveryPointArn', '')
                        if recovery_point_arn:
                            try:
                                backup.delete_recovery_point(
                                    BackupVaultName=vault_name,
                                    RecoveryPointArn=recovery_point_arn
                                )
                                self.logger.info(f"Deleted recovery point {recovery_point_arn}")
                            except ClientError as e:
                                self.logger.error(f"Error deleting recovery point {recovery_point_arn}: {e}")
                                return False, f"Error deleting recovery point {recovery_point_arn}: {str(e)}"
            
            # Delete the vault
            backup.delete_backup_vault(BackupVaultName=vault_name)
            
            return True, f"Successfully deleted Backup Vault {vault_name}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of Backup Vault")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a Backup Vault.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Get vault details
            response = backup.describe_backup_vault(BackupVaultName=vault_name)
            
            if response:
                created_date = response.get('CreationDate')
                
                if created_date:
                    return created_date.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation date for Backup Vault {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a Backup Vault.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract vault name from resource ID
            vault_name = resource.resource_id
            
            # Get vault ARN
            try:
                response = backup.describe_backup_vault(BackupVaultName=vault_name)
                if not response:
                    return {}
                
                vault_arn = response.get('BackupVaultArn')
                if not vault_arn:
                    return {}
                
                # Get tags for the vault
                tags_response = backup.list_tags(ResourceArn=vault_arn)
                
                return tags_response.get('Tags', {})
                
            except ClientError as e:
                self.logger.warning(f"Error getting vault ARN: {e}")
                return {}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for Backup Vault {resource.resource_id}: {e}")
            return {}
