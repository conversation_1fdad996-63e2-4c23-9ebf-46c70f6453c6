2025-04-29 01:30:04,835 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,835 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,835 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,845 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,846 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,846 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,847 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,849 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,849 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,851 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,852 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,853 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,853 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:04,855 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,859 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,859 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,859 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,859 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,859 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,865 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,867 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,869 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,871 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:04,872 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,872 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,875 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,875 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,876 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,876 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,876 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,876 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,876 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,876 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:04,876 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,882 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,884 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,886 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,886 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,876 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,882 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:04,889 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,889 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,889 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,889 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,895 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,895 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,895 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:04,895 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,895 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,895 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,895 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,895 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,895 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,902 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,903 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,905 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,905 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:04,906 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,908 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,909 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,909 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,910 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,911 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,912 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,912 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,912 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,913 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:04,913 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,913 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,927 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,927 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,917 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,921 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,923 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,925 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,925 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,926 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,927 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,915 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,921 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:04,928 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,928 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,928 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,928 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,929 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,929 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,929 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,929 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,929 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:04,930 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,930 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,930 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,931 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,932 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,932 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:04,933 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:05,104 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,104 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,111 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,111 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,111 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,115 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,125 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,136 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,141 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:05,155 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,155 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,155 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,161 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,161 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,165 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,171 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,175 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:05,175 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,175 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,233 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,245 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,246 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,255 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,285 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,285 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,295 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,320 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,325 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,295 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,325 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,325 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,325 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,354 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,335 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,354 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,354 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:05,390 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:05,354 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,354 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,335 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,430 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,375 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,375 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,354 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,445 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,430 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,430 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,354 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,445 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,445 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,390 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:05,445 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,445 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,445 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,445 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,445 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:05,445 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,445 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,445 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,445 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,485 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,485 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,485 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,506 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:05,525 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,525 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,525 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,525 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,525 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,525 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:05,525 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:05,535 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:06,712 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:06,764 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:06,799 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:06,925 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:06,965 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:07,009 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:07,055 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:07,106 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:07,155 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:07,195 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:08,205 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,215 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,270 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,270 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,270 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,270 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,270 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,275 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,275 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,280 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,280 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,280 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,280 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,280 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,280 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,280 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,285 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,285 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,285 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,285 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-087886ad913505771', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F26CEA4830>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,285 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F270A558E0>>
2025-04-29 01:30:08,285 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,285 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,290 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,299 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,306 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,306 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,306 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,306 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,306 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,306 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,306 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,306 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,306 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,306 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,314 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,315 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,315 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,315 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,315 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,315 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,318 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,318 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27C8AA030>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,318 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27C8AA090>>
2025-04-29 01:30:08,319 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,319 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27C8AAC90>>
2025-04-29 01:30:08,319 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,320 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'093540f7-bfae-4275-bb56-ff39887ccec9', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,321 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,324 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,330 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,331 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,331 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,331 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,331 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,331 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,333 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,334 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,334 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,334 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,334 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,334 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,334 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,334 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,348 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,348 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,348 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,348 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,348 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,348 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,348 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,348 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,348 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,348 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,348 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,354 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-06f9ef925e37efd17', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27CA07890>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,355 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,355 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27CA078C0>>
2025-04-29 01:30:08,355 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,355 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,355 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,355 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,355 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,355 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,355 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,355 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,355 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,355 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,355 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,355 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,355 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,364 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,364 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,370 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,370 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-0a8e2d1d949a06cd0', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27C8A9640>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,370 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,370 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,370 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27C8A9670>>
2025-04-29 01:30:08,370 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,370 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,370 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,370 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,380 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,380 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,380 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,380 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,380 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,380 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,380 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,380 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,380 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,380 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,385 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,385 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,385 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,606 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,606 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,606 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,606 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,606 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,606 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,606 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,606 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,606 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,614 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,614 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,614 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,614 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,614 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,614 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,614 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,625 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,625 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,625 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,631 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,631 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,631 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,631 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,631 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,631 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,631 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,635 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,635 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,644 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,644 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,644 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,644 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,644 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,644 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,644 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:08,644 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,649 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,649 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,657 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,657 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,657 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,657 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,669 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,669 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,669 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,669 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,669 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,669 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,669 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,669 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,669 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,675 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,675 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,675 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,675 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,675 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,675 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:08,675 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,675 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,675 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-041570276467c4850', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27CC83C20>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,675 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,675 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,675 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:08,675 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,675 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,675 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27CC83CE0>>
2025-04-29 01:30:08,675 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,675 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,675 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,675 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,675 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,675 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,684 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,684 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,684 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,684 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,684 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,684 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-09b8ea45284479875', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27057B410>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,684 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,684 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:08,684 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,684 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,684 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,684 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,684 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,695 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27057BFB0>>
2025-04-29 01:30:08,695 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,695 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,695 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-0fb6c6121c0c0411c', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F270738E30>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,695 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,695 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,695 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,695 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,695 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,695 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,695 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F270738D10>>
2025-04-29 01:30:08,695 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,695 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:08,695 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,695 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,695 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,695 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,695 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,705 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,705 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,705 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,705 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,705 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,705 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,705 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,705 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,705 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,705 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,705 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,705 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,705 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,714 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,715 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,715 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,715 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,715 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,717 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,717 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,718 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,718 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,718 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-0744b90d043ddddff', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D067530>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,718 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,719 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,719 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,719 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,719 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,719 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D067560>>
2025-04-29 01:30:08,719 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:08,719 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,719 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,719 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,719 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,719 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,719 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D098AA0>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,719 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,719 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,719 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,719 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-022732e968cba9142', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D099010>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,719 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,719 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D098EC0>>
2025-04-29 01:30:08,725 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,725 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D099040>>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,725 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,725 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,725 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-00e0cdac1a0eb0a21', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D09AF00>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,725 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,725 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,725 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D09AED0>>
2025-04-29 01:30:08,725 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,725 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-0c7b6d9a6a2ed1028', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D098DD0>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:08,725 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D09AF60>>
2025-04-29 01:30:08,725 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,725 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,725 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,725 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,725 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D067110>>
2025-04-29 01:30:08,725 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,725 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D119F40>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,725 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,725 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,725 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'edf40d36-f14e-4dfa-8fe4-b220e3df50ea', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,725 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,725 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,725 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,725 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,725 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D119FA0>>
2025-04-29 01:30:08,725 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,725 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,735 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,735 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:08,735 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,735 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,735 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,735 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D11B590>>
2025-04-29 01:30:08,735 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,735 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,735 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,735 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,735 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'd5a4c200-97f3-4cba-8506-25a455489a45', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,735 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,735 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,735 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,735 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,745 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,745 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,745 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,745 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,745 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:08,745 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,745 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,745 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,745 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,745 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,754 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,754 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,754 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,754 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,754 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,754 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,754 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,754 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D1C41A0>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,754 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,754 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,754 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,754 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,754 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D1C5CA0>>
2025-04-29 01:30:08,754 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,754 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,754 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,754 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,754 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,754 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,754 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,754 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D1C6AE0>>
2025-04-29 01:30:08,754 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,754 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,754 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:08,754 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,754 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,754 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,754 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,754 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,764 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,764 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,764 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'68aab2f8-0482-458d-a5b9-6a8754bab781', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,765 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,765 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D1C76E0>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,765 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,765 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,767 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,767 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D1C7740>>
2025-04-29 01:30:08,767 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,767 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,767 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,767 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,767 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,767 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D22CE00>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:08,767 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,767 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D22CA70>>
2025-04-29 01:30:08,767 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D22CDD0>>
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:08,767 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,767 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,767 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,767 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,767 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:08,774 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'0f95a514-7fde-4072-b0c7-8f487f7daafb', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,774 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D22F590>>
2025-04-29 01:30:08,774 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,774 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,774 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,774 [DEBUG] No configured endpoint found.
2025-04-29 01:30:08,774 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,774 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,774 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,774 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,774 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,774 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:08,774 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'4aa16c25-48fb-4a24-a6dc-23720d1a734d', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,774 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,774 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,774 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,774 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:08,774 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,774 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,774 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,774 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,774 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:08,774 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,785 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,785 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,785 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D272060>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,785 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,786 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D272030>>
2025-04-29 01:30:08,786 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,786 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,786 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,786 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,786 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D273860>>
2025-04-29 01:30:08,786 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,786 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,786 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:08,786 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D2738C0>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,786 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'8d7022f0-f7be-493e-a7a9-7a14536b2884', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D270980>>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:08,786 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,786 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,786 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D2CCBF0>>
2025-04-29 01:30:08,786 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D2CD1C0>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,786 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27D2CD370>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D2CD190>>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27D2CD250>>
2025-04-29 01:30:08,786 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'178099b1-d2cd-44a5-8c2c-5e4036548102', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,786 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,786 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D2CE030>>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D2CEE10>>
2025-04-29 01:30:08,786 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,786 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:08,786 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'22821fa3-5fc3-4387-b25c-8266126dbace', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,786 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'34845e9c-a66a-4b69-95e3-7e0db3f13c47', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:08,796 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:08,796 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,255 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,255 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '78a5a486-58d8-4478-8701-182c84c0ca78', 'Server': 'AWS SSO', 'x-amzn-RequestId': '78a5a486-58d8-4478-8701-182c84c0ca78'}
2025-04-29 01:30:12,255 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"LE8YBFiI1NfCkUqw0WPqP0HryEocKQwLdGSbk92T","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIE1XyeV5G7AjsLnU6Y4mjcn8PAtPXU/mFSDKN31E96KJAiEA+DuNzFMqrNMOnP3yjYcXHI+joYarJeqeJ+7lEk8lTzYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDPKz73K1AfNIQNNhzirMAnOTyrgJpcWaBarF9UJ271gGnlUSviJQNLtf6F9eIh0KSZjtUuKWGwWRohQRN9/4rPoUR48Xbmo1GilNfxBqKmi/7hQUq+D1wAJP+V/JPXLf3eIEzlX10LeCGrHiV0uFyA6qVPLfO94qNPKqBY6n+KMfUeXQYsPf+yF3/jFdCNEErShkGUfaB51wT9NMTR9ui8MykQc6AOFhdjeMg+dVLrPjEf1so2ru9VzMM4KhyJMExNWo5cYoUxVz9hRWFKP2FlucIE2JaUhNaktmbWkKaVzy1BMF7G9rsGzHzZaeNEe997/LNbf9+PQULX3Jei+WsMx04aG9s+FRC/OUHEsMWofFYA4px2las0ugh6V9dL33CnATFfwlp9lK1Cs4wz259rd1tHr4alRLBlCthbPm7wFYzd3Umodar0NzJdSrY6+DerxSKKx4kZNcUGoBMMu8v8AGOqcB8gIxpOyG8fsmFUG5E1YsXYH1CbR9mQuPtSy0YhEMuJ7/b5K12ntz4myaTRTbWgN7ZKO9bM/7Gz5G0nF0eMHB4yZ9T4MvE+NDC25Ec3dl2vCK/Tk+Bwhme4ZJxMbMqUddLpt5bkjAFpU8jm0fvOYIyIXqZZx9fvoKLnY2AvUlF9glI9d8NF5GL3uDmCANCZeEheLcroGmoGUCsH3c6UXo8jd33xj4C/k=","expiration":1745874010000}}'
2025-04-29 01:30:12,255 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27C8AAF00>
2025-04-29 01:30:12,255 [DEBUG] No retry needed.
2025-04-29 01:30:12,255 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,255 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,255 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIE1XyeV5G7AjsLnU6Y4mjcn8PAtPXU/mFSDKN31E96KJAiEA+DuNzFMqrNMOnP3yjYcXHI+joYarJeqeJ+7lEk8lTzYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDPKz73K1AfNIQNNhzirMAnOTyrgJpcWaBarF9UJ271gGnlUSviJQNLtf6F9eIh0KSZjtUuKWGwWRohQRN9/4rPoUR48Xbmo1GilNfxBqKmi/7hQUq+D1wAJP+V/JPXLf3eIEzlX10LeCGrHiV0uFyA6qVPLfO94qNPKqBY6n+KMfUeXQYsPf+yF3/jFdCNEErShkGUfaB51wT9NMTR9ui8MykQc6AOFhdjeMg+dVLrPjEf1so2ru9VzMM4KhyJMExNWo5cYoUxVz9hRWFKP2FlucIE2JaUhNaktmbWkKaVzy1BMF7G9rsGzHzZaeNEe997/LNbf9+PQULX3Jei+WsMx04aG9s+FRC/OUHEsMWofFYA4px2las0ugh6V9dL33CnATFfwlp9lK1Cs4wz259rd1tHr4alRLBlCthbPm7wFYzd3Umodar0NzJdSrY6+DerxSKKx4kZNcUGoBMMu8v8AGOqcB8gIxpOyG8fsmFUG5E1YsXYH1CbR9mQuPtSy0YhEMuJ7/b5K12ntz4myaTRTbWgN7ZKO9bM/7Gz5G0nF0eMHB4yZ9T4MvE+NDC25Ec3dl2vCK/Tk+Bwhme4ZJxMbMqUddLpt5bkjAFpU8jm0fvOYIyIXqZZx9fvoKLnY2AvUlF9glI9d8NF5GL3uDmCANCZeEheLcroGmoGUCsH3c6UXo8jd33xj4C/k=

content-type;host;x-amz-date;x-amz-security-token
b3d56d90439d82c1070f19f09715e79948cd4b3d96cd6bc060fb9c8c9e918f28
2025-04-29 01:30:12,255 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
8df369ce61b28eba3b34304949317b4d90877864885d4dd066f7a5a3c577fab4
2025-04-29 01:30:12,255 [DEBUG] Signature:
7aecf7c7528a9f12c5e8e1a7102a0ddd9f7d8c88a6e1c75a6e1137fb7e95cbe6
2025-04-29 01:30:12,255 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F2714DF170>>
2025-04-29 01:30:12,255 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,255 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIE1XyeV5G7AjsLnU6Y4mjcn8PAtPXU/mFSDKN31E96KJAiEA+DuNzFMqrNMOnP3yjYcXHI+joYarJeqeJ+7lEk8lTzYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDPKz73K1AfNIQNNhzirMAnOTyrgJpcWaBarF9UJ271gGnlUSviJQNLtf6F9eIh0KSZjtUuKWGwWRohQRN9/4rPoUR48Xbmo1GilNfxBqKmi/7hQUq+D1wAJP+V/JPXLf3eIEzlX10LeCGrHiV0uFyA6qVPLfO94qNPKqBY6n+KMfUeXQYsPf+yF3/jFdCNEErShkGUfaB51wT9NMTR9ui8MykQc6AOFhdjeMg+dVLrPjEf1so2ru9VzMM4KhyJMExNWo5cYoUxVz9hRWFKP2FlucIE2JaUhNaktmbWkKaVzy1BMF7G9rsGzHzZaeNEe997/LNbf9+PQULX3Jei+WsMx04aG9s+FRC/OUHEsMWofFYA4px2las0ugh6V9dL33CnATFfwlp9lK1Cs4wz259rd1tHr4alRLBlCthbPm7wFYzd3Umodar0NzJdSrY6+DerxSKKx4kZNcUGoBMMu8v8AGOqcB8gIxpOyG8fsmFUG5E1YsXYH1CbR9mQuPtSy0YhEMuJ7/b5K12ntz4myaTRTbWgN7ZKO9bM/7Gz5G0nF0eMHB4yZ9T4MvE+NDC25Ec3dl2vCK/Tk+Bwhme4ZJxMbMqUddLpt5bkjAFpU8jm0fvOYIyIXqZZx9fvoKLnY2AvUlF9glI9d8NF5GL3uDmCANCZeEheLcroGmoGUCsH3c6UXo8jd33xj4C/k=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=7aecf7c7528a9f12c5e8e1a7102a0ddd9f7d8c88a6e1c75a6e1137fb7e95cbe6', 'amz-sdk-invocation-id': b'cd193ea9-0c80-4d4f-91cc-1a24ee13b268', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,255 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,345 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,346 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '29d0100b-ab45-4a99-bc43-f74550105cf9', 'Server': 'AWS SSO', 'x-amzn-RequestId': '29d0100b-ab45-4a99-bc43-f74550105cf9'}
2025-04-29 01:30:12,346 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"nvzcV1EBZRdI/P6FnZhRal/H815+DiKb/bEWDVwO","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDzE42K2k8fANDmzg4CfR5tIWale3xbau1S/i+9ZK67eAiBOPYVi/Tl/CIIMbWOca98/oH4NckHCs91NaXScHJB7yirvAgh9EAAaDDA0NjMxOTE4NDYwOCIMQIQ+NmUc2JphC21mKswCb96ccQLSrY+PBxMVX7woUPJYwHUXQLq6kzNAvjXi9ZIpUUUvToy7ZIRoCmXUmnEzrKzssyHqnC3ut2dKR+3Ut6fPCUb6gfnl6Tiy9dJc6pQZHh91f9CHaWLS93imD6Q0EPg/kMcSshWcHpycnHMqTeYFL1bP8Ipvw2HCeAXveWPEpvj99aldtaXaWdefT8LCpV0BoDte4rQ2gSTLWqsPJICYuqH78jjP8R27uyg+2RstkrT6XNH69FU2lPf9CYuMIVMPwTGlP8aZF3lcoey3mLLoPhaku1HVtkR5otrI9F2/d2RrfEzt+usqN5SGL1tNta73E7TNRno4hHSTEqobsNNO5rrMkZktPsiPL0PYgSJl3WCbz3JxGwwVe2tK571bX2s+ztdWP3gZF7fjpep7T7IMHgH/lHc3Pr10yQb306r9byhdvuhoop+fks0wy7y/wAY6qAHWznzgH6tuHGnqSgBF1wbbbGO8xq0VRTjNe4hq1qMxFpao2Q/EjmrLIyFnzwPpgv7as+bvGqe+WZoQlVYG2dSIfrDBVvrNXKcLcO7tKfWd/rHaVsYFZ45XytAFnnSzgCCVdb2tRmT/sIHarvGu0z1vh7k22i41sUYSUpWpu5yMYRHHWK6g2M32fn9LfhbOzsQcdYm/rluY4d3MKYWNynNzY6bZVwNqyBU=","expiration":1745874010000}}'
2025-04-29 01:30:12,346 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D09B7D0>
2025-04-29 01:30:12,346 [DEBUG] No retry needed.
2025-04-29 01:30:12,347 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,347 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,347 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDzE42K2k8fANDmzg4CfR5tIWale3xbau1S/i+9ZK67eAiBOPYVi/Tl/CIIMbWOca98/oH4NckHCs91NaXScHJB7yirvAgh9EAAaDDA0NjMxOTE4NDYwOCIMQIQ+NmUc2JphC21mKswCb96ccQLSrY+PBxMVX7woUPJYwHUXQLq6kzNAvjXi9ZIpUUUvToy7ZIRoCmXUmnEzrKzssyHqnC3ut2dKR+3Ut6fPCUb6gfnl6Tiy9dJc6pQZHh91f9CHaWLS93imD6Q0EPg/kMcSshWcHpycnHMqTeYFL1bP8Ipvw2HCeAXveWPEpvj99aldtaXaWdefT8LCpV0BoDte4rQ2gSTLWqsPJICYuqH78jjP8R27uyg+2RstkrT6XNH69FU2lPf9CYuMIVMPwTGlP8aZF3lcoey3mLLoPhaku1HVtkR5otrI9F2/d2RrfEzt+usqN5SGL1tNta73E7TNRno4hHSTEqobsNNO5rrMkZktPsiPL0PYgSJl3WCbz3JxGwwVe2tK571bX2s+ztdWP3gZF7fjpep7T7IMHgH/lHc3Pr10yQb306r9byhdvuhoop+fks0wy7y/wAY6qAHWznzgH6tuHGnqSgBF1wbbbGO8xq0VRTjNe4hq1qMxFpao2Q/EjmrLIyFnzwPpgv7as+bvGqe+WZoQlVYG2dSIfrDBVvrNXKcLcO7tKfWd/rHaVsYFZ45XytAFnnSzgCCVdb2tRmT/sIHarvGu0z1vh7k22i41sUYSUpWpu5yMYRHHWK6g2M32fn9LfhbOzsQcdYm/rluY4d3MKYWNynNzY6bZVwNqyBU=

content-type;host;x-amz-date;x-amz-security-token
f37d1c4d7b992804dec0d9eec550d1376af36eaae3bfd3879194cf9d1579d180
2025-04-29 01:30:12,347 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
9dc4ec364b27a334f9c5e2f22b3271448ce339eac4b3278acfa7cafae6f9068d
2025-04-29 01:30:12,347 [DEBUG] Signature:
59761b06180a95f3b96c1494c1c9dcf68585c994b98708e7c5cb8cee4c042d32
2025-04-29 01:30:12,349 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27CA40B90>>
2025-04-29 01:30:12,349 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,349 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDzE42K2k8fANDmzg4CfR5tIWale3xbau1S/i+9ZK67eAiBOPYVi/Tl/CIIMbWOca98/oH4NckHCs91NaXScHJB7yirvAgh9EAAaDDA0NjMxOTE4NDYwOCIMQIQ+NmUc2JphC21mKswCb96ccQLSrY+PBxMVX7woUPJYwHUXQLq6kzNAvjXi9ZIpUUUvToy7ZIRoCmXUmnEzrKzssyHqnC3ut2dKR+3Ut6fPCUb6gfnl6Tiy9dJc6pQZHh91f9CHaWLS93imD6Q0EPg/kMcSshWcHpycnHMqTeYFL1bP8Ipvw2HCeAXveWPEpvj99aldtaXaWdefT8LCpV0BoDte4rQ2gSTLWqsPJICYuqH78jjP8R27uyg+2RstkrT6XNH69FU2lPf9CYuMIVMPwTGlP8aZF3lcoey3mLLoPhaku1HVtkR5otrI9F2/d2RrfEzt+usqN5SGL1tNta73E7TNRno4hHSTEqobsNNO5rrMkZktPsiPL0PYgSJl3WCbz3JxGwwVe2tK571bX2s+ztdWP3gZF7fjpep7T7IMHgH/lHc3Pr10yQb306r9byhdvuhoop+fks0wy7y/wAY6qAHWznzgH6tuHGnqSgBF1wbbbGO8xq0VRTjNe4hq1qMxFpao2Q/EjmrLIyFnzwPpgv7as+bvGqe+WZoQlVYG2dSIfrDBVvrNXKcLcO7tKfWd/rHaVsYFZ45XytAFnnSzgCCVdb2tRmT/sIHarvGu0z1vh7k22i41sUYSUpWpu5yMYRHHWK6g2M32fn9LfhbOzsQcdYm/rluY4d3MKYWNynNzY6bZVwNqyBU=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=59761b06180a95f3b96c1494c1c9dcf68585c994b98708e7c5cb8cee4c042d32', 'amz-sdk-invocation-id': b'da7c15b8-9915-43e8-b284-50c2c1141894', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,349 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,486 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,486 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '77b57ed7-00fa-4335-8a39-41e20cbb8fb1', 'Server': 'AWS SSO', 'x-amzn-RequestId': '77b57ed7-00fa-4335-8a39-41e20cbb8fb1'}
2025-04-29 01:30:12,486 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"hxaQpJtr5tSPgkz979o+pziKCkJdFr+YFsPVJf58","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDHHV2ejAcKApNK9hty/FvomHITLjvlEQomgmC2dLN5VAiBrSKzJeljtoFsHhirpdMClHqlJUMnDCjaB0x/dxNZ2cCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMtq04W9FSdLlq2IHGKswCsL67+rztuSEvGGhhOxLV7X0Bb5kIz+csm4+QJ4GTcoDGa7hLY2gZya1OmSE+hwsmGs5YLGLlykj5GXidiCyTAsbt5+YN28ecM9y29x9z4Dd4XYon/3bHVMy9vXXhdlIf4D/o5dnZrgZT/hl89rI6Tbe2rglZ9n6fygD+7l0mplbZdmCKOXkTyM9tfJkZmTlXyU0yK5AAZ4HJK2CkuZOAN43cD2nAJ0ZjboX78YyIRRIpxDcmvIH0xWb6Lq5jSZjTlpHJzV9UaZyvaDXkNG8zClsWziy/eOXl8xadYT4vrC5rpHYUZkgNMsCXipPNNbxjZbwEWUAeJSmkYHCVk95bZXqZ3MAcopr5apdr+p0+UhHo+6tq8jfONCLK4DLwJqrUNjGTitSYRyjOIwWEGHopAZda7iaDi3rgC2L66nkOG55HRa43w6lUxdzhWFEwy7y/wAY6qAF4Z4neU9kFXIzXv591javkcTBPP6eu/SyonhXuFveOf13ualdmS+Pn6nuJHn4uT1HuzRsFPc/Nyl6q/o86OB30jP9Wi4cDEFi7U0rK97Il6al7GrwWySsTKmbLzep1G4mvr86ja1GOpeNKgsOcipT7h4kfBUgFiT3FthSYNBr0Szwv7u3s7c11SoCFq/zsVeDjr8GVRREV2XAkf9+ViVfKzgDOZKOP31k=","expiration":1745874010000}}'
2025-04-29 01:30:12,486 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D1C7680>
2025-04-29 01:30:12,486 [DEBUG] No retry needed.
2025-04-29 01:30:12,486 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,495 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,495 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDHHV2ejAcKApNK9hty/FvomHITLjvlEQomgmC2dLN5VAiBrSKzJeljtoFsHhirpdMClHqlJUMnDCjaB0x/dxNZ2cCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMtq04W9FSdLlq2IHGKswCsL67+rztuSEvGGhhOxLV7X0Bb5kIz+csm4+QJ4GTcoDGa7hLY2gZya1OmSE+hwsmGs5YLGLlykj5GXidiCyTAsbt5+YN28ecM9y29x9z4Dd4XYon/3bHVMy9vXXhdlIf4D/o5dnZrgZT/hl89rI6Tbe2rglZ9n6fygD+7l0mplbZdmCKOXkTyM9tfJkZmTlXyU0yK5AAZ4HJK2CkuZOAN43cD2nAJ0ZjboX78YyIRRIpxDcmvIH0xWb6Lq5jSZjTlpHJzV9UaZyvaDXkNG8zClsWziy/eOXl8xadYT4vrC5rpHYUZkgNMsCXipPNNbxjZbwEWUAeJSmkYHCVk95bZXqZ3MAcopr5apdr+p0+UhHo+6tq8jfONCLK4DLwJqrUNjGTitSYRyjOIwWEGHopAZda7iaDi3rgC2L66nkOG55HRa43w6lUxdzhWFEwy7y/wAY6qAF4Z4neU9kFXIzXv591javkcTBPP6eu/SyonhXuFveOf13ualdmS+Pn6nuJHn4uT1HuzRsFPc/Nyl6q/o86OB30jP9Wi4cDEFi7U0rK97Il6al7GrwWySsTKmbLzep1G4mvr86ja1GOpeNKgsOcipT7h4kfBUgFiT3FthSYNBr0Szwv7u3s7c11SoCFq/zsVeDjr8GVRREV2XAkf9+ViVfKzgDOZKOP31k=

content-type;host;x-amz-date;x-amz-security-token
ee78c02194ff3cc3bd9c929527e0147a82e93c63b51599be0655169209bb8571
2025-04-29 01:30:12,495 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
9acabab236dfa4cb405dd509becfec36dcb17b2eec32664d84660e94e4775d70
2025-04-29 01:30:12,495 [DEBUG] Signature:
f778b9bc13012f9f59ccd7eac28cdcffb7f6a0d3414e2eecd52a1892f98a23fd
2025-04-29 01:30:12,495 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F270A55A00>>
2025-04-29 01:30:12,495 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,496 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDHHV2ejAcKApNK9hty/FvomHITLjvlEQomgmC2dLN5VAiBrSKzJeljtoFsHhirpdMClHqlJUMnDCjaB0x/dxNZ2cCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMtq04W9FSdLlq2IHGKswCsL67+rztuSEvGGhhOxLV7X0Bb5kIz+csm4+QJ4GTcoDGa7hLY2gZya1OmSE+hwsmGs5YLGLlykj5GXidiCyTAsbt5+YN28ecM9y29x9z4Dd4XYon/3bHVMy9vXXhdlIf4D/o5dnZrgZT/hl89rI6Tbe2rglZ9n6fygD+7l0mplbZdmCKOXkTyM9tfJkZmTlXyU0yK5AAZ4HJK2CkuZOAN43cD2nAJ0ZjboX78YyIRRIpxDcmvIH0xWb6Lq5jSZjTlpHJzV9UaZyvaDXkNG8zClsWziy/eOXl8xadYT4vrC5rpHYUZkgNMsCXipPNNbxjZbwEWUAeJSmkYHCVk95bZXqZ3MAcopr5apdr+p0+UhHo+6tq8jfONCLK4DLwJqrUNjGTitSYRyjOIwWEGHopAZda7iaDi3rgC2L66nkOG55HRa43w6lUxdzhWFEwy7y/wAY6qAF4Z4neU9kFXIzXv591javkcTBPP6eu/SyonhXuFveOf13ualdmS+Pn6nuJHn4uT1HuzRsFPc/Nyl6q/o86OB30jP9Wi4cDEFi7U0rK97Il6al7GrwWySsTKmbLzep1G4mvr86ja1GOpeNKgsOcipT7h4kfBUgFiT3FthSYNBr0Szwv7u3s7c11SoCFq/zsVeDjr8GVRREV2XAkf9+ViVfKzgDOZKOP31k=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=f778b9bc13012f9f59ccd7eac28cdcffb7f6a0d3414e2eecd52a1892f98a23fd', 'amz-sdk-invocation-id': b'2c353f65-095c-4750-b4b1-0a9686c6393a', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,496 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,496 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,505 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '807da58d-abb2-41bb-ad04-b981cdbc95bc', 'Server': 'AWS SSO', 'x-amzn-RequestId': '807da58d-abb2-41bb-ad04-b981cdbc95bc'}
2025-04-29 01:30:12,505 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"6SYdD3lpCCQsLhisQYLSt4pJwlBtjj+P7EdgPern","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDzfpJKg/spSpAhzsztRzD4rl0A2zjR8Hh3k07/LwQFtwIhAJqGcFrkbMpHf7ql/ERwhoKUpYNKETa1aJ1NwBvPwM55Ku8CCH0QABoMMDQ2MzE5MTg0NjA4IgzvwSt3ZQNZGUSiyq0qzAIl0RT1QTjMwY+WLgfdGMQLZGXe3H+yT2aRmo2cDqidOrW19iJWwKUKA3Hcx3xhB0fjntCgirLhWFMRkb0kThx7ReT66OW3+GWD09c6bxnxXp+Z7BpLQDL6MRM/c7VQbv5VkAsoor8aimcQ2XBf19puN2NZ/HUKqL8DXhWxWBFRKmw2Q7bayzAInG5AokeeZAheV1z7vWQCAJZ8zu0L/KMCg9RAIrMoeO60BlHfxL5YhKTk/diC6qqjgYDogpZfC3LAtyKr5gNXYIrUrE0vMOQMruncX72I+wpM+rw47IK3dgbWbTu3mhXK7DgXy9yqMdLyfv10l/al2QytEnIX5gPPEs0JNIKl6f0OIAjJ8aebTQ5f+35+gi1xNhUTeUWObBXNOMS8SRax4iOU42fFcYzIE+2/lESZfY8TIVRqKwzISAVN3hKztefoXlA9sDDLvL/ABjqmAbxQY06dwbytW1XiKmYVtQ64XrTPZQ7qymFYcBNvDtMmSw8TeY5SYYTI9o3+N89GChHD7OIsGYaHrLCedbQ+4Kg90iilwLgP9DL9aETByT6RMrCylAIrC02+TtYNy4+51L8296nKRM6pXohuKVUxiJ2cjMP8RePwwz+vBAAf9UqoPTQ5jGcm39L4twVLipHv6C5KkUt5Ovjfa40k7FQkCW9iXwOtzHg=","expiration":1745874010000}}'
2025-04-29 01:30:12,505 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D168620>
2025-04-29 01:30:12,505 [DEBUG] No retry needed.
2025-04-29 01:30:12,508 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,508 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,508 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDzfpJKg/spSpAhzsztRzD4rl0A2zjR8Hh3k07/LwQFtwIhAJqGcFrkbMpHf7ql/ERwhoKUpYNKETa1aJ1NwBvPwM55Ku8CCH0QABoMMDQ2MzE5MTg0NjA4IgzvwSt3ZQNZGUSiyq0qzAIl0RT1QTjMwY+WLgfdGMQLZGXe3H+yT2aRmo2cDqidOrW19iJWwKUKA3Hcx3xhB0fjntCgirLhWFMRkb0kThx7ReT66OW3+GWD09c6bxnxXp+Z7BpLQDL6MRM/c7VQbv5VkAsoor8aimcQ2XBf19puN2NZ/HUKqL8DXhWxWBFRKmw2Q7bayzAInG5AokeeZAheV1z7vWQCAJZ8zu0L/KMCg9RAIrMoeO60BlHfxL5YhKTk/diC6qqjgYDogpZfC3LAtyKr5gNXYIrUrE0vMOQMruncX72I+wpM+rw47IK3dgbWbTu3mhXK7DgXy9yqMdLyfv10l/al2QytEnIX5gPPEs0JNIKl6f0OIAjJ8aebTQ5f+35+gi1xNhUTeUWObBXNOMS8SRax4iOU42fFcYzIE+2/lESZfY8TIVRqKwzISAVN3hKztefoXlA9sDDLvL/ABjqmAbxQY06dwbytW1XiKmYVtQ64XrTPZQ7qymFYcBNvDtMmSw8TeY5SYYTI9o3+N89GChHD7OIsGYaHrLCedbQ+4Kg90iilwLgP9DL9aETByT6RMrCylAIrC02+TtYNy4+51L8296nKRM6pXohuKVUxiJ2cjMP8RePwwz+vBAAf9UqoPTQ5jGcm39L4twVLipHv6C5KkUt5Ovjfa40k7FQkCW9iXwOtzHg=

content-type;host;x-amz-date;x-amz-security-token
beb235faf3a3245147f28d0f80f92645c7477e64a7c9fe174231c31392662190
2025-04-29 01:30:12,508 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
b117a680c42083a63990524d60eb3ce387ffec7babe0226dcdcfcf6cf4dcfd1b
2025-04-29 01:30:12,508 [DEBUG] Signature:
8b9e967e88034536b26126d0e7cee8bb625661b41b66acc9750df784135aa5e9
2025-04-29 01:30:12,508 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27CB52BD0>>
2025-04-29 01:30:12,508 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,508 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDzfpJKg/spSpAhzsztRzD4rl0A2zjR8Hh3k07/LwQFtwIhAJqGcFrkbMpHf7ql/ERwhoKUpYNKETa1aJ1NwBvPwM55Ku8CCH0QABoMMDQ2MzE5MTg0NjA4IgzvwSt3ZQNZGUSiyq0qzAIl0RT1QTjMwY+WLgfdGMQLZGXe3H+yT2aRmo2cDqidOrW19iJWwKUKA3Hcx3xhB0fjntCgirLhWFMRkb0kThx7ReT66OW3+GWD09c6bxnxXp+Z7BpLQDL6MRM/c7VQbv5VkAsoor8aimcQ2XBf19puN2NZ/HUKqL8DXhWxWBFRKmw2Q7bayzAInG5AokeeZAheV1z7vWQCAJZ8zu0L/KMCg9RAIrMoeO60BlHfxL5YhKTk/diC6qqjgYDogpZfC3LAtyKr5gNXYIrUrE0vMOQMruncX72I+wpM+rw47IK3dgbWbTu3mhXK7DgXy9yqMdLyfv10l/al2QytEnIX5gPPEs0JNIKl6f0OIAjJ8aebTQ5f+35+gi1xNhUTeUWObBXNOMS8SRax4iOU42fFcYzIE+2/lESZfY8TIVRqKwzISAVN3hKztefoXlA9sDDLvL/ABjqmAbxQY06dwbytW1XiKmYVtQ64XrTPZQ7qymFYcBNvDtMmSw8TeY5SYYTI9o3+N89GChHD7OIsGYaHrLCedbQ+4Kg90iilwLgP9DL9aETByT6RMrCylAIrC02+TtYNy4+51L8296nKRM6pXohuKVUxiJ2cjMP8RePwwz+vBAAf9UqoPTQ5jGcm39L4twVLipHv6C5KkUt5Ovjfa40k7FQkCW9iXwOtzHg=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=8b9e967e88034536b26126d0e7cee8bb625661b41b66acc9750df784135aa5e9', 'amz-sdk-invocation-id': b'4e33fdb4-61ee-4735-9518-f54a89cb0738', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,511 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,547 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,548 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': 'caf7e1c9-bd36-490b-bd83-06a74d62890e', 'Server': 'AWS SSO', 'x-amzn-RequestId': 'caf7e1c9-bd36-490b-bd83-06a74d62890e'}
2025-04-29 01:30:12,548 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"u8ll1pKtzCq/bs1QPmQdjNS6jFkyTTgra8pEozfi","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDhtG7qjzx0ciuuBBsE5HPiwmU2Ec/aNTp58hT9fM7fYwIgHDsDX2iQQZuMg4KoBEIvegid2tVn9ARY5bKjC9FS2MYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDCQoZBlGjavM5yt5ayrMAgHiLF/78iz92XTsJSbcka5x0KgXTj/LuhsrwQrmIQh5CfxYyOtR0B3OWJxyT8KLzJH/xNIAZwX5R1XWUok5WnpCteJaxEXdUMBss3WzCd1ZCE5V5cXaJdD/OrnR3/I9I7tYyQbWGgNSZU2aFSlSBG9WCq3FRGaPYZcPEJc7gPEXQoQLmQY04BEVrDv4Zal5/LuE2jEGXZ76N6LEKW+UK7M9zlZ+flT5hMdPt2OxFPqaFB/BoqWD/sCfM43PlmucaCKEBVvgWD0zZvZBEkkl/9UfNWRCx4ny4pplTA8b/1MQ+/COYENkI7bSukWlG9hYZaXQXz57XxXWiwdrVRn0J1FKr5LcML+H9r89NKlikXM6Hlug/V941L/Uj1EZly0xKpY0fW+ekctNWl0G5BtuPGO9xb13J+stg+KnrCJq11+6/2znJzBaxnEW3cxUMMu8v8AGOqcBPVpg5C1jn4OnfWPd6ve7ZO+HB3rdsImHODb4iNcugV8MBG1+Na8AFQbxQKv1EJ7YSPi480eQloauu/dVTIe41lYkhnsStgL7rOeFf3NvkDFdCPsYbBCCPYPx32Pe/BmKzjha7Pke5Sv4bh4woMciwfFWan9cZ8n/51tOH/SW1gJysV5mYZ0N/REuQTBGL+Fv94QApOt2UFHl9Rrwf9BOkq5SL710wzY=","expiration":1745874010000}}'
2025-04-29 01:30:12,548 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D2CCEF0>
2025-04-29 01:30:12,548 [DEBUG] No retry needed.
2025-04-29 01:30:12,550 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,550 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,550 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDhtG7qjzx0ciuuBBsE5HPiwmU2Ec/aNTp58hT9fM7fYwIgHDsDX2iQQZuMg4KoBEIvegid2tVn9ARY5bKjC9FS2MYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDCQoZBlGjavM5yt5ayrMAgHiLF/78iz92XTsJSbcka5x0KgXTj/LuhsrwQrmIQh5CfxYyOtR0B3OWJxyT8KLzJH/xNIAZwX5R1XWUok5WnpCteJaxEXdUMBss3WzCd1ZCE5V5cXaJdD/OrnR3/I9I7tYyQbWGgNSZU2aFSlSBG9WCq3FRGaPYZcPEJc7gPEXQoQLmQY04BEVrDv4Zal5/LuE2jEGXZ76N6LEKW+UK7M9zlZ+flT5hMdPt2OxFPqaFB/BoqWD/sCfM43PlmucaCKEBVvgWD0zZvZBEkkl/9UfNWRCx4ny4pplTA8b/1MQ+/COYENkI7bSukWlG9hYZaXQXz57XxXWiwdrVRn0J1FKr5LcML+H9r89NKlikXM6Hlug/V941L/Uj1EZly0xKpY0fW+ekctNWl0G5BtuPGO9xb13J+stg+KnrCJq11+6/2znJzBaxnEW3cxUMMu8v8AGOqcBPVpg5C1jn4OnfWPd6ve7ZO+HB3rdsImHODb4iNcugV8MBG1+Na8AFQbxQKv1EJ7YSPi480eQloauu/dVTIe41lYkhnsStgL7rOeFf3NvkDFdCPsYbBCCPYPx32Pe/BmKzjha7Pke5Sv4bh4woMciwfFWan9cZ8n/51tOH/SW1gJysV5mYZ0N/REuQTBGL+Fv94QApOt2UFHl9Rrwf9BOkq5SL710wzY=

content-type;host;x-amz-date;x-amz-security-token
8167f3ec6e6fbe123aca56bc10ad00a5460e5e3afacbea4d99312e7377a5c2e9
2025-04-29 01:30:12,550 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
c9490a4c22c6aa06c2c25c134a6351a68080aad0c6553c1b937dd5337303a350
2025-04-29 01:30:12,550 [DEBUG] Signature:
7b5582ce8e545a6d6c63257c500d3dc8cbff626ff99fbda73e04289d9f8dc3c1
2025-04-29 01:30:12,550 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D09A2D0>>
2025-04-29 01:30:12,550 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,550 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDhtG7qjzx0ciuuBBsE5HPiwmU2Ec/aNTp58hT9fM7fYwIgHDsDX2iQQZuMg4KoBEIvegid2tVn9ARY5bKjC9FS2MYq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDCQoZBlGjavM5yt5ayrMAgHiLF/78iz92XTsJSbcka5x0KgXTj/LuhsrwQrmIQh5CfxYyOtR0B3OWJxyT8KLzJH/xNIAZwX5R1XWUok5WnpCteJaxEXdUMBss3WzCd1ZCE5V5cXaJdD/OrnR3/I9I7tYyQbWGgNSZU2aFSlSBG9WCq3FRGaPYZcPEJc7gPEXQoQLmQY04BEVrDv4Zal5/LuE2jEGXZ76N6LEKW+UK7M9zlZ+flT5hMdPt2OxFPqaFB/BoqWD/sCfM43PlmucaCKEBVvgWD0zZvZBEkkl/9UfNWRCx4ny4pplTA8b/1MQ+/COYENkI7bSukWlG9hYZaXQXz57XxXWiwdrVRn0J1FKr5LcML+H9r89NKlikXM6Hlug/V941L/Uj1EZly0xKpY0fW+ekctNWl0G5BtuPGO9xb13J+stg+KnrCJq11+6/2znJzBaxnEW3cxUMMu8v8AGOqcBPVpg5C1jn4OnfWPd6ve7ZO+HB3rdsImHODb4iNcugV8MBG1+Na8AFQbxQKv1EJ7YSPi480eQloauu/dVTIe41lYkhnsStgL7rOeFf3NvkDFdCPsYbBCCPYPx32Pe/BmKzjha7Pke5Sv4bh4woMciwfFWan9cZ8n/51tOH/SW1gJysV5mYZ0N/REuQTBGL+Fv94QApOt2UFHl9Rrwf9BOkq5SL710wzY=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=7b5582ce8e545a6d6c63257c500d3dc8cbff626ff99fbda73e04289d9f8dc3c1', 'amz-sdk-invocation-id': b'51db4add-b777-4122-a5bd-003db20c560d', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,550 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,608 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,608 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,615 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '4b05ae9e-bb5c-42d9-a02c-5636e9b5f80b', 'Server': 'AWS SSO', 'x-amzn-RequestId': '4b05ae9e-bb5c-42d9-a02c-5636e9b5f80b'}
2025-04-29 01:30:12,615 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '0bbd1d9f-089c-4153-8f33-f5bb8ee6b126', 'Server': 'AWS SSO', 'x-amzn-RequestId': '0bbd1d9f-089c-4153-8f33-f5bb8ee6b126'}
2025-04-29 01:30:12,615 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"vSVL77vB/7xcTUo7UQuUfhYyT7NYCB6J92UQ47l4","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDwggfWWFfoMdpoNoMUhyRnNtYJJVnNXOIs7VU5CuFikwIhAJURMfdRGfNyKp3ajPDD++JJFyFIi/s+rOWNuFIEE4ejKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyS6H3OzNErf3rWffIqzAJ35AsdWVEH0n+PyvhCwSWMqoFFKN31CjFXJYDPzkyDZ/LRAtblJ20r2vT6rXwHnas/dPlKhU5RWzodvXLc922V+9zx2YvtsZ2YMd4ypXRVIvMxjI2IxVTy/bqqweB0Syi8oo1yGaohE2WSafXbX9N7XFUjLNMBbhINfrUmrtzjK9jjmJPewVNYZDz/+EJQRgcl5OgGMG+IGKq+hnwJSavdtKF47I155uw6FtL8xl1zgexYCUZnXZRMB8yvL+LmKDvEmWinMw51txDk9FQACw9gsRZccyg5rizXlasTaTDJJoGIsWRVE6uvQ+oBZ0s5Q6NXgHXSnYUQIhuuvZGTpnPU6xBfQmOdvlxE7sOp1m8DjjFBGkxFjde6gihz1ZHggq/pGRvbZVa6qsG5jjFgS9Y3U6L5iq6VhT7YUMpIipHco/Pu01ExEXv/HUpvDDDLvL/ABjqmAS5V3rPrHOcWNTuScosKMiKSifrLAsUO3Fwyqm45vWOPj7d++fSK7tteNFhEFIB+HxsbyhL+Y5V1L9oK+LsI9Gn8dcjEOnwrwMEz/H+ftVKxLvPkxrBHpNf/ilWW0/w7dcqd4+qiDEmN16em1pOKkMhYNWmrLwbD+Mhsck1ObuG/VhLuDd0KUnFmmpjIuzu7Ow8wUSVWfNiNm0GtjPrDDDnKZkXVCZI=","expiration":1745874010000}}'
2025-04-29 01:30:12,615 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"i14jvsZIX6M1xjaXrrerKL1d00/f4rN54P9I21nZ","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDh2y+GQ+48oWen/xpqmwWVmJ3EpPlcWzgQwjqjqwfxCQIhAJyfU25bCwIQio6eKM+dSsPbEl43xxrcM6xHxDUb1j/CKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyIGVDrexHu1H5LQRoqzAJlXwXKCs+tAD5K1tX9Jl0gx/xyQlZ3PniW41U23cSc8OI8sF1GClNjfyo0mrz2SASvfPwPahkNLAvoQp2jLkie9lzNGlmhhukmI+wNpnrCKLK5iVqprI2QqR3VxXCoeafz0zgYTXaO2fo0Qjeueu9DPFqwHnf1rRKwMmdpULRB6HfSuXgdi09jScfiDIsqF86pJH+WhewHvU3mVEUm4k6nqcCbTuIYHDA3ctugDqkLhc6x4PIhbL7+tet7DYqIkZImuJr7cBqhE1pkPltfWZTqVUoP6kOksGMu/1i4YhpEkeJl98ecnxHLUkExm4jGzGrSoyUyeOCbE9uhT98e8WRF+Q0MGZuCZChy5H5J4YWfKYEfF9yiJV/knmg3fkNO0h60ZqGS3+rWtlDRpAxusebU1ix33e21KP5Pdr1zFOUAFgKXsWB6Jn2no/FR+DDLvL/ABjqmAY6PCJ29rqZwcompHesVGnWIYKG3sDWOktgdgeBm86Ir6jZJ2LjW8WazAWHvg2pqHB1cHtidlXNIKVpL9zleLzkMP6FgmPq7u75D8Iy8LCLdOsyfynMs4tUDMzevv5BHajxhqXbqXdgjiPRy5Egg5TT+3HwHeh4Abe+ozzkyCsLNycLgoxYK9n0e597d2WGOII5XHMx/7LdJySGM0Ec0f3HK9LDz6GQ=","expiration":1745874010000}}'
2025-04-29 01:30:12,615 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D273BC0>
2025-04-29 01:30:12,615 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D22DA90>
2025-04-29 01:30:12,615 [DEBUG] No retry needed.
2025-04-29 01:30:12,615 [DEBUG] No retry needed.
2025-04-29 01:30:12,620 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,620 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,620 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,620 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDwggfWWFfoMdpoNoMUhyRnNtYJJVnNXOIs7VU5CuFikwIhAJURMfdRGfNyKp3ajPDD++JJFyFIi/s+rOWNuFIEE4ejKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyS6H3OzNErf3rWffIqzAJ35AsdWVEH0n+PyvhCwSWMqoFFKN31CjFXJYDPzkyDZ/LRAtblJ20r2vT6rXwHnas/dPlKhU5RWzodvXLc922V+9zx2YvtsZ2YMd4ypXRVIvMxjI2IxVTy/bqqweB0Syi8oo1yGaohE2WSafXbX9N7XFUjLNMBbhINfrUmrtzjK9jjmJPewVNYZDz/+EJQRgcl5OgGMG+IGKq+hnwJSavdtKF47I155uw6FtL8xl1zgexYCUZnXZRMB8yvL+LmKDvEmWinMw51txDk9FQACw9gsRZccyg5rizXlasTaTDJJoGIsWRVE6uvQ+oBZ0s5Q6NXgHXSnYUQIhuuvZGTpnPU6xBfQmOdvlxE7sOp1m8DjjFBGkxFjde6gihz1ZHggq/pGRvbZVa6qsG5jjFgS9Y3U6L5iq6VhT7YUMpIipHco/Pu01ExEXv/HUpvDDDLvL/ABjqmAS5V3rPrHOcWNTuScosKMiKSifrLAsUO3Fwyqm45vWOPj7d++fSK7tteNFhEFIB+HxsbyhL+Y5V1L9oK+LsI9Gn8dcjEOnwrwMEz/H+ftVKxLvPkxrBHpNf/ilWW0/w7dcqd4+qiDEmN16em1pOKkMhYNWmrLwbD+Mhsck1ObuG/VhLuDd0KUnFmmpjIuzu7Ow8wUSVWfNiNm0GtjPrDDDnKZkXVCZI=

content-type;host;x-amz-date;x-amz-security-token
1bb7c590e0f97e20868e8f9497a53b80ccf8e3f290e72fe5b6a738312820da5c
2025-04-29 01:30:12,620 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
3d31fd3abbf346dfb6e323c5069c6092fe8382c47ed4ccba450009ad2f6a9d84
2025-04-29 01:30:12,620 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,620 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,620 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDh2y+GQ+48oWen/xpqmwWVmJ3EpPlcWzgQwjqjqwfxCQIhAJyfU25bCwIQio6eKM+dSsPbEl43xxrcM6xHxDUb1j/CKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyIGVDrexHu1H5LQRoqzAJlXwXKCs+tAD5K1tX9Jl0gx/xyQlZ3PniW41U23cSc8OI8sF1GClNjfyo0mrz2SASvfPwPahkNLAvoQp2jLkie9lzNGlmhhukmI+wNpnrCKLK5iVqprI2QqR3VxXCoeafz0zgYTXaO2fo0Qjeueu9DPFqwHnf1rRKwMmdpULRB6HfSuXgdi09jScfiDIsqF86pJH+WhewHvU3mVEUm4k6nqcCbTuIYHDA3ctugDqkLhc6x4PIhbL7+tet7DYqIkZImuJr7cBqhE1pkPltfWZTqVUoP6kOksGMu/1i4YhpEkeJl98ecnxHLUkExm4jGzGrSoyUyeOCbE9uhT98e8WRF+Q0MGZuCZChy5H5J4YWfKYEfF9yiJV/knmg3fkNO0h60ZqGS3+rWtlDRpAxusebU1ix33e21KP5Pdr1zFOUAFgKXsWB6Jn2no/FR+DDLvL/ABjqmAY6PCJ29rqZwcompHesVGnWIYKG3sDWOktgdgeBm86Ir6jZJ2LjW8WazAWHvg2pqHB1cHtidlXNIKVpL9zleLzkMP6FgmPq7u75D8Iy8LCLdOsyfynMs4tUDMzevv5BHajxhqXbqXdgjiPRy5Egg5TT+3HwHeh4Abe+ozzkyCsLNycLgoxYK9n0e597d2WGOII5XHMx/7LdJySGM0Ec0f3HK9LDz6GQ=

content-type;host;x-amz-date;x-amz-security-token
c4927ac64478f62c7c8c6e4445a4c339b48ce731663844c0f73bde7c7c1024e0
2025-04-29 01:30:12,620 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
07033ea723c7634ea5fe8919bc9702c441f8f02a772c7e34f872e69c55ce5da5
2025-04-29 01:30:12,620 [DEBUG] Signature:
d56fb0490814c7099e6e1e1b5591da539c838466c4b0cf67a424e81101163811
2025-04-29 01:30:12,620 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': 'e35e8fb6-dc78-4bf9-bf8c-58a3f5eebf7b', 'Server': 'AWS SSO', 'x-amzn-RequestId': 'e35e8fb6-dc78-4bf9-bf8c-58a3f5eebf7b'}
2025-04-29 01:30:12,620 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"Ae1BMRqAAiokxVLJz1on2B/m6tpm/YeA63b6ONS2","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIBXqMJpUTHADp5npfHgQKbFORSUI0HqNIhigDKK8gCIDAiEAtPqbYnFUPZu715WDJ3LPqYwSO0u3FW0HLhx1KpwKT78q7wIIfRAAGgwwNDYzMTkxODQ2MDgiDAJW1qmW6P0IwsllrCrMAgBDPwgEVFG3BMVjQodb21gtL1BhxhFQKvDAp6C/f2CEUXzzbKLuMGravO9Vbj9sI6+Q+efqI+NIjoDCw2wIzmL/vBda0aC8JLgO+2Gwj/45Jm2vJUSnxDKmKuSN1dwI72YA+uchS7y0MnBXxVHQyL/Lr45OW3/ha40sLFgtNhRXz2YZHNounIjZMoVIAX89m44pg4hdu0QSRHDyNgknCSBRf1XEITDxJYaK1qZMCY3ubwvxIO/8+mlQ00jM9eVsJIDdUz0SPoPT3In+R0ygivKIN3V+K+eauvCCEZ1rEJE5by91/vicX5TjYv2v3NN7MXpn8awU6iYYNIvCvkJELpe/erc3g1WgUZqZi/PB3RnaCAizFC+hnAJpwmWvItUw10bUTydU5Oelm1FPUo65qJmM/C2aHykFQjOS/Yw93yniJaNreGaBPVd6W3v0MMu8v8AGOqcBDg4QbU02BCYpwQu5BeDI2QYzjkUXqr20PrK3DvzyHuRNpBzx5IqVoCL9/2Ju05fzLb2dA6k84EdDMPx4h7XZljatkm19YmX96S45JO5RLxyGgxpx5xVrwGQ8kXfFrWCgN+Yd5XHHygicKUCxKt/HeC/HPiMxg1RtUucq1r++HZyb2vAnJ2brifL2rdfvvY2K9pk55n60NeGjACYd/g8UsnL5U+wzrb8=","expiration":1745874010000}}'
2025-04-29 01:30:12,620 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D098AD0>>
2025-04-29 01:30:12,620 [DEBUG] Signature:
efc1feea8885fb5c1eb0852078008f4fb043263f65075f378682bdb02541d785
2025-04-29 01:30:12,620 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D2CF560>
2025-04-29 01:30:12,625 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,625 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F270703680>>
2025-04-29 01:30:12,625 [DEBUG] No retry needed.
2025-04-29 01:30:12,625 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDwggfWWFfoMdpoNoMUhyRnNtYJJVnNXOIs7VU5CuFikwIhAJURMfdRGfNyKp3ajPDD++JJFyFIi/s+rOWNuFIEE4ejKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyS6H3OzNErf3rWffIqzAJ35AsdWVEH0n+PyvhCwSWMqoFFKN31CjFXJYDPzkyDZ/LRAtblJ20r2vT6rXwHnas/dPlKhU5RWzodvXLc922V+9zx2YvtsZ2YMd4ypXRVIvMxjI2IxVTy/bqqweB0Syi8oo1yGaohE2WSafXbX9N7XFUjLNMBbhINfrUmrtzjK9jjmJPewVNYZDz/+EJQRgcl5OgGMG+IGKq+hnwJSavdtKF47I155uw6FtL8xl1zgexYCUZnXZRMB8yvL+LmKDvEmWinMw51txDk9FQACw9gsRZccyg5rizXlasTaTDJJoGIsWRVE6uvQ+oBZ0s5Q6NXgHXSnYUQIhuuvZGTpnPU6xBfQmOdvlxE7sOp1m8DjjFBGkxFjde6gihz1ZHggq/pGRvbZVa6qsG5jjFgS9Y3U6L5iq6VhT7YUMpIipHco/Pu01ExEXv/HUpvDDDLvL/ABjqmAS5V3rPrHOcWNTuScosKMiKSifrLAsUO3Fwyqm45vWOPj7d++fSK7tteNFhEFIB+HxsbyhL+Y5V1L9oK+LsI9Gn8dcjEOnwrwMEz/H+ftVKxLvPkxrBHpNf/ilWW0/w7dcqd4+qiDEmN16em1pOKkMhYNWmrLwbD+Mhsck1ObuG/VhLuDd0KUnFmmpjIuzu7Ow8wUSVWfNiNm0GtjPrDDDnKZkXVCZI=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=d56fb0490814c7099e6e1e1b5591da539c838466c4b0cf67a424e81101163811', 'amz-sdk-invocation-id': b'9f9e72ca-f7a5-480c-858a-c8de95833ab2', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,625 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,625 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,625 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJIMEYCIQDh2y+GQ+48oWen/xpqmwWVmJ3EpPlcWzgQwjqjqwfxCQIhAJyfU25bCwIQio6eKM+dSsPbEl43xxrcM6xHxDUb1j/CKu8CCH0QABoMMDQ2MzE5MTg0NjA4IgyIGVDrexHu1H5LQRoqzAJlXwXKCs+tAD5K1tX9Jl0gx/xyQlZ3PniW41U23cSc8OI8sF1GClNjfyo0mrz2SASvfPwPahkNLAvoQp2jLkie9lzNGlmhhukmI+wNpnrCKLK5iVqprI2QqR3VxXCoeafz0zgYTXaO2fo0Qjeueu9DPFqwHnf1rRKwMmdpULRB6HfSuXgdi09jScfiDIsqF86pJH+WhewHvU3mVEUm4k6nqcCbTuIYHDA3ctugDqkLhc6x4PIhbL7+tet7DYqIkZImuJr7cBqhE1pkPltfWZTqVUoP6kOksGMu/1i4YhpEkeJl98ecnxHLUkExm4jGzGrSoyUyeOCbE9uhT98e8WRF+Q0MGZuCZChy5H5J4YWfKYEfF9yiJV/knmg3fkNO0h60ZqGS3+rWtlDRpAxusebU1ix33e21KP5Pdr1zFOUAFgKXsWB6Jn2no/FR+DDLvL/ABjqmAY6PCJ29rqZwcompHesVGnWIYKG3sDWOktgdgeBm86Ir6jZJ2LjW8WazAWHvg2pqHB1cHtidlXNIKVpL9zleLzkMP6FgmPq7u75D8Iy8LCLdOsyfynMs4tUDMzevv5BHajxhqXbqXdgjiPRy5Egg5TT+3HwHeh4Abe+ozzkyCsLNycLgoxYK9n0e597d2WGOII5XHMx/7LdJySGM0Ec0f3HK9LDz6GQ=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=efc1feea8885fb5c1eb0852078008f4fb043263f65075f378682bdb02541d785', 'amz-sdk-invocation-id': b'ef7f92eb-2c84-4958-bfbb-0aa8bfdf12b6', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,628 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,628 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,628 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIBXqMJpUTHADp5npfHgQKbFORSUI0HqNIhigDKK8gCIDAiEAtPqbYnFUPZu715WDJ3LPqYwSO0u3FW0HLhx1KpwKT78q7wIIfRAAGgwwNDYzMTkxODQ2MDgiDAJW1qmW6P0IwsllrCrMAgBDPwgEVFG3BMVjQodb21gtL1BhxhFQKvDAp6C/f2CEUXzzbKLuMGravO9Vbj9sI6+Q+efqI+NIjoDCw2wIzmL/vBda0aC8JLgO+2Gwj/45Jm2vJUSnxDKmKuSN1dwI72YA+uchS7y0MnBXxVHQyL/Lr45OW3/ha40sLFgtNhRXz2YZHNounIjZMoVIAX89m44pg4hdu0QSRHDyNgknCSBRf1XEITDxJYaK1qZMCY3ubwvxIO/8+mlQ00jM9eVsJIDdUz0SPoPT3In+R0ygivKIN3V+K+eauvCCEZ1rEJE5by91/vicX5TjYv2v3NN7MXpn8awU6iYYNIvCvkJELpe/erc3g1WgUZqZi/PB3RnaCAizFC+hnAJpwmWvItUw10bUTydU5Oelm1FPUo65qJmM/C2aHykFQjOS/Yw93yniJaNreGaBPVd6W3v0MMu8v8AGOqcBDg4QbU02BCYpwQu5BeDI2QYzjkUXqr20PrK3DvzyHuRNpBzx5IqVoCL9/2Ju05fzLb2dA6k84EdDMPx4h7XZljatkm19YmX96S45JO5RLxyGgxpx5xVrwGQ8kXfFrWCgN+Yd5XHHygicKUCxKt/HeC/HPiMxg1RtUucq1r++HZyb2vAnJ2brifL2rdfvvY2K9pk55n60NeGjACYd/g8UsnL5U+wzrb8=

content-type;host;x-amz-date;x-amz-security-token
0b8b2bc983b8d603da475d940e1c7a3b3459f4f27c40b6fc9a03f0c086178f9e
2025-04-29 01:30:12,628 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
7312abd329ea9dc375df65d747101efa55fca13f1a8e69390609f8262f267408
2025-04-29 01:30:12,628 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,628 [DEBUG] Signature:
25b2cba5f6e732a24de477215479893a252734af1cdaed9af464c410bc20205d
2025-04-29 01:30:12,628 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D118620>>
2025-04-29 01:30:12,628 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,628 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIBXqMJpUTHADp5npfHgQKbFORSUI0HqNIhigDKK8gCIDAiEAtPqbYnFUPZu715WDJ3LPqYwSO0u3FW0HLhx1KpwKT78q7wIIfRAAGgwwNDYzMTkxODQ2MDgiDAJW1qmW6P0IwsllrCrMAgBDPwgEVFG3BMVjQodb21gtL1BhxhFQKvDAp6C/f2CEUXzzbKLuMGravO9Vbj9sI6+Q+efqI+NIjoDCw2wIzmL/vBda0aC8JLgO+2Gwj/45Jm2vJUSnxDKmKuSN1dwI72YA+uchS7y0MnBXxVHQyL/Lr45OW3/ha40sLFgtNhRXz2YZHNounIjZMoVIAX89m44pg4hdu0QSRHDyNgknCSBRf1XEITDxJYaK1qZMCY3ubwvxIO/8+mlQ00jM9eVsJIDdUz0SPoPT3In+R0ygivKIN3V+K+eauvCCEZ1rEJE5by91/vicX5TjYv2v3NN7MXpn8awU6iYYNIvCvkJELpe/erc3g1WgUZqZi/PB3RnaCAizFC+hnAJpwmWvItUw10bUTydU5Oelm1FPUo65qJmM/C2aHykFQjOS/Yw93yniJaNreGaBPVd6W3v0MMu8v8AGOqcBDg4QbU02BCYpwQu5BeDI2QYzjkUXqr20PrK3DvzyHuRNpBzx5IqVoCL9/2Ju05fzLb2dA6k84EdDMPx4h7XZljatkm19YmX96S45JO5RLxyGgxpx5xVrwGQ8kXfFrWCgN+Yd5XHHygicKUCxKt/HeC/HPiMxg1RtUucq1r++HZyb2vAnJ2brifL2rdfvvY2K9pk55n60NeGjACYd/g8UsnL5U+wzrb8=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=25b2cba5f6e732a24de477215479893a252734af1cdaed9af464c410bc20205d', 'amz-sdk-invocation-id': b'4441d4a2-197a-4ffb-bafd-207aeba772df', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,628 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,638 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,638 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:11 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': 'b2f3c924-8643-49df-864f-e387237d3518', 'Server': 'AWS SSO', 'x-amzn-RequestId': 'b2f3c924-8643-49df-864f-e387237d3518'}
2025-04-29 01:30:12,638 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"yh3D2nBWNZoHl3RjCp9OZgrNiBaZAdjQvoN+8KT5","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDEJtaccHW7XOLxn9l/W9kT4oV5XAR32EiB5FmgEmy5KAiBX9ThA5Y/pC3xNRhTdSbqXzgXGJY5D56TDwnDIUHsiHCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMzn5AL15cdEb+B5XUKswCXHLgfaM7qpSwGQTSKd6O3/3tZC82nDvi/51s8IpX2+1DE9v81LaT32eHq0gQQmtUPULcw0PWLwNVuWZkvc6me0+mmk23TuzRJDvzEG5gzP3L46cYeSgDBMFmO1Wc1oHpgl26Ubf4QLBAZnTrpNPd7QiGvS4NNUkO+Q1EwnJANpZufM5o1In4yrSQrVk1xaNkLe/bkwAeU2MOqk1YlFVc3rTpadz0bwSSI/vqkgeQuH+ZBINnRGrDPaVAIT33OqsA2zvfrLu2VOVjSBBcTNGb94LW7MF/eToaKS6TkNdloTd2qbf4Zsavo0J6ShHPYgJjKOkbHuU6sLMawOQp+5ygooMxYpQZTd7FPRsuSUd4q60q7KxzJByNYexOfN//9Sl8Pf0oT5dFMOESUFcK2jvcz6jZK7dTYXAiMGuOb7PrWqC2+cvzFSea9qH+xjEwy7y/wAY6qAG8QVWjId2Vc9bIGdtAmbIhyK6fUwEuEoWzlQ8QOl2jJb5ZfnMBqACpfoBStxpSPAfk00s1lYHqdU3ea+b7Sn2Zi/j46VS7WroSZ5T1CdgISLkA6Nvr1a3YgklUmTWCtCnn/YZe1jmxUWwEOfGZiE01aWbk1P1f7PnlLfF9cvNPnnsUxt9aLrWgs5/naUQ6CvdT6/58v714At2EQ0zoA2C8HIH+8OfRh0I=","expiration":1745874010000}}'
2025-04-29 01:30:12,638 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D2CF3E0>
2025-04-29 01:30:12,638 [DEBUG] No retry needed.
2025-04-29 01:30:12,642 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:10+00:00
2025-04-29 01:30:12,642 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,642 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDEJtaccHW7XOLxn9l/W9kT4oV5XAR32EiB5FmgEmy5KAiBX9ThA5Y/pC3xNRhTdSbqXzgXGJY5D56TDwnDIUHsiHCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMzn5AL15cdEb+B5XUKswCXHLgfaM7qpSwGQTSKd6O3/3tZC82nDvi/51s8IpX2+1DE9v81LaT32eHq0gQQmtUPULcw0PWLwNVuWZkvc6me0+mmk23TuzRJDvzEG5gzP3L46cYeSgDBMFmO1Wc1oHpgl26Ubf4QLBAZnTrpNPd7QiGvS4NNUkO+Q1EwnJANpZufM5o1In4yrSQrVk1xaNkLe/bkwAeU2MOqk1YlFVc3rTpadz0bwSSI/vqkgeQuH+ZBINnRGrDPaVAIT33OqsA2zvfrLu2VOVjSBBcTNGb94LW7MF/eToaKS6TkNdloTd2qbf4Zsavo0J6ShHPYgJjKOkbHuU6sLMawOQp+5ygooMxYpQZTd7FPRsuSUd4q60q7KxzJByNYexOfN//9Sl8Pf0oT5dFMOESUFcK2jvcz6jZK7dTYXAiMGuOb7PrWqC2+cvzFSea9qH+xjEwy7y/wAY6qAG8QVWjId2Vc9bIGdtAmbIhyK6fUwEuEoWzlQ8QOl2jJb5ZfnMBqACpfoBStxpSPAfk00s1lYHqdU3ea+b7Sn2Zi/j46VS7WroSZ5T1CdgISLkA6Nvr1a3YgklUmTWCtCnn/YZe1jmxUWwEOfGZiE01aWbk1P1f7PnlLfF9cvNPnnsUxt9aLrWgs5/naUQ6CvdT6/58v714At2EQ0zoA2C8HIH+8OfRh0I=

content-type;host;x-amz-date;x-amz-security-token
296317dea2c568465e05ce059f8cc30882f913d1af3d4aefe225cd3b44f15636
2025-04-29 01:30:12,642 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
01125d6698b229d3954746826f540d84b720d3d6801981e9214e5734e66e448a
2025-04-29 01:30:12,642 [DEBUG] Signature:
028e5732500e96f2ae9e03965bef5cae03490eff89117f4a1f68fc1b930b8c6b
2025-04-29 01:30:12,645 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27D119460>>
2025-04-29 01:30:12,645 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,645 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJGMEQCIDEJtaccHW7XOLxn9l/W9kT4oV5XAR32EiB5FmgEmy5KAiBX9ThA5Y/pC3xNRhTdSbqXzgXGJY5D56TDwnDIUHsiHCrvAgh9EAAaDDA0NjMxOTE4NDYwOCIMzn5AL15cdEb+B5XUKswCXHLgfaM7qpSwGQTSKd6O3/3tZC82nDvi/51s8IpX2+1DE9v81LaT32eHq0gQQmtUPULcw0PWLwNVuWZkvc6me0+mmk23TuzRJDvzEG5gzP3L46cYeSgDBMFmO1Wc1oHpgl26Ubf4QLBAZnTrpNPd7QiGvS4NNUkO+Q1EwnJANpZufM5o1In4yrSQrVk1xaNkLe/bkwAeU2MOqk1YlFVc3rTpadz0bwSSI/vqkgeQuH+ZBINnRGrDPaVAIT33OqsA2zvfrLu2VOVjSBBcTNGb94LW7MF/eToaKS6TkNdloTd2qbf4Zsavo0J6ShHPYgJjKOkbHuU6sLMawOQp+5ygooMxYpQZTd7FPRsuSUd4q60q7KxzJByNYexOfN//9Sl8Pf0oT5dFMOESUFcK2jvcz6jZK7dTYXAiMGuOb7PrWqC2+cvzFSea9qH+xjEwy7y/wAY6qAG8QVWjId2Vc9bIGdtAmbIhyK6fUwEuEoWzlQ8QOl2jJb5ZfnMBqACpfoBStxpSPAfk00s1lYHqdU3ea+b7Sn2Zi/j46VS7WroSZ5T1CdgISLkA6Nvr1a3YgklUmTWCtCnn/YZe1jmxUWwEOfGZiE01aWbk1P1f7PnlLfF9cvNPnnsUxt9aLrWgs5/naUQ6CvdT6/58v714At2EQ0zoA2C8HIH+8OfRh0I=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=028e5732500e96f2ae9e03965bef5cae03490eff89117f4a1f68fc1b930b8c6b', 'amz-sdk-invocation-id': b'6e42fab8-d931-4e6f-92ec-740169f3b29f', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,645 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:12,670 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:12,670 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:12 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '52edfe32-7b7c-4c46-b068-40314530f8c1', 'Server': 'AWS SSO', 'x-amzn-RequestId': '52edfe32-7b7c-4c46-b068-40314530f8c1'}
2025-04-29 01:30:12,670 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"QfKbZThqBVmj+qTfChDVOOfpRwMoaufBVbgkvgzM","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCICqIVIQuP0alWin/ICf07CEyudXmqSWS34jcx2YeHd6yAiEA7fcQZjCTxDszoi+GBrTHz1vpXtXOE6AoqRedu0In4Fsq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDIjntH9inJEP/AaacCrMAlLCYp+aJvDJRuC0eQbjL+nYkvHJNrqSsuWbgrDjMn/PptQRd6rZz0Fs3r9e1t3AxPY17rW2zh9hsL5cfOj9jtpzRoRpqOupQbitmaSrQHPCvsctfNxBrvSSTdBMMFOM+S393QXc8iEUDAe/JcNjWL0fsnWUi/SB/SXjZnxPlCYLz9kzoz9kzSOIomHLpDtt10PzZxhRFaVaW8/YKQVjErP3n8hpXcnb+P6UHB0E20VBwsAvIplEC3v5aF2YGHm+eqFOn7Uy1G6C651Z3U3+nknLbt1UOB+IBlm2pwcWliad4ubd2QS9NXHHqzGqdE/L9VmCVueGUzQv6tJOGza+5xggxqdcTzA/NV+xyCow0ttY7yS+ZiKiCAfmF1MB8S/jKQli8XanjSPeqMOEtko15Jj/z3ECBHMwdPPp2oSAnVeLy/G2LQ1cbOgvdueJMMy8v8AGOqcBhqxMcxvMvNuXDbKzgnlxWnxM0ylMmqbiygf5rs4cpJz7N6Uvi1sQQxE7A0qY0vFcQoqPi9sSvY1i2xjqEeIfXCiYYCsdhXQYAupGa9VKqt8w70iB9/7RtH3tFfS4EMuKi1CgNB7aQ7kMP/X6DnByMcV0Jd4R0oM5NkJ50ZQXeQqNszw6XFVr9aMIsmEf25Un1nkw5gdYk3Lrg/GRxRuPxud9y+98BXQ=","expiration":1745874011000}}'
2025-04-29 01:30:12,675 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D22F800>
2025-04-29 01:30:12,675 [DEBUG] No retry needed.
2025-04-29 01:30:12,677 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:11+00:00
2025-04-29 01:30:12,677 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:12,677 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200012Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCICqIVIQuP0alWin/ICf07CEyudXmqSWS34jcx2YeHd6yAiEA7fcQZjCTxDszoi+GBrTHz1vpXtXOE6AoqRedu0In4Fsq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDIjntH9inJEP/AaacCrMAlLCYp+aJvDJRuC0eQbjL+nYkvHJNrqSsuWbgrDjMn/PptQRd6rZz0Fs3r9e1t3AxPY17rW2zh9hsL5cfOj9jtpzRoRpqOupQbitmaSrQHPCvsctfNxBrvSSTdBMMFOM+S393QXc8iEUDAe/JcNjWL0fsnWUi/SB/SXjZnxPlCYLz9kzoz9kzSOIomHLpDtt10PzZxhRFaVaW8/YKQVjErP3n8hpXcnb+P6UHB0E20VBwsAvIplEC3v5aF2YGHm+eqFOn7Uy1G6C651Z3U3+nknLbt1UOB+IBlm2pwcWliad4ubd2QS9NXHHqzGqdE/L9VmCVueGUzQv6tJOGza+5xggxqdcTzA/NV+xyCow0ttY7yS+ZiKiCAfmF1MB8S/jKQli8XanjSPeqMOEtko15Jj/z3ECBHMwdPPp2oSAnVeLy/G2LQ1cbOgvdueJMMy8v8AGOqcBhqxMcxvMvNuXDbKzgnlxWnxM0ylMmqbiygf5rs4cpJz7N6Uvi1sQQxE7A0qY0vFcQoqPi9sSvY1i2xjqEeIfXCiYYCsdhXQYAupGa9VKqt8w70iB9/7RtH3tFfS4EMuKi1CgNB7aQ7kMP/X6DnByMcV0Jd4R0oM5NkJ50ZQXeQqNszw6XFVr9aMIsmEf25Un1nkw5gdYk3Lrg/GRxRuPxud9y+98BXQ=

content-type;host;x-amz-date;x-amz-security-token
d93a97cbafad2d2ba6c9cc58d6662c9017a33a1f9113fae00a6d226224a46271
2025-04-29 01:30:12,679 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200012Z
********/us-east-1/ec2/aws4_request
adecfed4de681e1e0fe14f9e9492f453f61d760c03ee3efd78b6407ef672b51a
2025-04-29 01:30:12,679 [DEBUG] Signature:
8ed6195128a3b5377aabb15c679e1975e6da7f0a0cb7bc827b28a68981c69dc5
2025-04-29 01:30:12,679 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27094C5F0>>
2025-04-29 01:30:12,679 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:12,679 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200012Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCICqIVIQuP0alWin/ICf07CEyudXmqSWS34jcx2YeHd6yAiEA7fcQZjCTxDszoi+GBrTHz1vpXtXOE6AoqRedu0In4Fsq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDIjntH9inJEP/AaacCrMAlLCYp+aJvDJRuC0eQbjL+nYkvHJNrqSsuWbgrDjMn/PptQRd6rZz0Fs3r9e1t3AxPY17rW2zh9hsL5cfOj9jtpzRoRpqOupQbitmaSrQHPCvsctfNxBrvSSTdBMMFOM+S393QXc8iEUDAe/JcNjWL0fsnWUi/SB/SXjZnxPlCYLz9kzoz9kzSOIomHLpDtt10PzZxhRFaVaW8/YKQVjErP3n8hpXcnb+P6UHB0E20VBwsAvIplEC3v5aF2YGHm+eqFOn7Uy1G6C651Z3U3+nknLbt1UOB+IBlm2pwcWliad4ubd2QS9NXHHqzGqdE/L9VmCVueGUzQv6tJOGza+5xggxqdcTzA/NV+xyCow0ttY7yS+ZiKiCAfmF1MB8S/jKQli8XanjSPeqMOEtko15Jj/z3ECBHMwdPPp2oSAnVeLy/G2LQ1cbOgvdueJMMy8v8AGOqcBhqxMcxvMvNuXDbKzgnlxWnxM0ylMmqbiygf5rs4cpJz7N6Uvi1sQQxE7A0qY0vFcQoqPi9sSvY1i2xjqEeIfXCiYYCsdhXQYAupGa9VKqt8w70iB9/7RtH3tFfS4EMuKi1CgNB7aQ7kMP/X6DnByMcV0Jd4R0oM5NkJ50ZQXeQqNszw6XFVr9aMIsmEf25Un1nkw5gdYk3Lrg/GRxRuPxud9y+98BXQ=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=8ed6195128a3b5377aabb15c679e1975e6da7f0a0cb7bc827b28a68981c69dc5', 'amz-sdk-invocation-id': b'822ccd71-d758-4940-baec-dcdf1f5d50a6', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:12,680 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:15,158 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:15,158 [DEBUG] Response headers: {'x-amzn-RequestId': 'b6433fc5-3e0d-4580-9b2d-60ccded77671', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:14 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:15,158 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>b6433fc5-3e0d-4580-9b2d-60ccded77671</RequestID></Response>'
2025-04-29 01:30:15,169 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F2705C6C60>
2025-04-29 01:30:15,169 [DEBUG] No retry needed.
2025-04-29 01:30:15,175 [DEBUG] Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-04-29 01:30:15,175 [DEBUG] Changing event name from before-call.apigateway to before-call.api-gateway
2025-04-29 01:30:15,175 [DEBUG] Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-04-29 01:30:15,180 [DEBUG] Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-04-29 01:30:15,180 [DEBUG] Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-04-29 01:30:15,180 [DEBUG] Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-04-29 01:30:15,180 [DEBUG] Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-04-29 01:30:15,180 [DEBUG] Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-04-29 01:30:15,180 [DEBUG] Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-04-29 01:30:15,180 [DEBUG] Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-04-29 01:30:15,180 [DEBUG] Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-04-29 01:30:15,180 [DEBUG] Setting config variable for profile to 'support-************'
2025-04-29 01:30:15,180 [DEBUG] Setting config variable for region to 'us-east-1'
2025-04-29 01:30:15,205 [DEBUG] IMDS ENDPOINT: http://***************/
2025-04-29 01:30:15,205 [DEBUG] Skipping environment variable credential check because profile name was explicitly set.
2025-04-29 01:30:15,205 [DEBUG] Looking for credentials via: assume-role
2025-04-29 01:30:15,205 [DEBUG] Looking for credentials via: assume-role-with-web-identity
2025-04-29 01:30:15,205 [DEBUG] Looking for credentials via: sso
2025-04-29 01:30:15,210 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\endpoints.json
2025-04-29 01:30:15,229 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sdk-default-configuration.json
2025-04-29 01:30:15,229 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:15,440 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\service-2.json.gz
2025-04-29 01:30:15,493 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:15,494 [DEBUG] Response headers: {'x-amzn-RequestId': '5c23837d-381d-44d5-8b2d-3ef02e3ed13b', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:14 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:15,494 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>5c23837d-381d-44d5-8b2d-3ef02e3ed13b</RequestID></Response>'
2025-04-29 01:30:15,504 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27CA40E60>
2025-04-29 01:30:15,504 [DEBUG] No retry needed.
2025-04-29 01:30:15,735 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\ec2\2016-11-15\endpoint-rule-set-1.json.gz
2025-04-29 01:30:15,735 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\partitions.json
2025-04-29 01:30:15,765 [DEBUG] Event creating-client-class.ec2: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:15,765 [DEBUG] Looking for endpoint for ec2 via: environment_service
2025-04-29 01:30:15,765 [DEBUG] Looking for endpoint for ec2 via: environment_global
2025-04-29 01:30:15,765 [DEBUG] Looking for endpoint for ec2 via: config_service
2025-04-29 01:30:15,765 [DEBUG] Looking for endpoint for ec2 via: config_global
2025-04-29 01:30:15,765 [DEBUG] No configured endpoint found.
2025-04-29 01:30:15,765 [DEBUG] Setting ec2 timeout as (60, 60)
2025-04-29 01:30:15,765 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\_retry.json
2025-04-29 01:30:15,765 [DEBUG] Registering retry handlers for service: ec2
2025-04-29 01:30:15,770 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <bound method ParameterAlias.alias_parameter_in_call of <botocore.handlers.ParameterAlias object at 0x000001F26FB33CB0>>
2025-04-29 01:30:15,770 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:15,770 [DEBUG] Event before-parameter-build.ec2.DescribeSecurityGroups: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:15,770 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:15,770 [DEBUG] Endpoint provider result: https://ec2.us-east-1.amazonaws.com
2025-04-29 01:30:15,770 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:15,770 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:15,770 [DEBUG] Event before-call.ec2.DescribeSecurityGroups: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:15,770 [DEBUG] Making request for OperationModel(name=DescribeSecurityGroups) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': {'Action': 'DescribeSecurityGroups', 'Version': '2016-11-15', 'GroupId.1': 'sg-0e9f24f2def2eed52', 'DryRun': 'true'}, 'url': 'https://ec2.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27F4C0AD0>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None}}
2025-04-29 01:30:15,770 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27F4C0A70>>
2025-04-29 01:30:15,770 [DEBUG] Event choose-signer.ec2.DescribeSecurityGroups: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:15,770 [DEBUG] Event choose-service-name: calling handler <function handle_service_name_alias at 0x000001F26FB3E520>
2025-04-29 01:30:15,770 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\service-2.json.gz
2025-04-29 01:30:15,775 [DEBUG] Loading JSON file: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\botocore\data\sso\2019-06-10\endpoint-rule-set-1.json.gz
2025-04-29 01:30:15,775 [DEBUG] Event creating-client-class.sso: calling handler <function add_generate_presigned_url at 0x000001F26FA85120>
2025-04-29 01:30:15,775 [DEBUG] Looking for endpoint for sso via: environment_service
2025-04-29 01:30:15,775 [DEBUG] Looking for endpoint for sso via: environment_global
2025-04-29 01:30:15,775 [DEBUG] Looking for endpoint for sso via: config_service
2025-04-29 01:30:15,775 [DEBUG] Looking for endpoint for sso via: config_global
2025-04-29 01:30:15,775 [DEBUG] No configured endpoint found.
2025-04-29 01:30:15,775 [DEBUG] Setting portal.sso timeout as (60, 60)
2025-04-29 01:30:15,775 [DEBUG] Registering retry handlers for service: sso
2025-04-29 01:30:15,775 [DEBUG] Checking for cached token at: ca29b6e991400fcf85c1ef34c4be192fe03c3ac4
2025-04-29 01:30:15,775 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function generate_idempotent_uuid at 0x000001F26FB5C5E0>
2025-04-29 01:30:15,775 [DEBUG] Event before-parameter-build.sso.GetRoleCredentials: calling handler <function _handle_request_validation_mode_member at 0x000001F26FB5F1A0>
2025-04-29 01:30:15,775 [DEBUG] Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-04-29 01:30:15,775 [DEBUG] Endpoint provider result: https://portal.sso.us-east-1.amazonaws.com
2025-04-29 01:30:15,775 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_recursion_detection_header at 0x000001F26FB3F920>
2025-04-29 01:30:15,775 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function add_query_compatibility_header at 0x000001F26FB5F100>
2025-04-29 01:30:15,775 [DEBUG] Event before-call.sso.GetRoleCredentials: calling handler <function inject_api_version_header_if_needed at 0x000001F26FB5E0C0>
2025-04-29 01:30:15,775 [DEBUG] Making request for OperationModel(name=GetRoleCredentials) with params: {'url_path': '/federation/credentials', 'query_string': {'role_name': 'support', 'account_id': '************'}, 'method': 'GET', 'headers': {'x-amz-sso_bearer_token': 'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': 'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35'}, 'body': b'', 'url': 'https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x000001F27F4C3560>, 'has_streaming_input': False, 'auth_type': 'none', 'unsigned_payload': None}}
2025-04-29 01:30:15,775 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x000001F27F4C3530>>
2025-04-29 01:30:15,775 [DEBUG] Event choose-signer.sso.GetRoleCredentials: calling handler <function set_operation_specific_signer at 0x000001F26FB5C400>
2025-04-29 01:30:15,775 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27F524320>>
2025-04-29 01:30:15,775 [DEBUG] Event request-created.sso.GetRoleCredentials: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:15,775 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=GET, url=https://portal.sso.us-east-1.amazonaws.com/federation/credentials?role_name=support&account_id=************, headers={'x-amz-sso_bearer_token': b'aoaAAAAAGgP7ucm1DOq83BBX5o9dTRMrevONmaFfhk-0ST3z2AgzChjqJoDjNCsMB0YgUC2qMhBx42srFG4s5MShABkc0:MGYCMQCu1v6Spbw9OrtL1xDtN59A/UwVi7BIUId3JID1tT5G20hrGEnSOocBxdOATbZzAp0CMQDPksLKdp7bEdKpWF+eRalzT8xLEPu+g2cooQgJWOt0Qnd4wH2p8Y6p0yhS6wTbk98', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'amz-sdk-invocation-id': b'2cbf895a-d6df-4b3b-8fab-dbbbed2a9526', 'amz-sdk-request': b'attempt=1'}>
2025-04-29 01:30:15,775 [DEBUG] Starting new HTTPS connection (1): portal.sso.us-east-1.amazonaws.com:443
2025-04-29 01:30:15,805 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:15,805 [DEBUG] Response headers: {'x-amzn-RequestId': 'e1e77ed1-6073-480d-85fd-91b1822f9c68', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:15,805 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>e1e77ed1-6073-480d-85fd-91b1822f9c68</RequestID></Response>'
2025-04-29 01:30:15,815 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27094C920>
2025-04-29 01:30:15,815 [DEBUG] No retry needed.
2025-04-29 01:30:15,878 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:15,878 [DEBUG] Response headers: {'x-amzn-RequestId': '1f0b4ca3-003d-4d19-ae86-de9ca3bc7cba', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:15,880 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>1f0b4ca3-003d-4d19-ae86-de9ca3bc7cba</RequestID></Response>'
2025-04-29 01:30:15,889 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27CB53B90>
2025-04-29 01:30:15,889 [DEBUG] No retry needed.
2025-04-29 01:30:16,035 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,035 [DEBUG] Response headers: {'x-amzn-RequestId': '8615bb13-62a5-45bb-98ed-66a8471f8b15', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,035 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>8615bb13-62a5-45bb-98ed-66a8471f8b15</RequestID></Response>'
2025-04-29 01:30:16,045 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D11BAA0>
2025-04-29 01:30:16,045 [DEBUG] No retry needed.
2025-04-29 01:30:16,050 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,050 [DEBUG] Response headers: {'x-amzn-RequestId': '5a08ff38-e6c4-4966-850d-bce2bcfddad2', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,050 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>5a08ff38-e6c4-4966-850d-bce2bcfddad2</RequestID></Response>'
2025-04-29 01:30:16,060 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F2708FAA80>
2025-04-29 01:30:16,060 [DEBUG] No retry needed.
2025-04-29 01:30:16,075 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,075 [DEBUG] Response headers: {'x-amzn-RequestId': 'a36a7412-20ed-4f7e-abb2-51aeb588e189', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,076 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>a36a7412-20ed-4f7e-abb2-51aeb588e189</RequestID></Response>'
2025-04-29 01:30:16,081 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D1197C0>
2025-04-29 01:30:16,081 [DEBUG] No retry needed.
2025-04-29 01:30:16,098 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,098 [DEBUG] Response headers: {'x-amzn-RequestId': 'dad2c101-51ae-4759-a053-dfafbc59cfff', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,098 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>dad2c101-51ae-4759-a053-dfafbc59cfff</RequestID></Response>'
2025-04-29 01:30:16,106 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27CFB7260>
2025-04-29 01:30:16,106 [DEBUG] No retry needed.
2025-04-29 01:30:16,109 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,109 [DEBUG] Response headers: {'x-amzn-RequestId': 'cbbb4bd4-049f-4044-8c3c-4e4c3c54928a', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,109 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>cbbb4bd4-049f-4044-8c3c-4e4c3c54928a</RequestID></Response>'
2025-04-29 01:30:16,116 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D09B320>
2025-04-29 01:30:16,116 [DEBUG] No retry needed.
2025-04-29 01:30:16,118 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:16,119 [DEBUG] Response headers: {'x-amzn-RequestId': '2f249743-0d24-48bb-b240-9b7ca7a49a7b', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:15 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:16,119 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>2f249743-0d24-48bb-b240-9b7ca7a49a7b</RequestID></Response>'
2025-04-29 01:30:16,125 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D11A810>
2025-04-29 01:30:16,125 [DEBUG] No retry needed.
2025-04-29 01:30:18,346 [DEBUG] https://portal.sso.us-east-1.amazonaws.com:443 "GET /federation/credentials?role_name=support&account_id=************ HTTP/1.1" 200 1036
2025-04-29 01:30:18,346 [DEBUG] Response headers: {'Date': 'Mon, 28 Apr 2025 20:00:17 GMT', 'Content-Type': 'application/json', 'Content-Length': '1036', 'Connection': 'keep-alive', 'Access-Control-Expose-Headers': 'RequestId, x-amzn-RequestId', 'Cache-Control': 'no-cache', 'RequestId': '********-1c77-4d83-b9c5-8b4b993cda8d', 'Server': 'AWS SSO', 'x-amzn-RequestId': '********-1c77-4d83-b9c5-8b4b993cda8d'}
2025-04-29 01:30:18,347 [DEBUG] Response body:
b'{"roleCredentials":{"accessKeyId":"********************","secretAccessKey":"u8bCq99yJX7CYUIPjSKXkaWuMCjV5aMFDqq4cWxS","sessionToken":"IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDWGrbTYHHfYfUTCS82D0FwCNGTz2CZneMv3C8Nj+om8gIgX0tkLFDAvaE1rV0PX6KvknPe0lhUQp+yV1zajZLYm+Uq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDHBZgN7BpRwFyvaHMyrMAkMaYgeYpvU1kIVtdfM/RARMbs54aMB/n3sE8zKm0Fy4ce7avlzhT+4frvIxBGbagaJbHXYzCFi+IQXp6ZeFFtKlvKNzIbzek/C62wtmSMftr/wFsSbPxw4Cc6buBWjcbyAZwAUZOaYJaNtq+kWlXVUzG+wCtfHg2xr4oIERH+IKuHmeGwE/dAk1QEJD5bJ/DqS5PLlqABTuF+oifA4x5qe7IjazcRf2JkwgpY6ixXCw7C6M31yfk2Gj0DjuezVL0wE34ZQaRCJybQ3pGlytitVv9wSUC8Tn1K1QX8l2v19hzO0Dn0hstlzGgluDMo6P1kZG6HqjyqQTpMpZzuph5mV72yC4VdY+z8YEYRfVUJykfmirJFJwcgP0DjYnm+cOhmKz2lQzYBYLiV/jK7wOE7Uy+1siKFmrngW2OaiXgVsUDaQTT17Tew1nGelrMNG8v8AGOqcB4alr+/mOBDcGvjpU9lD/mmuOFxAa4e3J8eO7NrZyNzafQHy6Hiff4nl3/Lgxr8NDqAw4/mYSHhBT7p+n933tmEYG8CGPZOEjCsvbf9xj6w6cylc+wok6C6oFtdmiSaEHlMau5hBWEQdfT8jgNF3AoR6GaZsdR5OHgjrkZ12y+KTehw16XTJHbVDDugAKSW0OcTXgPjX0w943tkmDUdanrXJayYspooY=","expiration":1745874016000}}'
2025-04-29 01:30:18,347 [DEBUG] Event needs-retry.sso.GetRoleCredentials: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27F5245C0>
2025-04-29 01:30:18,347 [DEBUG] No retry needed.
2025-04-29 01:30:18,350 [DEBUG] Retrieved credentials will expire at: 2025-04-28 21:00:16+00:00
2025-04-29 01:30:18,350 [DEBUG] Calculating signature using v4 auth.
2025-04-29 01:30:18,350 [DEBUG] CanonicalRequest:
POST
/

content-type:application/x-www-form-urlencoded; charset=utf-8
host:ec2.us-east-1.amazonaws.com
x-amz-date:********T200018Z
x-amz-security-token:IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDWGrbTYHHfYfUTCS82D0FwCNGTz2CZneMv3C8Nj+om8gIgX0tkLFDAvaE1rV0PX6KvknPe0lhUQp+yV1zajZLYm+Uq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDHBZgN7BpRwFyvaHMyrMAkMaYgeYpvU1kIVtdfM/RARMbs54aMB/n3sE8zKm0Fy4ce7avlzhT+4frvIxBGbagaJbHXYzCFi+IQXp6ZeFFtKlvKNzIbzek/C62wtmSMftr/wFsSbPxw4Cc6buBWjcbyAZwAUZOaYJaNtq+kWlXVUzG+wCtfHg2xr4oIERH+IKuHmeGwE/dAk1QEJD5bJ/DqS5PLlqABTuF+oifA4x5qe7IjazcRf2JkwgpY6ixXCw7C6M31yfk2Gj0DjuezVL0wE34ZQaRCJybQ3pGlytitVv9wSUC8Tn1K1QX8l2v19hzO0Dn0hstlzGgluDMo6P1kZG6HqjyqQTpMpZzuph5mV72yC4VdY+z8YEYRfVUJykfmirJFJwcgP0DjYnm+cOhmKz2lQzYBYLiV/jK7wOE7Uy+1siKFmrngW2OaiXgVsUDaQTT17Tew1nGelrMNG8v8AGOqcB4alr+/mOBDcGvjpU9lD/mmuOFxAa4e3J8eO7NrZyNzafQHy6Hiff4nl3/Lgxr8NDqAw4/mYSHhBT7p+n933tmEYG8CGPZOEjCsvbf9xj6w6cylc+wok6C6oFtdmiSaEHlMau5hBWEQdfT8jgNF3AoR6GaZsdR5OHgjrkZ12y+KTehw16XTJHbVDDugAKSW0OcTXgPjX0w943tkmDUdanrXJayYspooY=

content-type;host;x-amz-date;x-amz-security-token
73e1b3e680b2b661e51539d4f4a12e352e1c94c63e7c5e9358c1e251340bd0d5
2025-04-29 01:30:18,350 [DEBUG] StringToSign:
AWS4-HMAC-SHA256
********T200018Z
********/us-east-1/ec2/aws4_request
247ad949609db88faa7a037b91e7d20577121ca3557a99819bcea7493d86d0ea
2025-04-29 01:30:18,350 [DEBUG] Signature:
058c7a8fd48c6d47c749cb21890026685726a326f2fcd98ec4cdc19c0669bb8a
2025-04-29 01:30:18,350 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x000001F27F4C16A0>>
2025-04-29 01:30:18,350 [DEBUG] Event request-created.ec2.DescribeSecurityGroups: calling handler <function add_retry_headers at 0x000001F26FB5E980>
2025-04-29 01:30:18,350 [DEBUG] Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://ec2.us-east-1.amazonaws.com/, headers={'Content-Type': b'application/x-www-form-urlencoded; charset=utf-8', 'User-Agent': b'Boto3/1.37.35 md/Botocore#1.37.35 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.12.4 md/pyimpl#CPython cfg/retry-mode#legacy Botocore/1.37.35', 'X-Amz-Date': b'********T200018Z', 'X-Amz-Security-Token': b'IQoJb3JpZ2luX2VjEOT//////////wEaCXVzLWVhc3QtMSJHMEUCIQDWGrbTYHHfYfUTCS82D0FwCNGTz2CZneMv3C8Nj+om8gIgX0tkLFDAvaE1rV0PX6KvknPe0lhUQp+yV1zajZLYm+Uq7wIIfRAAGgwwNDYzMTkxODQ2MDgiDHBZgN7BpRwFyvaHMyrMAkMaYgeYpvU1kIVtdfM/RARMbs54aMB/n3sE8zKm0Fy4ce7avlzhT+4frvIxBGbagaJbHXYzCFi+IQXp6ZeFFtKlvKNzIbzek/C62wtmSMftr/wFsSbPxw4Cc6buBWjcbyAZwAUZOaYJaNtq+kWlXVUzG+wCtfHg2xr4oIERH+IKuHmeGwE/dAk1QEJD5bJ/DqS5PLlqABTuF+oifA4x5qe7IjazcRf2JkwgpY6ixXCw7C6M31yfk2Gj0DjuezVL0wE34ZQaRCJybQ3pGlytitVv9wSUC8Tn1K1QX8l2v19hzO0Dn0hstlzGgluDMo6P1kZG6HqjyqQTpMpZzuph5mV72yC4VdY+z8YEYRfVUJykfmirJFJwcgP0DjYnm+cOhmKz2lQzYBYLiV/jK7wOE7Uy+1siKFmrngW2OaiXgVsUDaQTT17Tew1nGelrMNG8v8AGOqcB4alr+/mOBDcGvjpU9lD/mmuOFxAa4e3J8eO7NrZyNzafQHy6Hiff4nl3/Lgxr8NDqAw4/mYSHhBT7p+n933tmEYG8CGPZOEjCsvbf9xj6w6cylc+wok6C6oFtdmiSaEHlMau5hBWEQdfT8jgNF3AoR6GaZsdR5OHgjrkZ12y+KTehw16XTJHbVDDugAKSW0OcTXgPjX0w943tkmDUdanrXJayYspooY=', 'Authorization': b'AWS4-HMAC-SHA256 Credential=********************/********/us-east-1/ec2/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-security-token, Signature=058c7a8fd48c6d47c749cb21890026685726a326f2fcd98ec4cdc19c0669bb8a', 'amz-sdk-invocation-id': b'23937053-59f3-49e2-8b1d-e65f0aca01ad', 'amz-sdk-request': b'attempt=1', 'Content-Length': '91'}>
2025-04-29 01:30:18,350 [DEBUG] Starting new HTTPS connection (1): ec2.us-east-1.amazonaws.com:443
2025-04-29 01:30:20,952 [DEBUG] https://ec2.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 412 None
2025-04-29 01:30:20,952 [DEBUG] Response headers: {'x-amzn-RequestId': 'f6203eca-ecd0-4c4b-b3e3-106d781331b0', 'Cache-Control': 'no-cache, no-store', 'Strict-Transport-Security': 'max-age=********; includeSubDomains', 'vary': 'accept-encoding', 'Content-Type': 'text/xml;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Date': 'Mon, 28 Apr 2025 20:00:20 GMT', 'Server': 'AmazonEC2'}
2025-04-29 01:30:20,952 [DEBUG] Response body:
b'<?xml version="1.0" encoding="UTF-8"?>\n<Response><Errors><Error><Code>DryRunOperation</Code><Message>Request would have succeeded, but DryRun flag is set.</Message></Error></Errors><RequestID>f6203eca-ecd0-4c4b-b3e3-106d781331b0</RequestID></Response>'
2025-04-29 01:30:20,965 [DEBUG] Event needs-retry.ec2.DescribeSecurityGroups: calling handler <botocore.retryhandler.RetryHandler object at 0x000001F27D2CEC60>
2025-04-29 01:30:20,965 [DEBUG] No retry needed.
