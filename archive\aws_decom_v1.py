import boto3
import csv
import time
import logging
import argparse
from collections import defaultdict
from datetime import datetime
from colorama import init, Fore, Style

init(autoreset=True)

logging.basicConfig(filename='aws_cleanup.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


def parse_arn(arn):
    try:
        parts = arn.split(":")
        region = parts[3]
        account_id = parts[4]
        resource_type, resource_id = parts[5].split("/") if "/" in parts[5] else parts[5].split(":")
        return region, account_id, resource_type.lower(), resource_id
    except Exception:
        return None, None, None, None


def get_session_and_account_name(account_id, region):
    try:
        profile_name = f"support-{account_id}"
        session = boto3.session.Session(profile_name=profile_name, region_name=region)
        iam_client = session.client("iam", region_name=region, verify=False)
        account_aliases = iam_client.list_account_aliases().get("AccountAliases", [])
        account_name = account_aliases[0] if account_aliases else "Unknown"
        return session, account_name
    except Exception as e:
        logging.error(f"Failed to get session or account name for {account_id}: {e}")
        return None, "Unknown"


def delete_resource(session, region, resource_type, resource_id, dry_run=False):
    created_date = "N/A"
    deleted_date = "N/A"

    try:
        if resource_type == "instance":
            ec2 = session.client("ec2", region_name=region)
            resp = ec2.describe_instances(InstanceIds=[resource_id])
            reservations = resp.get("Reservations", [])
            if reservations:
                instance = reservations[0]['Instances'][0]
                created_date = instance['LaunchTime'].isoformat()

                ec2.modify_instance_attribute(InstanceId=resource_id, DisableApiTermination={'Value': False})

                if dry_run:
                    ec2.terminate_instances(InstanceIds=[resource_id], DryRun=True)
                    return created_date, "Simulated", "DryRun: Termination simulated"
                else:
                    ec2.terminate_instances(InstanceIds=[resource_id])
                    deleted_date = datetime.utcnow().isoformat()
                    return created_date, deleted_date, "Success"

        elif resource_type == "volume":
            ec2 = session.client("ec2", region_name=region)
            volume = ec2.describe_volumes(VolumeIds=[resource_id])["Volumes"][0]
            created_date = volume["CreateTime"].isoformat()
            if dry_run:
                return created_date, "Simulated", "DryRun: Volume deletion simulated"
            ec2.delete_volume(VolumeId=resource_id)
            return created_date, datetime.utcnow().isoformat(), "Success"

        elif resource_type == "snapshot":
            ec2 = session.client("ec2", region_name=region)
            snap = ec2.describe_snapshots(SnapshotIds=[resource_id])["Snapshots"][0]
            created_date = snap["StartTime"].isoformat()
            if dry_run:
                return created_date, "Simulated", "DryRun: Snapshot deletion simulated"
            ec2.delete_snapshot(SnapshotId=resource_id)
            return created_date, datetime.utcnow().isoformat(), "Success"

        elif resource_type == "rule":
            events = session.client("events", region_name=region)
            if dry_run:
                return "N/A", "Simulated", "DryRun: EventBridge rule deletion simulated"
            events.delete_rule(Name=resource_id, Force=True)
            return "N/A", datetime.utcnow().isoformat(), "Success"

        elif resource_type == "function":
            lambda_client = session.client("lambda", region_name=region)
            config = lambda_client.get_function_configuration(FunctionName=resource_id)
            created_date = config.get("LastModified", "N/A")
            if dry_run:
                return created_date, "Simulated", "DryRun: Lambda deletion simulated"
            lambda_client.delete_function(FunctionName=resource_id)
            return created_date, datetime.utcnow().isoformat(), "Success"

        elif resource_type == "stack":
            cfn = session.client("cloudformation", region_name=region)
            if dry_run:
                return "N/A", "Simulated", "DryRun: Stack deletion simulated"
            cfn.delete_stack(StackName=resource_id)
            return "N/A", datetime.utcnow().isoformat(), "Success"

        else:
            return "N/A", "N/A", f"Unsupported: {resource_type}"
    except Exception as e:
        return created_date, deleted_date, f"Failed: {str(e)}"


def preview_colored_csv(csv_file):
    try:
        print(Fore.CYAN + "\n📊 DRY-RUN CSV SUMMARY (Preview):")
        with open(csv_file, 'r') as f:
            reader = csv.reader(f)
            headers = next(reader)
            print(Style.BRIGHT + " | ".join(headers))

            for row in reader:
                resource_type, outcome, count = row
                if outcome.lower() == "simulated":
                    color = Fore.GREEN
                elif "fail" in outcome.lower():
                    color = Fore.RED
                elif outcome.lower() == "invalid":
                    color = Fore.YELLOW
                else:
                    color = Fore.WHITE
                print(color + " | ".join(row))
        print(Fore.CYAN + "==============================\n")
    except Exception as e:
        logging.error(f"Failed to preview dry_run_summary.csv: {e}")


def process_resources(input_csv, output_csv, dry_run=False):
    dry_run_summary = defaultdict(int)

    with open(input_csv, 'r') as infile, open(output_csv, 'w', newline='') as outfile:
        reader = csv.DictReader(infile)
        fieldnames = [
            "Account_Name", "Account_id", "Resource_Type", "Resource_ID",
            "Launch/Created Date", "Deleted date", "Status", "Duration (seconds)"
        ]
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        for row in reader:
            arn = row.get("Resource_ARN")
            if not arn:
                continue

            region, account_id, resource_type, resource_id = parse_arn(arn)
            if not all([region, account_id, resource_type, resource_id]):
                logging.warning(f"Invalid ARN skipped: {arn}")
                dry_run_summary['Invalid'] += 1
                continue

            session, account_name = get_session_and_account_name(account_id, region)
            if not session:
                status = "SessionFailed"
                logging.error(f"Session creation failed for account {account_id}")
                dry_run_summary[f"{resource_type}-SessionFailed"] += 1
                writer.writerow({
                    "Account_Name": account_name, "Account_id": account_id,
                    "Resource_Type": resource_type, "Resource_ID": resource_id,
                    "Launch/Created Date": "N/A", "Deleted date": "N/A",
                    "Status": status, "Duration (seconds)": "0"
                })
                continue

            start_time = time.time()
            created_date, deleted_date, status = delete_resource(
                session, region, resource_type, resource_id, dry_run=dry_run
            )
            duration = round(time.time() - start_time, 2)

            writer.writerow({
                "Account_Name": account_name, "Account_id": account_id,
                "Resource_Type": resource_type, "Resource_ID": resource_id,
                "Launch/Created Date": created_date, "Deleted date": deleted_date,
                "Status": status, "Duration (seconds)": duration
            })

            if dry_run:
                if status.startswith("DryRun:"):
                    dry_run_summary[f"{resource_type}-Simulated"] += 1
                else:
                    dry_run_summary[f"{resource_type}-Failed"] += 1

    if dry_run:
        total_simulated = sum(v for k, v in dry_run_summary.items() if k.endswith("-Simulated"))
        total_failed = sum(v for k, v in dry_run_summary.items() if not k.endswith("-Simulated"))

        print(Fore.YELLOW + Style.BRIGHT + "\n======= DRY-RUN SUMMARY =======")
        print(Fore.GREEN + f"✅ Simulated deletions: {total_simulated}")
        print(Fore.RED + f"❌ Skipped/Failed: {total_failed}")
        for k, v in sorted(dry_run_summary.items()):
            color = Fore.GREEN if k.endswith("-Simulated") else Fore.RED
            print(color + f"  - {k}: {v}")
        print(Fore.YELLOW + "================================")

        # Export to CSV only (JSON removed)
        with open("dry_run_summary.csv", "w", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(["Resource Type", "Outcome", "Count"])
            for key, value in sorted(dry_run_summary.items()):
                if '-' in key:
                    resource, outcome = key.split('-', 1)
                else:
                    resource, outcome = key, "Unknown"
                writer.writerow([resource, outcome, value])

        preview_colored_csv("dry_run_summary.csv")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Delete AWS resources from a CSV of ARNs.")
    parser.add_argument("input_csv", help="CSV file with column Resource_ARN")
    parser.add_argument("--dry-run", action="store_true", help="Simulate deletions without actual action")
    args = parser.parse_args()

    process_resources(args.input_csv, "deleted_resources_output.csv", dry_run=args.dry_run)
