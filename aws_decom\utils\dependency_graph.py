"""
Dependency graph visualization for AWS Resource Decommissioning Tool.
"""

import os
import logging
from typing import List, Dict, Any, Optional
import graphviz

from ..models.resource import Resource, ResourceDependency

class DependencyGraph:
    """
    Creates and visualizes dependency graphs for AWS resources.
    """
    
    def __init__(self, output_dir: str = "reports"):
        """
        Initialize the dependency graph.
        
        Args:
            output_dir: Directory to save the graph.
        """
        self.logger = logging.getLogger(__name__)
        self.output_dir = output_dir
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
    
    def create_graph(self, resources: List[Resource], cr_number: str) -> Optional[str]:
        """
        Create a dependency graph for the specified resources.
        
        Args:
            resources: List of resources to include in the graph.
            cr_number: Change request number.
            
        Returns:
            Path to the generated graph file or None if generation failed.
        """
        try:
            # Create a new graph
            dot = graphviz.Digraph(
                name=f"dependency_graph_{cr_number}",
                comment=f"Resource Dependencies for CR {cr_number}",
                format="png"
            )
            
            # Set graph attributes
            dot.attr(rankdir="LR", size="10,10", ratio="fill", fontname="Arial")
            dot.attr("node", shape="box", style="filled", fontname="Arial")
            dot.attr("edge", fontname="Arial")
            
            # Add resources as nodes
            for resource in resources:
                # Set node color based on resource status
                if resource.deletion_success is True:
                    fillcolor = "lightgreen"
                elif resource.deletion_success is False:
                    fillcolor = "lightcoral"
                elif resource.dry_run_success is True:
                    fillcolor = "lightblue"
                elif resource.dry_run_success is False:
                    fillcolor = "lightsalmon"
                else:
                    fillcolor = "lightgray"
                
                # Create node label
                label = f"{resource.service}:{resource.resource_type}\\n{resource.resource_id}\\n{resource.region}"
                
                # Add node to graph
                dot.node(
                    resource.full_arn,
                    label=label,
                    fillcolor=fillcolor,
                    tooltip=f"ARN: {resource.full_arn}"
                )
            
            # Add dependencies as edges
            for resource in resources:
                for dependency in resource.dependencies:
                    # Find the target resource
                    target_resource = None
                    for res in resources:
                        if (res.service == dependency.resource_type.split(':')[0] and
                            res.resource_id == dependency.resource_id):
                            target_resource = res
                            break
                    
                    # If target resource is in our list, add an edge
                    if target_resource:
                        # Set edge color based on blocking status
                        color = "red" if dependency.blocking else "blue"
                        style = "solid" if dependency.blocking else "dashed"
                        
                        # Add edge to graph
                        dot.edge(
                            resource.full_arn,
                            target_resource.full_arn,
                            label=dependency.relationship,
                            color=color,
                            style=style,
                            tooltip=f"{dependency.relationship} ({dependency.resource_type})"
                        )
                    else:
                        # If target resource is not in our list, add a placeholder node
                        placeholder_id = f"{dependency.resource_type}:{dependency.resource_id}"
                        placeholder_label = f"{dependency.resource_type}\\n{dependency.resource_id}"
                        
                        dot.node(
                            placeholder_id,
                            label=placeholder_label,
                            fillcolor="white",
                            style="dashed,filled",
                            tooltip=f"External resource: {placeholder_id}"
                        )
                        
                        # Add edge to placeholder
                        color = "red" if dependency.blocking else "blue"
                        style = "solid" if dependency.blocking else "dashed"
                        
                        dot.edge(
                            resource.full_arn,
                            placeholder_id,
                            label=dependency.relationship,
                            color=color,
                            style=style,
                            tooltip=f"{dependency.relationship} ({dependency.resource_type})"
                        )
            
            # Render the graph
            output_path = os.path.join(self.output_dir, f"dependency_graph_{cr_number}")
            dot.render(output_path, cleanup=True)
            
            self.logger.info(f"Dependency graph created: {output_path}.png")
            return f"{output_path}.png"
            
        except Exception as e:
            self.logger.error(f"Error creating dependency graph: {e}")
            return None
    
    def create_service_graph(self, resources: List[Resource], cr_number: str) -> Optional[str]:
        """
        Create a service-level dependency graph.
        
        This graph shows dependencies between services rather than individual resources.
        
        Args:
            resources: List of resources to include in the graph.
            cr_number: Change request number.
            
        Returns:
            Path to the generated graph file or None if generation failed.
        """
        try:
            # Create a new graph
            dot = graphviz.Digraph(
                name=f"service_graph_{cr_number}",
                comment=f"Service Dependencies for CR {cr_number}",
                format="png"
            )
            
            # Set graph attributes
            dot.attr(rankdir="LR", size="8,8", ratio="fill", fontname="Arial")
            dot.attr("node", shape="box", style="filled", fontname="Arial", fillcolor="lightblue")
            dot.attr("edge", fontname="Arial")
            
            # Track services and their dependencies
            services = {}
            service_dependencies = {}
            
            # Collect services and resource counts
            for resource in resources:
                if resource.service not in services:
                    services[resource.service] = 0
                services[resource.service] += 1
            
            # Collect service dependencies
            for resource in resources:
                for dependency in resource.dependencies:
                    source_service = resource.service
                    target_service = dependency.resource_type.split(':')[0]
                    
                    if source_service not in service_dependencies:
                        service_dependencies[source_service] = {}
                    
                    if target_service not in service_dependencies[source_service]:
                        service_dependencies[source_service][target_service] = {
                            'count': 0,
                            'blocking': False
                        }
                    
                    service_dependencies[source_service][target_service]['count'] += 1
                    
                    # If any dependency is blocking, mark the service dependency as blocking
                    if dependency.blocking:
                        service_dependencies[source_service][target_service]['blocking'] = True
            
            # Add services as nodes
            for service, count in services.items():
                dot.node(
                    service,
                    label=f"{service}\\n({count} resources)",
                    tooltip=f"{service}: {count} resources"
                )
            
            # Add service dependencies as edges
            for source_service, targets in service_dependencies.items():
                for target_service, info in targets.items():
                    # Skip self-dependencies
                    if source_service == target_service:
                        continue
                    
                    # Set edge attributes
                    color = "red" if info['blocking'] else "blue"
                    style = "solid" if info['blocking'] else "dashed"
                    
                    # Add edge to graph
                    dot.edge(
                        source_service,
                        target_service,
                        label=str(info['count']),
                        color=color,
                        style=style,
                        tooltip=f"{info['count']} dependencies"
                    )
            
            # Render the graph
            output_path = os.path.join(self.output_dir, f"service_graph_{cr_number}")
            dot.render(output_path, cleanup=True)
            
            self.logger.info(f"Service graph created: {output_path}.png")
            return f"{output_path}.png"
            
        except Exception as e:
            self.logger.error(f"Error creating service graph: {e}")
            return None
