# AWS Resource Decommissioning Tool - Developer Guide

## Introduction

This guide provides detailed information for developers who want to extend or modify the AWS Resource Decommissioning Tool. It covers the architecture, key components, and guidelines for adding new features.

## Architecture Overview

The AWS Resource Decommissioning Tool follows a modular architecture with the following key components:

```
aws_decom/
├── core/                  # Core functionality
│   ├── config_manager.py  # Configuration management
│   ├── session_manager.py # AWS session management
│   ├── resource_manager.py # Resource management
│   └── validator.py       # Input validation
├── handlers/              # Resource handlers
│   ├── base_handler.py    # Base handler class
│   ├── ec2_handler.py     # EC2 instance handler
│   ├── s3_handler.py      # S3 bucket handler
│   └── ...                # Other handlers
├── models/                # Data models
│   ├── resource.py        # Resource model
│   └── report.py          # Report model
├── utils/                 # Utility functions
│   ├── arn_parser.py      # ARN parsing
│   ├── input_manager.py   # Input file handling
│   ├── dependency_graph.py # Dependency visualization
│   └── ...                # Other utilities
└── cli.py                 # Command-line interface
```

## Key Components

### Core Components

#### ConfigManager

The `ConfigManager` class handles configuration loading and management. It loads configuration from YAML files and supports environment variable overrides.

```python
from aws_decom.core.config_manager import ConfigManager

# Create a config manager
config_manager = ConfigManager("config/config.yaml")

# Get a configuration value
default_region = config_manager.get("general.default_region", "us-east-1")
```

#### SessionManager

The `SessionManager` class manages AWS sessions for different accounts. It creates and caches boto3 sessions based on account IDs.

```python
from aws_decom.core.session_manager import SessionManager

# Create a session manager
session_manager = SessionManager(config_manager)

# Get a session for an account
session = session_manager.get_session("************", "us-east-1")

# Get a client for a service
s3_client = session_manager.get_client(session, "s3")
```

#### ResourceManager

The `ResourceManager` class orchestrates the validation, dry run, and deletion of AWS resources. It uses resource handlers to perform operations on specific resource types.

```python
from aws_decom.core.resource_manager import ResourceManager

# Create a resource manager
resource_manager = ResourceManager(config_manager, session_manager)

# Process resources
report = resource_manager.process_resources(arns, cr_number, dry_run=True)
```

#### Validator

The `Validator` class validates inputs such as ARNs and change request numbers.

```python
from aws_decom.core.validator import Validator

# Create a validator
validator = Validator(config_manager)

# Validate a CR number
valid, message = validator.validate_cr_number("GECHG1234567")

# Validate ARNs
valid_arns, invalid_arns = validator.validate_arns(arns)
```

### Resource Handlers

Resource handlers are responsible for performing operations on specific resource types. They implement the following methods:

- `validate_resource`: Validate that a resource exists and can be deleted
- `check_dependencies`: Check for dependencies that might prevent deletion
- `dry_run`: Perform a dry run of resource deletion
- `delete_resource`: Delete a resource

All handlers inherit from the `BaseResourceHandler` class, which provides common functionality.

```python
from aws_decom.handlers.base_handler import BaseResourceHandler

class MyResourceHandler(BaseResourceHandler):
    def validate_resource(self, resource, session):
        # Implementation
        
    def check_dependencies(self, resource, session):
        # Implementation
        
    def dry_run(self, resource, session):
        # Implementation
        
    def delete_resource(self, resource, session):
        # Implementation
```

### Data Models

#### Resource

The `Resource` class represents an AWS resource to be decommissioned. It contains information about the resource, its dependencies, and operation status.

```python
from aws_decom.models.resource import Resource, ResourceDependency

# Create a resource
resource = Resource(
    service="ec2",
    resource_type="instance",
    resource_id="i-1234567890abcdef0",
    region="us-east-1",
    account_id="************",
    full_arn="arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0",
    cr_number="GECHG1234567"
)

# Add a dependency
resource.dependencies.append(ResourceDependency(
    resource_type="ec2:volume",
    resource_id="vol-1234567890abcdef0",
    relationship="attached_to",
    blocking=True
))
```

#### Report

The `DecommissioningReport` class represents a report of AWS resource decommissioning operations. It contains information about the resources processed and summary statistics.

```python
from aws_decom.models.report import DecommissioningReport

# Create a report
report = DecommissioningReport(cr_number="GECHG1234567")

# Add a resource to the report
report.add_resource(resource)

# Save the report
csv_path = report.save_csv("reports")
json_path = report.save_json("reports")
```

### Utilities

#### ARN Parser

The `ArnParser` class parses AWS ARNs into their components.

```python
from aws_decom.utils.arn_parser import ArnParser

# Create an ARN parser
arn_parser = ArnParser()

# Parse an ARN
parsed_arn = arn_parser.parse_arn("arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0")
```

#### Input Manager

The `InputManager` class loads ARNs from various file formats.

```python
from aws_decom.utils.input_manager import InputManager

# Create an input manager
input_manager = InputManager()

# Load ARNs from a file
arns = input_manager.load_arns_from_file("resources.csv")
```

#### Dependency Graph

The `DependencyGraph` class creates and visualizes dependency graphs for AWS resources.

```python
from aws_decom.utils.dependency_graph import DependencyGraph

# Create a dependency graph
dependency_graph = DependencyGraph("reports")

# Create a graph
graph_path = dependency_graph.create_graph(resources, "GECHG1234567")
```

## Extending the Tool

### Adding a New Resource Handler

To add support for a new AWS resource type, follow these steps:

1. Create a new handler class that inherits from `BaseResourceHandler`:

```python
# aws_decom/handlers/my_handler.py
from aws_decom.handlers.base_handler import BaseResourceHandler

class MyResourceHandler(BaseResourceHandler):
    def validate_resource(self, resource, session):
        # Implementation
        
    def check_dependencies(self, resource, session):
        # Implementation
        
    def dry_run(self, resource, session):
        # Implementation
        
    def delete_resource(self, resource, session):
        # Implementation
```

2. Register the handler in `handler_factory.py`:

```python
# aws_decom/handlers/handler_factory.py
from .my_handler import MyResourceHandler

# In the _register_handlers method
handler_classes = {
    # Existing handlers
    'my-service:my-resource': MyResourceHandler,
}
```

### Adding a New Feature

To add a new feature to the tool, follow these steps:

1. Implement the feature in the appropriate module
2. Update the CLI to expose the feature
3. Update the documentation to describe the feature
4. Add tests for the feature

## Testing

### Unit Tests

Unit tests are located in the `tests` directory. They use the `pytest` framework.

To run the tests:

```bash
pytest
```

### Integration Tests

Integration tests are also located in the `tests` directory. They require AWS credentials to run.

To run the integration tests:

```bash
pytest tests/integration
```

## Coding Guidelines

### Style Guide

- Follow PEP 8 for Python code style
- Use type hints for function parameters and return values
- Use docstrings to document classes and methods
- Use meaningful variable and function names

### Error Handling

- Use try-except blocks to handle exceptions
- Log errors with appropriate log levels
- Return meaningful error messages to the user

### Logging

- Use the logging module for all logging
- Use appropriate log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Include relevant context in log messages

## Release Process

1. Update version number in `aws_decom/__init__.py`
2. Update CHANGELOG.md with changes
3. Create a new release on GitHub
4. Build and publish the package to PyPI

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for your changes
5. Submit a pull request

## Resources

- [AWS SDK for Python (Boto3) Documentation](https://boto3.amazonaws.com/v1/documentation/api/latest/index.html)
- [AWS CLI Documentation](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html)
- [Python Documentation](https://docs.python.org/3/)
