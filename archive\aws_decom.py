import boto3
import botocore
import csv
from datetime import datetime
import re
import logging

# Setup logging
logging.basicConfig(
    filename='deletion.log',
    filemode='a',
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

def parse_arn(arn):
    parts = arn.split(":")
    if len(parts) < 6:
        return None, None, None, None
    region = parts[3]
    account_id = parts[4]
    service = parts[2]
    resource = parts[5]

    resource_type, resource_id = None, None
    if service == "cloudformation":
        match = re.match(r"stack/([^/]+)/", resource)
        if match:
            resource_type = "stack"
            resource_id = match.group(1)
    elif '/' in resource:
        resource_type, resource_id = resource.split('/', 1)
    elif ':' in resource:
        resource_type, resource_id = resource.split(':', 1)
    else:
        resource_type, resource_id = resource, ""

    return region, account_id, resource_type, resource_id

def get_session_and_account_name(account_id, region):
    profile_name = f"support-{account_id}"
    logging.info(f"Using profile: {profile_name}")
    try:
        session = boto3.session.Session(profile_name=profile_name, region_name=region)
        iam_client = session.client('iam', region_name=region, verify=False)
        aliases = iam_client.list_account_aliases().get('AccountAliases', [])
        account_name = aliases[0] if aliases else 'Unknown'
        logging.info(f"Account alias for {account_id}: {account_name}")
        return session, account_name
    except Exception as e:
        logging.error(f"Failed to create session for account {account_id}: {e}")
        return None, 'Unknown'

def delete_resource(session, region, resource_type, resource_id):
    created_date = "N/A"
    deleted_date = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")
    logging.info(f"Attempting to delete {resource_type} {resource_id} in {region}")

    try:
        if resource_type == "instance":
            ec2 = session.client("ec2", region_name=region)
            instance = ec2.describe_instances(InstanceIds=[resource_id])['Reservations'][0]['Instances'][0]
            created_date = instance['LaunchTime'].strftime("%Y-%m-%d %H:%M:%S UTC")
            ec2.modify_instance_attribute(InstanceId=resource_id, DisableApiTermination={'Value': False})
            ec2.terminate_instances(InstanceIds=[resource_id])
            logging.info(f"Terminated EC2 instance: {resource_id}")

        elif resource_type == "volume":
            ec2 = session.client("ec2", region_name=region)
            vol = ec2.describe_volumes(VolumeIds=[resource_id])['Volumes'][0]
            created_date = vol['CreateTime'].strftime("%Y-%m-%d %H:%M:%S UTC")
            ec2.delete_volume(VolumeId=resource_id)
            logging.info(f"Deleted volume: {resource_id}")

        elif resource_type == "snapshot":
            ec2 = session.client("ec2", region_name=region)
            snap = ec2.describe_snapshots(SnapshotIds=[resource_id])['Snapshots'][0]
            created_date = snap['StartTime'].strftime("%Y-%m-%d %H:%M:%S UTC")
            ec2.delete_snapshot(SnapshotId=resource_id)
            logging.info(f"Deleted snapshot: {resource_id}")

        elif resource_type == "function":
            lambda_client = session.client("lambda", region_name=region)
            lambda_client.delete_function(FunctionName=resource_id)
            created_date = "Unknown"
            logging.info(f"Deleted Lambda function: {resource_id}")

        elif resource_type == "rule":
            eb = session.client("events", region_name=region)
            targets = eb.list_targets_by_rule(Rule=resource_id).get('Targets', [])
            if targets:
                eb.remove_targets(Rule=resource_id, Ids=[t['Id'] for t in targets])
                logging.info(f"Removed {len(targets)} targets from EventBridge rule: {resource_id}")
            eb.delete_rule(Name=resource_id)
            created_date = "Unknown"
            logging.info(f"Deleted EventBridge rule: {resource_id}")

        elif resource_type == "stack":
            cfn = session.client("cloudformation", region_name=region)
            stacks = cfn.describe_stacks(StackName=resource_id)['Stacks']
            if stacks:
                created_date = stacks[0]['CreationTime'].strftime("%Y-%m-%d %H:%M:%S UTC")
            cfn.delete_stack(StackName=resource_id)
            logging.info(f"Deleted CloudFormation stack: {resource_id}")

        else:
            msg = f"Unknown resource type: {resource_type}"
            logging.warning(msg)
            return created_date, deleted_date, msg

        return created_date, deleted_date, "Success"

    except botocore.exceptions.ClientError as e:
        logging.error(f"Failed to delete {resource_type} {resource_id}: {e}")
        return created_date, deleted_date, f"Failed: {str(e)}"

def process_resources(input_csv, output_csv):
    with open(input_csv, 'r') as infile, open(output_csv, 'w', newline='') as outfile:
        reader = csv.DictReader(infile)
        fieldnames = ["Account_Name", "Account_id", "Resource_Type", "Resource_ID", "Launch/Created Date", "Deleted date", "Status"]
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        for row in reader:
            arn = row.get("Resource_ARN")
            if not arn:
                continue

            region, account_id, resource_type, resource_id = parse_arn(arn)
            if not all([region, account_id, resource_type, resource_id]):
                logging.warning(f"Invalid ARN skipped: {arn}")
                continue

            session, account_name = get_session_and_account_name(account_id, region)
            if not session:
                writer.writerow({
                    "Account_Name": account_name,
                    "Account_id": account_id,
                    "Resource_Type": resource_type,
                    "Resource_ID": resource_id,
                    "Launch/Created Date": "N/A",
                    "Deleted date": "N/A",
                    "Status": "SessionFailed"
                })
                continue

            created_date, deleted_date, status = delete_resource(session, region, resource_type, resource_id)

            writer.writerow({
                "Account_Name": account_name,
                "Account_id": account_id,
                "Resource_Type": resource_type,
                "Resource_ID": resource_id,
                "Launch/Created Date": created_date,
                "Deleted date": deleted_date,
                "Status": status
            })

if __name__ == "__main__":
    logging.info("Starting resource deletion script...")
    process_resources("input.csv", "deleted_resources_output.csv")
    logging.info("Resource deletion completed.")
