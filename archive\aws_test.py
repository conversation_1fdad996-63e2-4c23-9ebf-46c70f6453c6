# aws_cleanup_cli.py

import os
import boto3
import logging
import certifi
import warnings
import re
import pandas as pd
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Setup
warnings.filterwarnings("ignore")
os.environ["REQUESTS_CA_BUNDLE"] = certifi.where()

# Logging
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "cleanup.log")),
        logging.StreamHandler()
    ]
)

# ARN Patterns
ARN_PATTERNS = {
    'ec2': r'arn:aws:ec2:(.*?):(.*?):instance/(.*?)$',
    'ec2:image': r'arn:aws:ec2:(.*?):(.*?):image/(.*?)$',
    'ec2:volume': r'arn:aws:ec2:(.*?):(.*?):volume/(.*?)$',
    'ec2:snapshot': r'arn:aws:ec2:(.*?):(.*?):snapshot/(.*?)$',
    'ec2:securitygroup': r'arn:aws:ec2:(.*?):(.*?):security-group/(.*?)$',
    'lambda': r'arn:aws:lambda:(.*?):(.*?):function:(.*?)$',
    'sns': r'arn:aws:sns:(.*?):(.*?):(.*?)$',
    's3': r'arn:aws:s3:::(.*?)$',
    'cloudwatch:alarm': r'arn:aws:cloudwatch:(.*?):(.*?):alarm:(.*?)$',
    'secretsmanager:secret': r'arn:aws:secretsmanager:(.*?):(.*?):secret:(.*?)$',
    'states:stateMachine': r'arn:aws:states:(.*?):(.*?):stateMachine:(.*?)$',
    'events:rule': r'arn:aws:events:(.*?):(.*?):rule/(.*?)$',
    'cloudformation:stack': r'arn:aws:cloudformation:(.*?):(.*?):stack/(.*?)(?:/.*)?$',
    'elbv2:listener': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener/(.*?)/(.*?)$',
    'elbv2:targetgroup': r'arn:aws:elasticloadbalancing:(.*?):(.*?):targetgroup/(.*?)/(.*?)$',
    'elbv2:listener-rule': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener-rule/(.*?)/(.*?)$',
    'ssm:parameter': r'arn:aws:ssm:(.*?):(.*?):parameter/(.*?)$',
    'backup:backup-plan': r'arn:aws:backup:(.*?):(.*?):backup-plan:(.*?)$',
    'backup:backup-vault': r'arn:aws:backup:(.*?):(.*?):backup-vault:(.*?)$',
    'sns:subscription': r'arn:aws:sns:(.*?):(.*?):.*?:.*?$',
}

def parse_arn(arn):
    for rtype, pattern in ARN_PATTERNS.items():
        if re.match(pattern, arn):
            return rtype, arn, re.match(pattern, arn).groups()[-1]
    return 'unknown', arn, arn

def get_session(account_id):
    profile_name = f"support-{account_id}"
    try:
        return boto3.session.Session(profile_name=profile_name, region_name='us-east-1')
    except Exception as e:
        logging.error(f"Failed to create session for {profile_name}: {e}")
        return None

def delete_resource(rtype, session, arn, rid, dry_run=True):
    try:
        if rtype.startswith('ec2'):
            ec2 = session.client('ec2')
            if rtype == 'ec2':
                if not dry_run: ec2.terminate_instances(InstanceIds=[rid])
            elif rtype == 'ec2:image':
                if not dry_run: ec2.deregister_image(ImageId=rid)
            elif rtype == 'ec2:volume':
                if not dry_run: ec2.delete_volume(VolumeId=rid)
            elif rtype == 'ec2:snapshot':
                if not dry_run: ec2.delete_snapshot(SnapshotId=rid)
            elif rtype == 'ec2:securitygroup':
                if not dry_run: ec2.delete_security_group(GroupId=rid)

        elif rtype == 'lambda':
            if not dry_run: session.client('lambda').delete_function(FunctionName=rid)
        elif rtype == 'sns':
            if not dry_run: session.client('sns').delete_topic(TopicArn=arn)
        elif rtype == 's3':
            if not dry_run: session.client('s3').delete_bucket(Bucket=rid)
        elif rtype == 'cloudwatch:alarm':
            if not dry_run: session.client('cloudwatch').delete_alarms(AlarmNames=[rid])
        elif rtype == 'secretsmanager:secret':
            if not dry_run: session.client('secretsmanager').delete_secret(SecretId=rid, ForceDeleteWithoutRecovery=True)
        elif rtype == 'states:stateMachine':
            if not dry_run: session.client('stepfunctions').delete_state_machine(stateMachineArn=arn)
        elif rtype == 'events:rule':
            client = session.client('events')
            if not dry_run:
                client.remove_targets(Rule=rid, Ids=['*'])
                client.delete_rule(Name=rid)
        elif rtype == 'cloudformation:stack':
            if not dry_run: session.client('cloudformation').delete_stack(StackName=rid)
        elif rtype == 'elbv2:listener':
            if not dry_run: session.client('elbv2').delete_listener(ListenerArn=arn)
        elif rtype == 'elbv2:targetgroup':
            if not dry_run: session.client('elbv2').delete_target_group(TargetGroupArn=arn)
        elif rtype == 'elbv2:listener-rule':
            if not dry_run: session.client('elbv2').delete_rule(RuleArn=arn)
        elif rtype == 'ssm:parameter':
            if not dry_run: session.client('ssm').delete_parameter(Name=rid)
        elif rtype == 'backup:backup-plan':
            if not dry_run: session.client('backup').delete_backup_plan(BackupPlanId=rid)
        elif rtype == 'backup:backup-vault':
            if not dry_run: session.client('backup').delete_backup_vault(BackupVaultName=rid)
        elif rtype == 'sns:subscription':
            if not dry_run: session.client('sns').unsubscribe(SubscriptionArn=arn)

        logging.info(f"{'[DRY-RUN]' if dry_run else '[DELETED]'} {rtype}: {rid}")
        return {'resource_type': rtype, 'resource_id': rid, 'arn': arn, 'status': 'Dry-Run' if dry_run else 'Deleted', 'error': ''}
    except Exception as e:
        logging.error(f"Error deleting {rtype}: {rid} - {e}")
        return {'resource_type': rtype, 'resource_id': rid, 'arn': arn, 'status': 'Failed', 'error': str(e)}

def process_file(filepath, dry_run=True):
    df = pd.read_csv(filepath)
    summary = []

    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for _, row in df.iterrows():
            arn = row['arn']
            account_id = arn.split(':')[4]  # Extracting account ID from ARN
            session = get_session(account_id)
            if not session:
                continue
            rtype, parsed_arn, rid = parse_arn(arn)
            futures.append(executor.submit(delete_resource, rtype, session, parsed_arn, rid, dry_run))

        for future in futures:
            result = future.result()
            if result:
                summary.append(result)

    return pd.DataFrame(summary)

def main():
    import argparse
    parser = argparse.ArgumentParser(description="🧹 AWS Cleanup CLI")
    parser.add_argument('--file', required=True, help='Path to CSV file with ARNs')
    parser.add_argument('--dry-run', action='store_true', help='Only simulate the deletions')
    args = parser.parse_args()

    df = pd.read_csv(args.file)
    print("\n🔍 Resources found in file:\n")
    print(df['arn'].to_string(index=False))

    if not args.dry_run:
        print("\n⚠️  WARNING: The listed AWS resources will be PERMANENTLY DELETED!")
        confirm = input("Type 'YES' to proceed or 'NO' to cancel: ").strip()
        if confirm.lower() != 'yes':
            logging.info("Operation aborted by user.")
            return

    summary_df = process_file(args.file, dry_run=args.dry_run)
    summary_file = os.path.join(log_dir, f"cleanup_summary_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.csv")
    summary_df.to_csv(summary_file, index=False)
    logging.info(f"✅ Cleanup complete. Summary saved to {summary_file}")

if __name__ == "__main__":
    main()
