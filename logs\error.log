{"asctime": "2025-04-29 08:23:08,036", "name": "aws_decom.utils.input_manager", "levelname": "ERROR", "message": "Error loading ARNs from CSV file test_resources.csv: Could not determine delimiter"}
{"asctime": "2025-04-29 08:24:17,577", "name": "aws_decom.utils.input_manager", "levelname": "ERROR", "message": "Error loading ARNs from CSV file test_handlers.csv: Could not determine delimiter"}
{"asctime": "2025-04-29 08:25:15,840", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,840", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,850", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:15,907", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,908", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,912", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:15,968", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,970", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,974", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:16,022", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,023", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,023", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:16,087", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,089", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,091", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:16,134", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,139", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,139", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,479", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,480", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,480", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,523", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,531", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,531", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,574", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,576", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,580", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,628", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,630", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,631", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,769", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,769", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,777", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,832", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,839", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,847", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,899", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,904", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,904", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,670", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,670", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,676", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,778", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,780", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,784", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,884", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,889", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,895", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,984", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,990", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,994", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,142", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,142", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,150", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,200", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,200", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,209", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,255", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,261", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,261", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:42,799", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0c7b6d9a6a2ed1028' does not exist"}
{"asctime": "2025-04-29 08:32:42,799", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-09b8ea45284479875' does not exist"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-087886ad913505771' does not exist"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:50,434", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-06f9ef925e37efd17' does not exist"}
{"asctime": "2025-04-29 08:32:50,440", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:52,954", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-00e0cdac1a0eb0a21' does not exist"}
{"asctime": "2025-04-29 08:32:52,959", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:55,489", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-022732e968cba9142' does not exist"}
{"asctime": "2025-04-29 08:32:55,500", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0a8e2d1d949a06cd0' does not exist"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:00,589", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-041570276467c4850' does not exist"}
{"asctime": "2025-04-29 08:33:00,589", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:03,139", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0744b90d043ddddff' does not exist"}
{"asctime": "2025-04-29 08:33:03,143", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:05,700", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0fb6c6121c0c0411c' does not exist"}
{"asctime": "2025-04-29 08:33:05,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:08,234", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0e9f24f2def2eed52' does not exist"}
{"asctime": "2025-04-29 08:33:08,239", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:31,829", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:31,893", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:31,903", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:31,913", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:31,976", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,098", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,140", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,142", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,147", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,184", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,220", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,223", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,229", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,269", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,349", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,389", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,392", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,392", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,500", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,599", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,631", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,631", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,640", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:20,624", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0c7b6d9a6a2ed1028' does not exist"}
{"asctime": "2025-04-29 08:40:20,629", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:25,859", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-09b8ea45284479875' does not exist"}
{"asctime": "2025-04-29 08:40:25,870", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:31,229", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-087886ad913505771' does not exist"}
{"asctime": "2025-04-29 08:40:31,234", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:36,386", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-06f9ef925e37efd17' does not exist"}
{"asctime": "2025-04-29 08:40:36,390", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:41,539", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-00e0cdac1a0eb0a21' does not exist"}
{"asctime": "2025-04-29 08:40:41,547", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:46,769", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-022732e968cba9142' does not exist"}
{"asctime": "2025-04-29 08:40:46,777", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0a8e2d1d949a06cd0' does not exist"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:56,999", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-041570276467c4850' does not exist"}
{"asctime": "2025-04-29 08:40:57,007", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:41:02,364", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0744b90d043ddddff' does not exist"}
{"asctime": "2025-04-29 08:41:02,370", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:41:07,659", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0fb6c6121c0c0411c' does not exist"}
{"asctime": "2025-04-29 08:41:07,663", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:41:12,814", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0e9f24f2def2eed52' does not exist"}
{"asctime": "2025-04-29 08:41:12,819", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,275", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,364", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,398", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,399", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,399", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,451", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,531", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,620", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,662", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,664", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,706", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,789", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,873", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,924", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,930", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,931", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,421", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,461", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,461", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,466", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,509", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,624", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,666", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,671", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,732", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,793", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,793", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,800", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,864", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,919", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,919", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,924", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,970", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,010", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,012", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:04,049", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,083", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,083", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,089", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:04,129", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,164", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,166", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,169", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,368", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,409", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,440", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,482", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,482", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,489", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,546", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,572", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,579", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,579", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,619", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,659", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,663", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,709", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,749", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,755", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,817", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,888", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,961", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,009", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:07,059", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,099", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
