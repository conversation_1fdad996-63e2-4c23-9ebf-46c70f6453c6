"""
Resource manager for AWS Resource Decommissioning Tool.
"""

import logging
import time
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime

import boto3

from ..models.resource import Resource
from ..models.report import DecommissioningReport
from ..core.config_manager import ConfigManager
from ..core.session_manager import SessionManager
from ..handlers.handler_factory import ResourceHandlerFactory
from ..utils.arn_parser import ArnParser

class ResourceManager:
    """
    Manages AWS resources for decommissioning.

    Orchestrates the validation, dry run, and deletion of AWS resources.
    """

    def __init__(self, config_manager: ConfigManager, session_manager: SessionManager):
        """
        Initialize the resource manager.

        Args:
            config_manager: Configuration manager instance.
            session_manager: Session manager instance.
        """
        self.logger = logging.getLogger(__name__)
        self.config = config_manager
        self.session_manager = session_manager
        self.handler_factory = ResourceHandlerFactory(config_manager)
        self.arn_parser = ArnParser()

    def create_resource_from_arn(self, arn: str, cr_number: str) -> Optional[Resource]:
        """
        Create a resource object from an ARN.

        Args:
            arn: ARN to create resource from.
            cr_number: Change request number.

        Returns:
            Resource object or None if ARN is invalid.
        """
        # Parse the ARN
        parsed_arn = self.arn_parser.parse_arn(arn)

        # Check if ARN is valid
        if parsed_arn['service'] == 'unknown':
            self.logger.warning(f"Invalid ARN: {arn}")
            return None

        # Create resource object
        resource = Resource(
            service=parsed_arn['service'],
            resource_type=parsed_arn['resource_type'],
            resource_id=parsed_arn['resource_id'],
            region=parsed_arn['region'],
            account_id=parsed_arn['account_id'],
            full_arn=arn,
            cr_number=cr_number
        )

        return resource

    def validate_resource(self, resource: Resource) -> bool:
        """
        Validate a resource.

        Args:
            resource: Resource to validate.

        Returns:
            True if validation succeeded, False otherwise.
        """
        self.logger.info(f"Validating resource: {resource}")

        # Get session for the resource's account
        session = self.session_manager.get_session(resource.account_id, resource.region)
        if not session:
            resource.validation_message = f"Failed to create session for account {resource.account_id}"
            self.logger.error(resource.validation_message)
            return False

        # Get handler for the resource
        handler = self.handler_factory.get_handler(resource)
        if not handler:
            resource.validation_message = f"No handler available for resource type: {resource.service}:{resource.resource_type}"
            self.logger.error(resource.validation_message)
            return False

        # Validate the resource
        start_time = time.time()
        success, message = handler.validate_resource(resource, session)
        end_time = time.time()

        # Update resource with validation results
        resource.validated = success
        resource.validation_message = message
        resource.validation_duration = end_time - start_time

        # If validation succeeded, get additional resource information
        if success:
            # Get creation date
            resource.created_date = handler.get_created_date(resource, session)

            # Get tags
            resource.tags = handler.get_tags(resource, session)

            # Check dependencies
            resource.dependencies = handler.check_dependencies(resource, session)
            if resource.dependencies:
                dependency_str = ", ".join(str(dep) for dep in resource.dependencies)
                self.logger.info(f"Resource {resource} has dependencies: {dependency_str}")

        return success

    def dry_run_deletion(self, resource: Resource) -> bool:
        """
        Perform a dry run of resource deletion.

        Args:
            resource: Resource to delete.

        Returns:
            True if dry run succeeded, False otherwise.
        """
        self.logger.info(f"Performing dry run for resource: {resource}")

        # Check if resource was validated
        if not resource.validated:
            resource.dry_run_message = "Resource not validated"
            resource.dry_run_success = False
            self.logger.error(resource.dry_run_message)
            return False

        # Get session for the resource's account
        session = self.session_manager.get_session(resource.account_id, resource.region)
        if not session:
            resource.dry_run_message = f"Failed to create session for account {resource.account_id}"
            resource.dry_run_success = False
            self.logger.error(resource.dry_run_message)
            return False

        # Get handler for the resource
        handler = self.handler_factory.get_handler(resource)
        if not handler:
            resource.dry_run_message = f"No handler available for resource type: {resource.service}:{resource.resource_type}"
            resource.dry_run_success = False
            self.logger.error(resource.dry_run_message)
            return False

        # Check for blocking dependencies
        if resource.has_blocking_dependencies():
            dependency_str = ", ".join(str(dep) for dep in resource.dependencies if dep.blocking)
            resource.dry_run_message = f"Resource has blocking dependencies: {dependency_str}"
            resource.dry_run_success = False
            self.logger.warning(resource.dry_run_message)
            return False

        # Perform dry run
        start_time = time.time()
        success, message = handler.dry_run(resource, session)
        end_time = time.time()

        # Update resource with dry run results
        resource.dry_run_success = success
        resource.dry_run_message = message
        resource.dry_run_duration = end_time - start_time

        return success

    def delete_resource(self, resource: Resource) -> bool:
        """
        Delete a resource.

        Args:
            resource: Resource to delete.

        Returns:
            True if deletion succeeded, False otherwise.
        """
        self.logger.info(f"Deleting resource: {resource}")

        # Check if resource was validated
        if not resource.validated:
            resource.deletion_message = "Resource not validated"
            resource.deletion_success = False
            self.logger.error(resource.deletion_message)
            return False

        # Get session for the resource's account
        session = self.session_manager.get_session(resource.account_id, resource.region)
        if not session:
            resource.deletion_message = f"Failed to create session for account {resource.account_id}"
            resource.deletion_success = False
            self.logger.error(resource.deletion_message)
            return False

        # Get handler for the resource
        handler = self.handler_factory.get_handler(resource)
        if not handler:
            resource.deletion_message = f"No handler available for resource type: {resource.service}:{resource.resource_type}"
            resource.deletion_success = False
            self.logger.error(resource.deletion_message)
            return False

        # Check for blocking dependencies
        if resource.has_blocking_dependencies():
            # Check if we should force deletion
            force_deletion = self.config.get("resources.dependencies.cascade_delete", False)
            if not force_deletion:
                dependency_str = ", ".join(str(dep) for dep in resource.dependencies if dep.blocking)
                resource.deletion_message = f"Resource has blocking dependencies: {dependency_str}"
                resource.deletion_success = False
                self.logger.warning(resource.deletion_message)
                return False
            else:
                self.logger.warning(f"Forcing deletion of resource with dependencies: {resource}")

        # Perform deletion
        start_time = time.time()
        success, message = handler.delete_resource(resource, session)
        end_time = time.time()

        # Update resource with deletion results
        resource.deletion_success = success
        resource.deletion_message = message
        resource.deletion_duration = end_time - start_time

        if success:
            resource.deleted_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return success

    def process_resources(self, arns: List[str], cr_number: str, dry_run: bool = True) -> DecommissioningReport:
        """
        Process a list of resources.

        Args:
            arns: List of ARNs to process.
            cr_number: Change request number.
            dry_run: Whether to perform a dry run or actual deletion.

        Returns:
            DecommissioningReport containing results.
        """
        # Create report
        report = DecommissioningReport(cr_number=cr_number, is_dry_run=dry_run)

        # Import tqdm here to avoid circular imports
        from tqdm import tqdm
        from colorama import Fore

        # Set progress bar color based on operation type
        color = Fore.BLUE if dry_run else Fore.RED
        operation_type = "Dry Run" if dry_run else "Deletion"

        # Create progress bar
        with tqdm(total=len(arns), desc=f"{color}{operation_type} Progress", unit="resource",
                  bar_format="{l_bar}%s{bar}%s{r_bar}" % (color, Fore.RESET)) as pbar:
            # Process each ARN
            for arn in arns:
                # Create resource from ARN
                resource = self.create_resource_from_arn(arn, cr_number)
                if not resource:
                    self.logger.warning(f"Skipping invalid ARN: {arn}")
                    pbar.update(1)
                    continue

                # Get account name
                session = self.session_manager.get_session(resource.account_id, resource.region)
                if session:
                    account_name = self.session_manager.get_account_name(session)
                    # Add account name to resource tags
                    resource.tags["Account_Name"] = account_name

                # Validate resource
                self.validate_resource(resource)

                # Perform dry run or deletion
                if dry_run:
                    self.dry_run_deletion(resource)
                else:
                    # Perform dry run first to ensure resource can be deleted
                    if self.dry_run_deletion(resource):
                        self.delete_resource(resource)
                    else:
                        self.logger.warning(f"Skipping deletion of resource that failed dry run: {resource}")

                # Add resource to report
                report.add_resource(resource)

                # Update progress bar
                pbar.update(1)

        return report

    def group_resources_by_service(self, resources: List[Resource]) -> Dict[str, List[Resource]]:
        """
        Group resources by service.

        Args:
            resources: List of resources to group.

        Returns:
            Dict mapping service names to lists of resources.
        """
        result = {}
        for resource in resources:
            if resource.service not in result:
                result[resource.service] = []
            result[resource.service].append(resource)
        return result

    def group_resources_by_service_and_type(self, resources: List[Resource]) -> Dict[str, Dict[str, List[Resource]]]:
        """
        Group resources by service and resource type.

        Args:
            resources: List of resources to group.

        Returns:
            Dict mapping service names to dicts of resource types to lists of resources.
        """
        result = {}
        for resource in resources:
            if resource.service not in result:
                result[resource.service] = {}

            if resource.resource_type not in result[resource.service]:
                result[resource.service][resource.resource_type] = []

            result[resource.service][resource.resource_type].append(resource)
        return result

    def group_resources_by_account(self, resources: List[Resource]) -> Dict[str, List[Resource]]:
        """
        Group resources by account.

        Args:
            resources: List of resources to group.

        Returns:
            Dict mapping account IDs to lists of resources.
        """
        result = {}
        for resource in resources:
            if resource.account_id not in result:
                result[resource.account_id] = []
            result[resource.account_id].append(resource)
        return result
