"""
Resource handler factory for AWS Resource Decommissioning Tool.
"""

import logging
from typing import Dict, Optional, Type

from .base_handler import BaseResourceHandler
from .ec2_handler import EC2<PERSON>nstance<PERSON>andler
from .ec2_volume_handler import EC2VolumeHandler
from .ec2_snapshot_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .ec2_ami_handler import <PERSON>2AM<PERSON><PERSON><PERSON><PERSON>
from .ec2_security_group_handler import EC2SecurityGroupHandler
from .s3_handler import S3<PERSON>ucketHandler
from .lambda_handler import LambdaFunction<PERSON>andler
from .cloudwatch_alarm_handler import CloudWatchAlarmHandler
from .secretsmanager_handler import SecretsManagerSecretHandler
from .sns_topic_handler import S<PERSON>Topic<PERSON>andler
from .sns_subscription_handler import SNSSubscriptionHandler
from .stepfunctions_handler import StepFunctionsStateMachineHandler
from .eventbridge_handler import EventBridgeRuleHandler
from .cloudformation_handler import CloudFormationStackHandler
from .elb_handler import ELBLoadBalancerH<PERSON><PERSON>
from .elb_target_group_handler import <PERSON>LB<PERSON>argetGroup<PERSON><PERSON><PERSON>
from .elb_listener_handler import EL<PERSON>ist<PERSON>Hand<PERSON>
from .elb_listener_rule_handler import <PERSON>L<PERSON>istenerRuleHandler
from .ssm_parameter_handler import SSMParameterHandler
from .ssm_document_handler import SSMDocumentHandler
from .backup_vault_handler import BackupVaultHandler
from .backup_plan_handler import BackupPlanHandler
from ..models.resource import Resource
from ..core.config_manager import ConfigManager

class ResourceHandlerFactory:
    """
    Factory for creating resource handlers.

    Creates appropriate resource handlers based on resource type.
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the resource handler factory.

        Args:
            config_manager: Configuration manager instance.
        """
        self.logger = logging.getLogger(__name__)
        self.config = config_manager
        self.handlers: Dict[str, BaseResourceHandler] = {}

        # Register handlers
        self._register_handlers()

    def _register_handlers(self) -> None:
        """Register all resource handlers."""
        # Map of resource types to handler classes
        handler_classes = {
            # EC2 resources
            'ec2:instance': EC2InstanceHandler,
            'ec2:volume': EC2VolumeHandler,
            'ec2:snapshot': EC2SnapshotHandler,
            'ec2:image': EC2AMIHandler,
            'ec2:security-group': EC2SecurityGroupHandler,

            # S3 resources
            's3:bucket': S3BucketHandler,

            # Lambda resources
            'lambda:function': LambdaFunctionHandler,

            # CloudWatch resources
            'cloudwatch:alarm': CloudWatchAlarmHandler,

            # Secrets Manager resources
            'secretsmanager:secret': SecretsManagerSecretHandler,

            # SNS resources
            'sns:topic': SNSTopicHandler,
            'sns:subscription': SNSSubscriptionHandler,

            # Step Functions resources
            'states:stateMachine': StepFunctionsStateMachineHandler,

            # EventBridge resources
            'events:rule': EventBridgeRuleHandler,

            # CloudFormation resources
            'cloudformation:stack': CloudFormationStackHandler,

            # ELB resources
            'elasticloadbalancing:loadbalancer': ELBLoadBalancerHandler,
            'elasticloadbalancing:targetgroup': ELBTargetGroupHandler,
            'elasticloadbalancing:listener': ELBListenerHandler,
            'elasticloadbalancing:listener-rule': ELBListenerRuleHandler,

            # SSM resources
            'ssm:parameter': SSMParameterHandler,
            'ssm:document': SSMDocumentHandler,

            # AWS Backup resources
            'backup:backup-vault': BackupVaultHandler,
            'backup:backup-plan': BackupPlanHandler,

            # Add more handlers as they are implemented
        }

        # Create instances of all handlers
        for resource_type, handler_class in handler_classes.items():
            self.handlers[resource_type] = handler_class(self.config)
            self.logger.debug(f"Registered handler for {resource_type}: {handler_class.__name__}")

    def get_handler(self, resource: Resource) -> Optional[BaseResourceHandler]:
        """
        Get a handler for the specified resource.

        Args:
            resource: Resource to get handler for.

        Returns:
            Resource handler or None if no handler is available.
        """
        # Create resource type key
        resource_type_key = f"{resource.service}:{resource.resource_type}"

        # Check if we have a handler for this resource type
        if resource_type_key in self.handlers:
            return self.handlers[resource_type_key]

        # Log warning and return None
        self.logger.warning(f"No handler available for resource type: {resource_type_key}")
        return None

    def register_handler(self, resource_type: str, handler: BaseResourceHandler) -> None:
        """
        Register a custom handler for a resource type.

        Args:
            resource_type: Resource type to register handler for.
            handler: Handler instance.
        """
        self.handlers[resource_type] = handler
        self.logger.info(f"Registered custom handler for {resource_type}: {handler.__class__.__name__}")

    def get_supported_resource_types(self) -> list:
        """
        Get a list of supported resource types.

        Returns:
            List of supported resource types.
        """
        return list(self.handlers.keys())
