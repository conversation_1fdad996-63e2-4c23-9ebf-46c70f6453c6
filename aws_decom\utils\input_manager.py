"""
Input management utilities for AWS Resource Decommissioning Tool.
"""

import csv
import json
import logging
import os
from typing import List, Optional

class InputManager:
    """
    Manages input files for AWS resource decommissioning.
    
    Loads ARNs from various file formats.
    """
    
    def __init__(self):
        """Initialize the input manager."""
        self.logger = logging.getLogger(__name__)
    
    def load_arns_from_file(self, file_path: str) -> List[str]:
        """
        Load ARNs from a file.
        
        Supports CSV, JSON, and plain text files.
        
        Args:
            file_path: Path to the file.
            
        Returns:
            List of ARNs.
        """
        # Check if file exists
        if not os.path.isfile(file_path):
            self.logger.error(f"File not found: {file_path}")
            return []
        
        # Determine file type based on extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        if ext == '.csv':
            return self._load_arns_from_csv(file_path)
        elif ext == '.json':
            return self._load_arns_from_json(file_path)
        elif ext == '.txt':
            return self._load_arns_from_text(file_path)
        else:
            self.logger.warning(f"Unsupported file type: {ext}. Trying as CSV.")
            return self._load_arns_from_csv(file_path)
    
    def _load_arns_from_csv(self, file_path: str) -> List[str]:
        """
        Load ARNs from a CSV file.
        
        Args:
            file_path: Path to the CSV file.
            
        Returns:
            List of ARNs.
        """
        arns = []
        
        try:
            with open(file_path, 'r') as csvfile:
                # Try to determine if file has headers
                sample = csvfile.read(1024)
                csvfile.seek(0)
                
                has_header = csv.Sniffer().has_header(sample)
                reader = csv.reader(csvfile)
                
                # Skip header if present
                if has_header:
                    next(reader)
                
                # Read ARNs from first column
                for row in reader:
                    if row and row[0].strip():
                        arns.append(row[0].strip())
        
        except Exception as e:
            self.logger.error(f"Error loading ARNs from CSV file {file_path}: {e}")
        
        self.logger.info(f"Loaded {len(arns)} ARNs from CSV file {file_path}")
        return arns
    
    def _load_arns_from_json(self, file_path: str) -> List[str]:
        """
        Load ARNs from a JSON file.
        
        Supports various JSON formats:
        - List of strings
        - List of objects with 'arn' field
        - Object with 'arns' field containing a list
        
        Args:
            file_path: Path to the JSON file.
            
        Returns:
            List of ARNs.
        """
        arns = []
        
        try:
            with open(file_path, 'r') as jsonfile:
                data = json.load(jsonfile)
                
                # Handle different JSON formats
                if isinstance(data, list):
                    # List of strings or objects
                    for item in data:
                        if isinstance(item, str):
                            arns.append(item.strip())
                        elif isinstance(item, dict) and 'arn' in item:
                            arns.append(item['arn'].strip())
                
                elif isinstance(data, dict):
                    # Object with 'arns' field
                    if 'arns' in data and isinstance(data['arns'], list):
                        for item in data['arns']:
                            if isinstance(item, str):
                                arns.append(item.strip())
                            elif isinstance(item, dict) and 'arn' in item:
                                arns.append(item['arn'].strip())
        
        except Exception as e:
            self.logger.error(f"Error loading ARNs from JSON file {file_path}: {e}")
        
        self.logger.info(f"Loaded {len(arns)} ARNs from JSON file {file_path}")
        return arns
    
    def _load_arns_from_text(self, file_path: str) -> List[str]:
        """
        Load ARNs from a plain text file.
        
        Args:
            file_path: Path to the text file.
            
        Returns:
            List of ARNs.
        """
        arns = []
        
        try:
            with open(file_path, 'r') as textfile:
                for line in textfile:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        arns.append(line)
        
        except Exception as e:
            self.logger.error(f"Error loading ARNs from text file {file_path}: {e}")
        
        self.logger.info(f"Loaded {len(arns)} ARNs from text file {file_path}")
        return arns
