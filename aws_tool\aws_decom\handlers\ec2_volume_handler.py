"""
EC2 Volume handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EC2VolumeHandler(BaseResourceHandler):
    """
    Handler for EC2 volumes.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EC2 volume handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EC2 volume exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if volume exists
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            
            # Check if there are any volumes
            if not response.get('Volumes'):
                return False, f"Volume {resource.resource_id} not found"
            
            # Get volume state
            volume = response['Volumes'][0]
            state = volume.get('State')
            
            # Check if volume is already deleted
            if state == 'deleted':
                return False, f"Volume {resource.resource_id} is already deleted"
            
            # Check if volume is in use
            if state == 'in-use':
                # Check if we can force detach
                force_detach = self.config.get("resources.services.ec2.force_detach_volumes", False)
                if not force_detach:
                    return False, f"Volume {resource.resource_id} is in use and force_detach_volumes is not enabled"
            
            return True, f"Volume {resource.resource_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EC2 volume")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EC2 volume deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Get volume details
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            
            if not response.get('Volumes'):
                return dependencies
            
            volume = response['Volumes'][0]
            
            # Check for attached instances
            for attachment in volume.get('Attachments', []):
                instance_id = attachment.get('InstanceId')
                if instance_id:
                    dependencies.append(ResourceDependency(
                        resource_type="ec2:instance",
                        resource_id=instance_id,
                        relationship="attached_to",
                        blocking=True  # Blocking because volume must be detached first
                    ))
            
            # Check for snapshots (not blocking)
            try:
                snapshot_response = ec2.describe_snapshots(Filters=[
                    {'Name': 'volume-id', 'Values': [resource.resource_id]}
                ])
                
                for snapshot in snapshot_response.get('Snapshots', []):
                    snapshot_id = snapshot.get('SnapshotId')
                    if snapshot_id:
                        dependencies.append(ResourceDependency(
                            resource_type="ec2:snapshot",
                            resource_id=snapshot_id,
                            relationship="created_from",
                            blocking=False  # Not blocking because snapshots can exist independently
                        ))
            except ClientError:
                # Ignore errors from snapshot API
                pass
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EC2 volume {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EC2 volume deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if volume is in use
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            if not response.get('Volumes'):
                return False, f"Volume {resource.resource_id} not found"
            
            volume = response['Volumes'][0]
            state = volume.get('State')
            
            if state == 'in-use':
                # Check if we can force detach
                force_detach = self.config.get("resources.services.ec2.force_detach_volumes", False)
                if not force_detach:
                    return False, f"Volume {resource.resource_id} is in use and force_detach_volumes is not enabled"
                else:
                    # Simulate detaching
                    for attachment in volume.get('Attachments', []):
                        instance_id = attachment.get('InstanceId')
                        device = attachment.get('Device')
                        self.logger.info(f"Dry run: Would detach volume {resource.resource_id} from instance {instance_id} at device {device}")
            
            # Try to delete with DryRun=True
            ec2.delete_volume(
                VolumeId=resource.resource_id,
                DryRun=True
            )
            
            # If we get here, something went wrong (DryRun should raise an exception)
            return False, "Unexpected success in dry run mode"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of EC2 volume")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an EC2 volume.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if volume is in use
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            if not response.get('Volumes'):
                return False, f"Volume {resource.resource_id} not found"
            
            volume = response['Volumes'][0]
            state = volume.get('State')
            
            if state == 'in-use':
                # Check if we can force detach
                force_detach = self.config.get("resources.services.ec2.force_detach_volumes", False)
                if not force_detach:
                    return False, f"Volume {resource.resource_id} is in use and force_detach_volumes is not enabled"
                else:
                    # Detach all attachments
                    for attachment in volume.get('Attachments', []):
                        instance_id = attachment.get('InstanceId')
                        device = attachment.get('Device')
                        
                        self.logger.info(f"Detaching volume {resource.resource_id} from instance {instance_id} at device {device}")
                        
                        ec2.detach_volume(
                            VolumeId=resource.resource_id,
                            InstanceId=instance_id,
                            Device=device,
                            Force=True
                        )
                    
                    # Wait for volume to become available
                    waiter = ec2.get_waiter('volume_available')
                    try:
                        waiter.wait(
                            VolumeIds=[resource.resource_id],
                            WaiterConfig={
                                'Delay': 5,
                                'MaxAttempts': 12  # Wait up to 1 minute
                            }
                        )
                    except ClientError as e:
                        return False, f"Error waiting for volume to become available: {str(e)}"
            
            # Delete the volume
            ec2.delete_volume(VolumeId=resource.resource_id)
            
            return True, f"Volume {resource.resource_id} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting EC2 volume")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an EC2 volume.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            
            if not response.get('Volumes'):
                return None
            
            volume = response['Volumes'][0]
            create_time = volume.get('CreateTime')
            
            if create_time:
                return create_time.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for EC2 volume {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EC2 volume.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_volumes(VolumeIds=[resource.resource_id])
            
            if not response.get('Volumes'):
                return {}
            
            volume = response['Volumes'][0]
            tags = volume.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EC2 volume {resource.resource_id}: {e}")
            return {}
