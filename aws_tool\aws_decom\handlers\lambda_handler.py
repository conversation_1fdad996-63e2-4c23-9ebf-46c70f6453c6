"""
Lambda function handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class LambdaFunctionHandler(BaseResourceHandler):
    """
    Handler for Lambda functions.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the Lambda function handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a Lambda function exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            # Check if function exists
            function = lambda_client.get_function(FunctionName=resource.resource_id)
            
            # Check if function is being used by other services
            # This is a complex check and might require checking multiple services
            
            return True, f"Lambda function {resource.resource_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating Lambda function")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent Lambda function deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            # Check for event source mappings
            try:
                mappings = lambda_client.list_event_source_mappings(FunctionName=resource.resource_id)
                for mapping in mappings.get('EventSourceMappings', []):
                    mapping_id = mapping.get('UUID')
                    source_arn = mapping.get('EventSourceArn')
                    
                    if mapping_id and source_arn:
                        dependencies.append(ResourceDependency(
                            resource_type="lambda:event-source-mapping",
                            resource_id=mapping_id,
                            relationship="triggered_by",
                            blocking=True  # Blocking because mapping must be deleted first
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking event source mappings: {e}")
            
            # Check for CloudWatch Events rules
            try:
                events_client = session.client('events', region_name=resource.region)
                rules = events_client.list_rules()
                
                for rule in rules.get('Rules', []):
                    rule_name = rule.get('Name')
                    
                    # Check if this rule targets our Lambda function
                    targets = events_client.list_targets_by_rule(Rule=rule_name)
                    for target in targets.get('Targets', []):
                        if target.get('Arn') == resource.full_arn:
                            dependencies.append(ResourceDependency(
                                resource_type="events:rule",
                                resource_id=rule_name,
                                relationship="triggered_by",
                                blocking=True  # Blocking because rule must be updated first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudWatch Events rules: {e}")
            
            # Check for API Gateway integrations
            try:
                apigw_client = session.client('apigateway', region_name=resource.region)
                apis = apigw_client.get_rest_apis()
                
                for api in apis.get('items', []):
                    api_id = api.get('id')
                    
                    # Get resources for this API
                    resources = apigw_client.get_resources(restApiId=api_id)
                    
                    for res in resources.get('items', []):
                        resource_id = res.get('id')
                        
                        # Check methods for this resource
                        for method in res.get('resourceMethods', {}).keys():
                            try:
                                integration = apigw_client.get_integration(
                                    restApiId=api_id,
                                    resourceId=resource_id,
                                    httpMethod=method
                                )
                                
                                # Check if integration points to our Lambda function
                                uri = integration.get('uri', '')
                                if resource.resource_id in uri:
                                    dependencies.append(ResourceDependency(
                                        resource_type="apigateway:integration",
                                        resource_id=f"{api_id}/{resource_id}/{method}",
                                        relationship="integrated_with",
                                        blocking=True  # Blocking because integration must be updated first
                                    ))
                            except ClientError:
                                # Integration might not exist
                                pass
            except ClientError as e:
                self.logger.warning(f"Error checking API Gateway integrations: {e}")
            
            # Check for CloudFormation stacks
            try:
                cfn_client = session.client('cloudformation', region_name=resource.region)
                stacks = cfn_client.list_stacks(StackStatusFilter=[
                    'CREATE_COMPLETE', 'UPDATE_COMPLETE', 'UPDATE_ROLLBACK_COMPLETE'
                ])
                
                for stack in stacks.get('StackSummaries', []):
                    stack_name = stack.get('StackName')
                    
                    # Get resources for this stack
                    resources = cfn_client.list_stack_resources(StackName=stack_name)
                    
                    for res in resources.get('StackResourceSummaries', []):
                        if res.get('ResourceType') == 'AWS::Lambda::Function' and res.get('PhysicalResourceId') == resource.resource_id:
                            dependencies.append(ResourceDependency(
                                resource_type="cloudformation:stack",
                                resource_id=stack_name,
                                relationship="managed_by",
                                blocking=True  # Blocking because stack manages the resource
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudFormation stacks: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for Lambda function {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of Lambda function deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            # Check if function exists
            lambda_client.get_function(FunctionName=resource.resource_id)
            
            # Check for dependencies
            if resource.dependencies:
                dependency_str = ", ".join(str(dep) for dep in resource.dependencies)
                self.logger.info(f"Dry run: Function {resource.resource_id} has dependencies: {dependency_str}")
            
            self.logger.info(f"Dry run: Would delete Lambda function {resource.resource_id}")
            return True, f"Lambda function {resource.resource_id} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of Lambda function")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a Lambda function.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            # Delete the function
            lambda_client.delete_function(FunctionName=resource.resource_id)
            
            return True, f"Lambda function {resource.resource_id} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting Lambda function")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of a Lambda function.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            response = lambda_client.get_function(FunctionName=resource.resource_id)
            
            # Lambda doesn't provide creation date directly, but LastModified is close
            last_modified = response.get('Configuration', {}).get('LastModified')
            
            if last_modified:
                return last_modified
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for Lambda function {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a Lambda function.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            lambda_client = session.client('lambda', region_name=resource.region)
            
            response = lambda_client.list_tags(Resource=resource.full_arn)
            
            return response.get('Tags', {})
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for Lambda function {resource.resource_id}: {e}")
            return {}
