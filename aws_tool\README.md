# AWS Resource Decommissioning Tool

A comprehensive tool for safely decommissioning AWS resources across multiple accounts and regions.

## Features

- Support for multiple AWS resource types
- Dependency detection and visualization
- Dry run mode for safe validation
- Detailed reporting in CSV and JSON formats
- Colored progress bars for operation tracking

## Supported Resource Types

- EC2: instances, volumes, snapshots, AMIs, security groups
- ELB: load balancers, target groups, listeners, listener rules
- S3: buckets
- Lambda: functions
- CloudWatch: alarms
- Secrets Manager: secrets
- SNS: topics, subscriptions
- Step Functions: state machines
- EventBridge: rules
- CloudFormation: stacks
- SSM: parameters, documents
- AWS Backup: vaults, plans

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

## Usage

```bash
# Dry run mode (no resources will be deleted)
python aws_decom_tool.py --input resources.txt --cr-no GECHG1234567 --dry-run

# Deletion mode (resources will be permanently deleted)
python aws_decom_tool.py --input resources.txt --cr-no GECHG1234567 --delete

# Force deletion mode (delete resources with dependencies)
python aws_decom_tool.py --input resources.txt --cr-no GECHG1234567 --delete --force

# Direct deletion mode (skip validation and dry-run checks - USE WITH EXTREME CAUTION)
python aws_decom_tool.py --input resources.txt --cr-no GECHG1234567 --delete --skip-checks
```

## Directory Structure

- `aws_decom/`: Main package code
- `config/`: Configuration files
- `logs/`: Log files
- `reports/`: Generated reports

## Configuration

The tool can be configured using the following files:

- `config/config.yaml`: General configuration
- `config/logging_config.yaml`: Logging configuration

## AWS Profiles

The tool expects AWS profiles to be named with the format `support-{account_id}`, e.g., `support-************`.

## Reports

Reports are generated in the `reports/` directory with the following naming convention:

- Dry run: `decom_report_DRY_RUN_{cr_number}_{timestamp}.csv/json`
- Deletion: `decom_report_DELETE_{cr_number}_{timestamp}.csv/json`
