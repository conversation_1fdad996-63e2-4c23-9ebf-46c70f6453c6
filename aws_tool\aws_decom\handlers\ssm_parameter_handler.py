"""
SSM Parameter handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class SSMParameterHandler(BaseResourceHandler):
    """
    Handler for SSM Parameters.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the SSM Parameter handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an SSM Parameter exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract parameter name from resource ID
            param_name = resource.resource_id
            
            # Check if parameter exists
            response = ssm.get_parameter(Name=param_name)
            
            if response and 'Parameter' in response:
                return True, f"SSM Parameter {param_name} exists and can be deleted"
            else:
                return False, f"SSM Parameter {param_name} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating SSM Parameter")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of an SSM Parameter.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        # SSM Parameters typically don't have dependencies that would prevent deletion
        # However, we could check for references in CloudFormation stacks or other resources
        # that might be using the parameter
        
        return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of SSM Parameter deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract parameter name from resource ID
            param_name = resource.resource_id
            
            # Check if parameter exists
            response = ssm.get_parameter(Name=param_name)
            
            if response and 'Parameter' in response:
                # Check if parameter is a secure string
                param_type = response['Parameter'].get('Type', '')
                if param_type == 'SecureString':
                    # Check if we have permission to delete secure strings
                    can_delete_secure = self.config.get("resources.services.ssm.delete_secure_parameters", True)
                    if not can_delete_secure:
                        return False, f"SSM Parameter {param_name} is a SecureString and delete_secure_parameters is not enabled"
                
                return True, f"Dry run successful: Would delete SSM Parameter {param_name}"
            else:
                return False, f"SSM Parameter {param_name} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of SSM Parameter")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an SSM Parameter.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract parameter name from resource ID
            param_name = resource.resource_id
            
            # Check if parameter exists and get its type
            try:
                response = ssm.get_parameter(Name=param_name)
                
                # Check if parameter is a secure string
                if response and 'Parameter' in response:
                    param_type = response['Parameter'].get('Type', '')
                    if param_type == 'SecureString':
                        # Check if we have permission to delete secure strings
                        can_delete_secure = self.config.get("resources.services.ssm.delete_secure_parameters", True)
                        if not can_delete_secure:
                            return False, f"SSM Parameter {param_name} is a SecureString and delete_secure_parameters is not enabled"
            except ClientError as e:
                return self.handle_client_error(e, "checking SSM Parameter type")
            
            # Delete the parameter
            ssm.delete_parameter(Name=param_name)
            
            return True, f"Successfully deleted SSM Parameter {param_name}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of SSM Parameter")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of an SSM Parameter.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        # SSM Parameters don't have a creation date available through the API
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an SSM Parameter.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract parameter name from resource ID
            param_name = resource.resource_id
            
            # Get parameter ARN
            try:
                response = ssm.get_parameter(Name=param_name)
                if not response or 'Parameter' not in response:
                    return {}
                
                # Construct the ARN if not provided in the response
                if 'ARN' not in response['Parameter']:
                    sts = session.client('sts')
                    account_id = sts.get_caller_identity()['Account']
                    param_arn = f"arn:aws:ssm:{resource.region}:{account_id}:parameter/{param_name.lstrip('/')}"
                else:
                    param_arn = response['Parameter']['ARN']
                
                # Get tags for the parameter
                tags_response = ssm.list_tags_for_resource(ResourceType='Parameter', ResourceId=param_arn)
                
                tags = {}
                for tag in tags_response.get('TagList', []):
                    tags[tag['Key']] = tag['Value']
                
                return tags
            
            except ClientError as e:
                self.logger.warning(f"Error getting parameter ARN: {e}")
                return {}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for SSM Parameter {resource.resource_id}: {e}")
            return {}
