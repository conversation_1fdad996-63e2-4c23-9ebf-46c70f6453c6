# AWS Resource Decommissioning Tool - API Reference

## Core Module

### ConfigManager

```python
class ConfigManager:
    """
    Manages configuration for the AWS Resource Decommissioning Tool.
    
    Loads configuration from YAML files and environment variables.
    """
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file.
        """
        
    def get(self, path: str, default: Any = None) -> Any:
        """
        Get a configuration value by path.
        
        Args:
            path: Dot-separated path to the configuration value.
            default: Default value to return if path is not found.
            
        Returns:
            Configuration value or default.
        """
        
    def get_all(self) -> Dict[str, Any]:
        """
        Get the entire configuration.
        
        Returns:
            Dict containing the entire configuration.
        """
```

### SessionManager

```python
class SessionManager:
    """
    Manages AWS sessions for different accounts.
    
    Creates and caches boto3 sessions for different AWS accounts.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the session manager.
        
        Args:
            config_manager: Configuration manager instance.
        """
        
    def get_session(self, account_id: str, region: Optional[str] = None) -> Optional[boto3.Session]:
        """
        Get a boto3 session for the specified account.
        
        Args:
            account_id: AWS account ID.
            region: AWS region (optional, uses default from config if not specified).
            
        Returns:
            boto3.Session or None if session creation fails.
        """
        
    def get_account_name(self, session: boto3.Session) -> str:
        """
        Get the account name (alias) for the specified session.
        
        Args:
            session: boto3 session.
            
        Returns:
            Account name or "Unknown" if not found.
        """
        
    def get_client(self, session: boto3.Session, service: str, region: Optional[str] = None) -> boto3.client:
        """
        Get a boto3 client for the specified service.
        
        Args:
            session: boto3 session.
            service: AWS service name.
            region: AWS region (optional, uses session region if not specified).
            
        Returns:
            boto3 client.
        """
```

### ResourceManager

```python
class ResourceManager:
    """
    Manages AWS resources for decommissioning.
    
    Orchestrates the validation, dry run, and deletion of AWS resources.
    """
    
    def __init__(self, config_manager: ConfigManager, session_manager: SessionManager):
        """
        Initialize the resource manager.
        
        Args:
            config_manager: Configuration manager instance.
            session_manager: Session manager instance.
        """
        
    def create_resource_from_arn(self, arn: str, cr_number: str) -> Optional[Resource]:
        """
        Create a resource object from an ARN.
        
        Args:
            arn: ARN to create resource from.
            cr_number: Change request number.
            
        Returns:
            Resource object or None if ARN is invalid.
        """
        
    def validate_resource(self, resource: Resource) -> bool:
        """
        Validate a resource.
        
        Args:
            resource: Resource to validate.
            
        Returns:
            True if validation succeeded, False otherwise.
        """
        
    def dry_run_deletion(self, resource: Resource) -> bool:
        """
        Perform a dry run of resource deletion.
        
        Args:
            resource: Resource to delete.
            
        Returns:
            True if dry run succeeded, False otherwise.
        """
        
    def delete_resource(self, resource: Resource) -> bool:
        """
        Delete a resource.
        
        Args:
            resource: Resource to delete.
            
        Returns:
            True if deletion succeeded, False otherwise.
        """
        
    def process_resources(self, arns: List[str], cr_number: str, dry_run: bool = True) -> DecommissioningReport:
        """
        Process a list of resources.
        
        Args:
            arns: List of ARNs to process.
            cr_number: Change request number.
            dry_run: Whether to perform a dry run or actual deletion.
            
        Returns:
            DecommissioningReport containing results.
        """
```

### Validator

```python
class Validator:
    """
    Validates inputs for AWS resource decommissioning.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the validator.
        
        Args:
            config_manager: Configuration manager instance.
        """
        
    def validate_cr_number(self, cr_number: str) -> Tuple[bool, str]:
        """
        Validate a change request number.
        
        Args:
            cr_number: Change request number to validate.
            
        Returns:
            Tuple of (valid, message).
        """
        
    def validate_arns(self, arns: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate a list of ARNs.
        
        Args:
            arns: List of ARNs to validate.
            
        Returns:
            Tuple of (valid_arns, invalid_arns).
        """
        
    def validate_supported_resource_types(self, arns: List[str], supported_types: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate that ARNs are of supported resource types.
        
        Args:
            arns: List of ARNs to validate.
            supported_types: List of supported resource types.
            
        Returns:
            Tuple of (supported_arns, unsupported_arns).
        """
```

## Handlers Module

### BaseResourceHandler

```python
class BaseResourceHandler(ABC):
    """
    Base class for AWS resource handlers.
    
    Provides common functionality for resource handlers.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the resource handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        
    @abstractmethod
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a resource exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        
    @abstractmethod
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        
    @abstractmethod
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of resource deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        
    @abstractmethod
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a resource.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a resource.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a resource.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        
    def handle_client_error(self, e: ClientError, operation: str) -> Tuple[bool, str]:
        """
        Handle a ClientError from boto3.
        
        Args:
            e: ClientError exception.
            operation: Operation being performed.
            
        Returns:
            Tuple of (success, message).
        """
```

### ResourceHandlerFactory

```python
class ResourceHandlerFactory:
    """
    Factory for creating resource handlers.
    
    Creates appropriate resource handlers based on resource type.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the resource handler factory.
        
        Args:
            config_manager: Configuration manager instance.
        """
        
    def get_handler(self, resource: Resource) -> Optional[BaseResourceHandler]:
        """
        Get a handler for the specified resource.
        
        Args:
            resource: Resource to get handler for.
            
        Returns:
            Resource handler or None if no handler is available.
        """
        
    def register_handler(self, resource_type: str, handler: BaseResourceHandler) -> None:
        """
        Register a custom handler for a resource type.
        
        Args:
            resource_type: Resource type to register handler for.
            handler: Handler instance.
        """
        
    def get_supported_resource_types(self) -> list:
        """
        Get a list of supported resource types.
        
        Returns:
            List of supported resource types.
        """
```

## Models Module

### Resource

```python
@dataclass
class ResourceDependency:
    """
    Represents a dependency between AWS resources.
    """
    resource_type: str
    resource_id: str
    relationship: str  # e.g., "attached_to", "contains", "references"
    blocking: bool = True  # Whether this dependency blocks deletion

@dataclass
class Resource:
    """
    Represents an AWS resource to be decommissioned.
    """
    # Resource identification
    service: str
    resource_type: str
    resource_id: str
    region: str
    account_id: str
    full_arn: str
    
    # Resource metadata
    created_date: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    # Dependencies
    dependencies: List[ResourceDependency] = field(default_factory=list)
    
    # Operation status
    validated: bool = False
    validation_message: Optional[str] = None
    
    dry_run_success: Optional[bool] = None
    dry_run_message: Optional[str] = None
    
    deletion_success: Optional[bool] = None
    deletion_message: Optional[str] = None
    deleted_date: Optional[str] = None
    
    # Performance metrics
    validation_duration: Optional[float] = None
    dry_run_duration: Optional[float] = None
    deletion_duration: Optional[float] = None
    
    # Change management
    cr_number: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the resource to a dictionary for reporting.
        
        Returns:
            Dict representation of the resource.
        """
        
    def has_blocking_dependencies(self) -> bool:
        """
        Check if the resource has blocking dependencies.
        
        Returns:
            True if the resource has blocking dependencies, False otherwise.
        """
```

### DecommissioningReport

```python
@dataclass
class DecommissioningReport:
    """
    Represents a report of AWS resource decommissioning operations.
    """
    cr_number: str
    timestamp: datetime = field(default_factory=datetime.now)
    resources: List[Resource] = field(default_factory=list)
    
    # Summary statistics
    total_resources: int = 0
    validated_resources: int = 0
    dry_run_success: int = 0
    dry_run_failed: int = 0
    deletion_success: int = 0
    deletion_failed: int = 0
    resources_with_dependencies: int = 0
    
    def add_resource(self, resource: Resource) -> None:
        """
        Add a resource to the report and update statistics.
        
        Args:
            resource: Resource to add.
        """
        
    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the report.
        
        Returns:
            Dict containing report summary.
        """
        
    def save_csv(self, output_dir: str) -> str:
        """
        Save the report as a CSV file.
        
        Args:
            output_dir: Directory to save the report.
            
        Returns:
            Path to the saved report.
        """
        
    def save_json(self, output_dir: str) -> str:
        """
        Save the report as a JSON file.
        
        Args:
            output_dir: Directory to save the report.
            
        Returns:
            Path to the saved report.
        """
        
    def get_resources_by_service(self) -> Dict[str, List[Resource]]:
        """
        Group resources by service.
        
        Returns:
            Dict mapping service names to lists of resources.
        """
        
    def get_resources_by_account(self) -> Dict[str, List[Resource]]:
        """
        Group resources by account.
        
        Returns:
            Dict mapping account IDs to lists of resources.
        """
```

## Utils Module

### ArnParser

```python
class ArnParser:
    """
    Parser for AWS ARNs (Amazon Resource Names).
    """
    
    def __init__(self):
        """Initialize the ARN parser."""
        
    def parse_arn(self, arn: str) -> Dict[str, Any]:
        """
        Parse an AWS ARN into its components.
        
        Args:
            arn: The ARN to parse.
            
        Returns:
            Dict containing parsed ARN components:
            - service: AWS service (e.g., 'ec2', 's3')
            - resource_type: Type of resource (e.g., 'instance', 'bucket')
            - region: AWS region
            - account_id: AWS account ID
            - resource_id: Resource identifier
            - full_arn: The original ARN
        """
        
    def is_valid_arn(self, arn: str) -> bool:
        """
        Check if an ARN is valid.
        
        Args:
            arn: The ARN to check.
            
        Returns:
            True if the ARN is valid, False otherwise.
        """
```

### InputManager

```python
class InputManager:
    """
    Manages input files for AWS resource decommissioning.
    
    Loads ARNs from various file formats.
    """
    
    def __init__(self):
        """Initialize the input manager."""
        
    def load_arns_from_file(self, file_path: str) -> List[str]:
        """
        Load ARNs from a file.
        
        Supports CSV, JSON, and plain text files.
        
        Args:
            file_path: Path to the file.
            
        Returns:
            List of ARNs.
        """
```

### DependencyGraph

```python
class DependencyGraph:
    """
    Creates and visualizes dependency graphs for AWS resources.
    """
    
    def __init__(self, output_dir: str = "reports"):
        """
        Initialize the dependency graph.
        
        Args:
            output_dir: Directory to save the graph.
        """
        
    def create_graph(self, resources: List[Resource], cr_number: str) -> Optional[str]:
        """
        Create a dependency graph for the specified resources.
        
        Args:
            resources: List of resources to include in the graph.
            cr_number: Change request number.
            
        Returns:
            Path to the generated graph file or None if generation failed.
        """
        
    def create_service_graph(self, resources: List[Resource], cr_number: str) -> Optional[str]:
        """
        Create a service-level dependency graph.
        
        This graph shows dependencies between services rather than individual resources.
        
        Args:
            resources: List of resources to include in the graph.
            cr_number: Change request number.
            
        Returns:
            Path to the generated graph file or None if generation failed.
        """
```

## CLI Module

```python
class CLI:
    """
    Command-line interface for AWS Resource Decommissioning Tool.
    """
    
    def __init__(self):
        """Initialize the CLI."""
        
    def parse_args(self) -> argparse.Namespace:
        """
        Parse command-line arguments.
        
        Returns:
            Parsed arguments.
        """
        
    def run(self) -> int:
        """
        Run the CLI.
        
        Returns:
            Exit code.
        """

def main():
    """Main entry point for the CLI."""
```
