#!/usr/bin/env python3

import boto3
import botocore
import csv
import os
import sys
import argparse
import certifi
import logging
from datetime import datetime
from tqdm import tqdm
from colorama import Fore, Style, init
from tabulate import tabulate
import time

# Initialize colorama
init(autoreset=True)

# Constants
VERSION = "1.2"  # Version updated
SUPPORTED_RESOURCES = {
    "ec2": ["instance", "volume", "snapshot", "ami", "security-group"],
    "s3": ["bucket", "object"],
    "lambda": ["function"],
    "cloudwatch": ["alarm"],
    "secretsmanager": ["secret"],
    "sns": ["topic", "subscription"],
    "states": ["stateMachine"],
    "cloudformation": ["stack"],
    "ssm": ["parameter"],
    "elasticloadbalancing": ["load-balancer", "target-group", "listener", "rule"],
    "events": ["rule"],
}
SERVICE_COLORS = {
    "ec2": Fore.<PERSON>YAN,
    "s3": Fore.BLUE,
    "lambda": Fore.YELLOW,
    "cloudwatch": Fore.MAGENTA,
    "secretsmanager": Fore.GREEN,
    "sns": Fore.LIGHTBLUE_EX,
    "states": Fore.LIGHTCYAN_EX,
    "cloudformation": Fore.LIGHTMAGENTA_EX,
    "ssm": Fore.LIGHTGREEN_EX,
    "elasticloadbalancing": Fore.LIGHTRED_EX,
    "events": Fore.LIGHTYELLOW_EX,
}

# Global settings
os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()

# Ensure output directories exist
os.makedirs('logs', exist_ok=True)
os.makedirs('results', exist_ok=True)

# Setup logging
timestamp = datetime.now().strftime('%y_%m_%d_%H%M%S')
logfile = f"logs/runtime_log_{timestamp}.log"
logging.basicConfig(filename=logfile, level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')

# Helper Functions
def log(msg):
    print(msg)
    logging.info(msg)

def parse_arn(arn):
    parts = arn.split(":")
    service = parts[2]
    region = parts[3]
    account_id = parts[4]
    resource = ":".join(parts[5:])
    return service, region, account_id, resource

def get_session(profile_name):
    try:
        return boto3.session.Session(profile_name=profile_name)
    except Exception as e:
        log(f"{Fore.RED}Session Error: {e}")
        sys.exit(1)

def get_account_name(session):
    try:
        client = session.client('iam')
        aliases = client.list_account_aliases()
        return aliases['AccountAliases'][0] if aliases['AccountAliases'] else "Unknown"
    except Exception:
        return "Unknown"

def get_created_date(session, service, resource_type, resource_id, region):
    try:
        if service == 'ec2':
            ec2 = session.client('ec2', region_name=region, verify=False)
            if resource_type == 'instance':
                resp = ec2.describe_instances(InstanceIds=[resource_id])
                return resp['Reservations'][0]['Instances'][0]['LaunchTime'].strftime('%Y-%m-%d %H:%M:%S')
            elif resource_type == 'volume':
                resp = ec2.describe_volumes(VolumeIds=[resource_id])
                return resp['Volumes'][0]['CreateTime'].strftime('%Y-%m-%d %H:%M:%S')
            elif resource_type == 'snapshot':
                resp = ec2.describe_snapshots(SnapshotIds=[resource_id])
                return resp['Snapshots'][0]['StartTime'].strftime('%Y-%m-%d %H:%M:%S')
            elif resource_type == 'ami':
                resp = ec2.describe_images(ImageIds=[resource_id])
                return resp['Images'][0]['CreationDate']
        elif service == 'lambda':
            lam = session.client('lambda', region_name=region, verify=False)
            resp = lam.get_function(FunctionName=resource_id)
            return resp['Configuration']['LastModified']
        elif service == 's3' and resource_type == 'bucket':
            s3 = session.client('s3', region_name=region, verify=False)
            resp = s3.get_bucket_location(Bucket=resource_id)
            return "Unknown"  # No direct way to get creation date from S3 API
    except Exception:
        return "Unknown"

    return "Unknown"

def validate_or_delete(session, service, resource_type, resource_id, region, dry_run=False):
    try:
        if service == 'ec2':
            ec2 = session.client('ec2', region_name=region, verify=False)
            if resource_type == 'instance':
                ec2.modify_instance_attribute(InstanceId=resource_id, DisableApiTermination={'Value': False})
                if dry_run:
                    ec2.describe_instances(InstanceIds=[resource_id], DryRun=True)
                else:
                    ec2.terminate_instances(InstanceIds=[resource_id])
            elif resource_type == 'volume':
                if dry_run:
                    ec2.describe_volumes(VolumeIds=[resource_id], DryRun=True)
                else:
                    ec2.delete_volume(VolumeId=resource_id)
            elif resource_type == 'snapshot':
                if dry_run:
                    ec2.describe_snapshots(SnapshotIds=[resource_id], DryRun=True)
                else:
                    ec2.delete_snapshot(SnapshotId=resource_id)
            elif resource_type == 'ami':
                if dry_run:
                    ec2.describe_images(ImageIds=[resource_id], DryRun=True)
                else:
                    ec2.deregister_image(ImageId=resource_id)
            elif resource_type == 'security-group':
                if dry_run:
                    ec2.describe_security_groups(GroupIds=[resource_id], DryRun=True)
                else:
                    ec2.delete_security_group(GroupId=resource_id)
        elif service == 's3':
            s3 = session.client('s3', region_name=region, verify=False)
            if resource_type == 'bucket':
                if dry_run:
                    s3.head_bucket(Bucket=resource_id)
                else:
                    s3.delete_bucket(Bucket=resource_id)
            elif resource_type == 'object':
                bucket, key = resource_id.split('/', 1)
                if dry_run:
                    s3.head_object(Bucket=bucket, Key=key)
                else:
                    s3.delete_object(Bucket=bucket, Key=key)
        elif service == 'lambda':
            lam = session.client('lambda', region_name=region, verify=False)
            if dry_run:
                lam.get_function(FunctionName=resource_id)
            else:
                lam.delete_function(FunctionName=resource_id)
        elif service == 'cloudwatch':
            cw = session.client('cloudwatch', region_name=region, verify=False)
            if dry_run:
                cw.describe_alarms(AlarmNames=[resource_id])
            else:
                cw.delete_alarms(AlarmNames=[resource_id])
        elif service == 'secretsmanager':
            sm = session.client('secretsmanager', region_name=region, verify=False)
            if dry_run:
                sm.describe_secret(SecretId=resource_id)
            else:
                sm.delete_secret(SecretId=resource_id, ForceDeleteWithoutRecovery=True)
        elif service == 'sns':
            sns = session.client('sns', region_name=region, verify=False)
            if resource_type == 'topic':
                if dry_run:
                    sns.get_topic_attributes(TopicArn=resource_id)
                else:
                    sns.delete_topic(TopicArn=resource_id)
            elif resource_type == 'subscription':
                if dry_run:
                    sns.get_subscription_attributes(SubscriptionArn=resource_id)
                else:
                    sns.unsubscribe(SubscriptionArn=resource_id)
        elif service == 'states':
            sf = session.client('stepfunctions', region_name=region, verify=False)
            if dry_run:
                sf.describe_state_machine(stateMachineArn=resource_id)
            else:
                sf.delete_state_machine(stateMachineArn=resource_id)
        elif service == 'cloudformation':
            cf = session.client('cloudformation', region_name=region, verify=False)
            if dry_run:
                cf.describe_stacks(StackName=resource_id)
            else:
                cf.delete_stack(StackName=resource_id)
        elif service == 'ssm':
            ssm = session.client('ssm', region_name=region, verify=False)
            if dry_run:
                ssm.get_parameter(Name=resource_id)
            else:
                ssm.delete_parameter(Name=resource_id)
        elif service == 'elasticloadbalancing':
            elb = session.client('elbv2', region_name=region, verify=False)
            if resource_type == 'load-balancer':
                if dry_run:
                    elb.describe_load_balancers(LoadBalancerArns=[resource_id])
                else:
                    elb.delete_load_balancer(LoadBalancerArn=resource_id)
            elif resource_type == 'target-group':
                if dry_run:
                    elb.describe_target_groups(TargetGroupArns=[resource_id])
                else:
                    elb.delete_target_group(TargetGroupArn=resource_id)
            elif resource_type == 'listener':
                if dry_run:
                    elb.describe_listeners(ListenerArns=[resource_id])
                else:
                    elb.delete_listener(ListenerArn=resource_id)
            elif resource_type == 'rule':
                if dry_run:
                    elb.describe_rules(RuleArns=[resource_id])
                else:
                    elb.delete_rule(RuleArn=resource_id)
        elif service == 'events':
            ev = session.client('events', region_name=region, verify=False)
            if dry_run:
                ev.describe_rule(Name=resource_id)
            else:
                ev.delete_rule(Name=resource_id, Force=True)
        return "Success", ""
    except botocore.exceptions.ClientError as e:
        return "Failed", str(e)

def main():
    parser = argparse.ArgumentParser(description="AWS Resource Cleanup Script")
    parser.add_argument('--input', required=True, help="CSV file containing ARNs")
    parser.add_argument('--dry-run', action='store_true', help="Dry run validation")
    parser.add_argument('--delete', action='store_true', help="Delete resources")
    args = parser.parse_args()

    cr_no = input("Enter the CR number (e.g., GECHG1234567): ").strip()
    timestamp2 = datetime.now().strftime('%y-%m-%d_%H-%M')
    output_file = f"results/AWS_RESOURCE_{'DRYRUN' if args.dry_run else 'DECOM'}_{cr_no}_{timestamp2}.csv"

    # Read Input
    resources = []
    with open(args.input) as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            arn = row[0]
            service, region, account_id, resource_id = parse_arn(arn)
            resources.append((service, region, account_id, resource_id, arn))

    # Summary
    summary = {}
    for service, *_ in resources:
        summary.setdefault(service, 0)
        summary[service] += 1

    table = []
    for service, count in summary.items():
        color = SERVICE_COLORS.get(service, Fore.WHITE)
        table.append([f"{color}{service}{Style.RESET_ALL}", count])
    print(tabulate(table, headers=["Service", "Count"]))

    proceed = input("\nProceed (Y/N)? ").strip().lower()
    if proceed != 'y':
        log("Operation cancelled.")
        sys.exit(0)

    # Processing
    with open(output_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["CR No", "Account Name", "Account ID", "Service", "Resource Type", "Resource_ID", "Launch/Created Date", "Deleted Date", "Status", "Error", "Duration(sec)"])
        with tqdm(total=len(resources)) as pbar:
            for service, region, account_id, resource_id, arn in resources:
                profile = f"support-{account_id}"
                session = get_session(profile)
                account_name = get_account_name(session)

                created_date = get_created_date(session, service, resource_id, resource_id, region)

                start = time.time()
                status, error = validate_or_delete(session, service, resource_id, resource_id, region, dry_run=args.dry_run)
                end = time.time()

                deleted_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S') if not args.dry_run else ""
                duration = round(end - start, 2)

                resource_type = "Unknown"
                for type_ in SUPPORTED_RESOURCES.get(service, []):
                    if type_ in resource_id.lower():
                        resource_type = type_
                        break

                writer.writerow([cr_no, account_name, account_id, service, resource_type, resource_id, created_date, deleted_date, status, error, duration])
                pbar.update(1)

    log(f"Completed. Results saved in {output_file}")

if __name__ == "__main__":
    main()
