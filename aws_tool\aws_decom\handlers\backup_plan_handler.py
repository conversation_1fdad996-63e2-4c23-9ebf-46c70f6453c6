"""
AWS Backup Plan handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class BackupPlanHandler(BaseResourceHandler):
    """
    Handler for AWS Backup Plans.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the Backup Plan handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a Backup Plan exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Check if plan exists
            response = backup.get_backup_plan(BackupPlanId=plan_id)
            
            if response and 'BackupPlan' in response:
                return True, f"Backup Plan {plan_id} exists and can be deleted"
            else:
                return False, f"Backup Plan {plan_id} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating Backup Plan")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a Backup Plan.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Check for selections (resources assigned to the plan)
            try:
                paginator = backup.get_paginator('list_backup_selections')
                page_iterator = paginator.paginate(BackupPlanId=plan_id)
                
                for page in page_iterator:
                    selections = page.get('BackupSelections', [])
                    
                    for selection in selections:
                        selection_id = selection.get('SelectionId', '')
                        selection_name = selection.get('SelectionName', 'Unknown')
                        
                        dependencies.append(ResourceDependency(
                            resource_type="backup:selection",
                            resource_id=f"{plan_id}:{selection_id}",
                            relationship="assigned_to",
                            blocking=False  # Not blocking because selections are deleted with the plan
                        ))
                        
                        # Try to get more details about the selection
                        try:
                            selection_response = backup.get_backup_selection(
                                BackupPlanId=plan_id,
                                SelectionId=selection_id
                            )
                            
                            selection_details = selection_response.get('BackupSelection', {})
                            resources = selection_details.get('Resources', [])
                            
                            # Add resources as dependencies
                            for resource_arn in resources:
                                # Extract resource type and ID from ARN
                                parts = resource_arn.split(':')
                                if len(parts) >= 6:
                                    service = parts[2]
                                    resource_type = parts[5].split('/')[0] if '/' in parts[5] else parts[5]
                                    resource_id = parts[5].split('/')[-1] if '/' in parts[5] else parts[5]
                                    
                                    dependencies.append(ResourceDependency(
                                        resource_type=f"{service}:{resource_type}",
                                        resource_id=resource_id,
                                        relationship="backs_up",
                                        blocking=False  # Not blocking because the plan can be deleted regardless
                                    ))
                        except ClientError as e:
                            self.logger.warning(f"Error getting details for selection {selection_id}: {e}")
            except ClientError as e:
                self.logger.warning(f"Error checking selections for plan {plan_id}: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for Backup Plan {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of Backup Plan deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Check if plan exists
            try:
                response = backup.get_backup_plan(BackupPlanId=plan_id)
                
                if not response or 'BackupPlan' not in response:
                    return False, f"Backup Plan {plan_id} not found"
            except ClientError as e:
                return self.handle_client_error(e, "checking if Backup Plan exists")
            
            # Check for selections
            selections = []
            try:
                paginator = backup.get_paginator('list_backup_selections')
                page_iterator = paginator.paginate(BackupPlanId=plan_id)
                
                for page in page_iterator:
                    selections.extend(page.get('BackupSelections', []))
            except ClientError as e:
                self.logger.warning(f"Error checking selections for plan {plan_id}: {e}")
            
            if selections:
                return True, f"Dry run successful: Would delete {len(selections)} selections and then delete Backup Plan {plan_id}"
            else:
                return True, f"Dry run successful: Would delete Backup Plan {plan_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of Backup Plan")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a Backup Plan.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Check if plan exists
            try:
                response = backup.get_backup_plan(BackupPlanId=plan_id)
                
                if not response or 'BackupPlan' not in response:
                    return False, f"Backup Plan {plan_id} not found"
                
                # Get the version ID
                version_id = response.get('VersionId')
            except ClientError as e:
                return self.handle_client_error(e, "checking if Backup Plan exists")
            
            # Delete all selections first
            try:
                paginator = backup.get_paginator('list_backup_selections')
                page_iterator = paginator.paginate(BackupPlanId=plan_id)
                
                for page in page_iterator:
                    selections = page.get('BackupSelections', [])
                    
                    for selection in selections:
                        selection_id = selection.get('SelectionId', '')
                        
                        if selection_id:
                            try:
                                backup.delete_backup_selection(
                                    BackupPlanId=plan_id,
                                    SelectionId=selection_id
                                )
                                self.logger.info(f"Deleted selection {selection_id} from plan {plan_id}")
                            except ClientError as e:
                                self.logger.error(f"Error deleting selection {selection_id}: {e}")
                                return False, f"Error deleting selection {selection_id}: {str(e)}"
            except ClientError as e:
                self.logger.warning(f"Error listing selections for plan {plan_id}: {e}")
                return False, f"Error listing selections for plan {plan_id}: {str(e)}"
            
            # Delete the plan
            backup.delete_backup_plan(BackupPlanId=plan_id)
            
            return True, f"Successfully deleted Backup Plan {plan_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of Backup Plan")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a Backup Plan.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Get plan details
            response = backup.get_backup_plan(BackupPlanId=plan_id)
            
            if response and 'BackupPlan' in response:
                created_date = response.get('CreationDate')
                
                if created_date:
                    return created_date.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation date for Backup Plan {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a Backup Plan.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            backup = session.client('backup', region_name=resource.region)
            
            # Extract plan ID from resource ID
            plan_id = resource.resource_id
            
            # Get plan ARN
            try:
                response = backup.get_backup_plan(BackupPlanId=plan_id)
                if not response:
                    return {}
                
                plan_arn = response.get('BackupPlanArn')
                if not plan_arn:
                    return {}
                
                # Get tags for the plan
                tags_response = backup.list_tags(ResourceArn=plan_arn)
                
                return tags_response.get('Tags', {})
                
            except ClientError as e:
                self.logger.warning(f"Error getting plan ARN: {e}")
                return {}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for Backup Plan {resource.resource_id}: {e}")
            return {}
