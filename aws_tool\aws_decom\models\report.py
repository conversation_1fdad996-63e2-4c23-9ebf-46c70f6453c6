"""
Report models for AWS Resource Decommissioning Tool.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import csv
import os
from .resource import Resource

@dataclass
class DecommissioningReport:
    """
    Represents a report of AWS resource decommissioning operations.
    """
    cr_number: str
    timestamp: datetime = field(default_factory=datetime.now)
    resources: List[Resource] = field(default_factory=list)
    is_dry_run: bool = True

    # Summary statistics
    total_resources: int = 0
    validated_resources: int = 0
    dry_run_success: int = 0
    dry_run_failed: int = 0
    deletion_success: int = 0
    deletion_failed: int = 0
    resources_with_dependencies: int = 0

    def add_resource(self, resource: Resource) -> None:
        """
        Add a resource to the report and update statistics.

        Args:
            resource: Resource to add.
        """
        self.resources.append(resource)
        self.total_resources += 1

        if resource.validated:
            self.validated_resources += 1

        if resource.dry_run_success is True:
            self.dry_run_success += 1
        elif resource.dry_run_success is False:
            self.dry_run_failed += 1

        if resource.deletion_success is True:
            self.deletion_success += 1
        elif resource.deletion_success is False:
            self.deletion_failed += 1

        if resource.dependencies:
            self.resources_with_dependencies += 1

    def get_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the report.

        Returns:
            Dict containing report summary.
        """
        operation_type = "Dry Run" if self.is_dry_run else "Deletion"
        return {
            "CR_Number": self.cr_number,
            "Operation_Type": operation_type,
            "Timestamp": self.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
            "Total_Resources": self.total_resources,
            "Validated_Resources": self.validated_resources,
            "Dry_Run_Success": self.dry_run_success,
            "Dry_Run_Failed": self.dry_run_failed,
            "Deletion_Success": self.deletion_success,
            "Deletion_Failed": self.deletion_failed,
            "Resources_With_Dependencies": self.resources_with_dependencies,
        }

    def save_csv(self, output_dir: str) -> str:
        """
        Save the report as a CSV file.

        Args:
            output_dir: Directory to save the report.

        Returns:
            Path to the saved report.
        """
        os.makedirs(output_dir, exist_ok=True)

        timestamp_str = self.timestamp.strftime("%Y%m%d_%H%M%S")
        operation_type = "DRY_RUN" if self.is_dry_run else "DELETE"
        filename = f"{output_dir}/decom_report_{operation_type}_{self.cr_number}_{timestamp_str}.csv"

        # Define the exact field order as specified
        fieldnames = [
            "CR_Number", "Account_Name", "Account_ID", "Service", "Resource_Type",
            "Resource_ID", "Region", "ARN", "Created_Date", "Validated",
            "Validation_Message", "Dry_Run_Success", "Dry_Run_Message",
            "Deletion_Success", "Deletion_Message", "Deleted_Date",
            "Dependencies", "Validation_Duration", "Dry_Run_Duration", "Deletion_Duration"
        ]

        with open(filename, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for resource in self.resources:
                writer.writerow(resource.to_dict())

        return filename

    def save_json(self, output_dir: str) -> str:
        """
        Save the report as a JSON file.

        Args:
            output_dir: Directory to save the report.

        Returns:
            Path to the saved report.
        """
        os.makedirs(output_dir, exist_ok=True)

        timestamp_str = self.timestamp.strftime("%Y%m%d_%H%M%S")
        operation_type = "DRY_RUN" if self.is_dry_run else "DELETE"
        filename = f"{output_dir}/decom_report_{operation_type}_{self.cr_number}_{timestamp_str}.json"

        report_data = {
            "summary": self.get_summary(),
            "operation_type": operation_type,
            "resources": [resource.to_dict() for resource in self.resources]
        }

        with open(filename, 'w') as jsonfile:
            json.dump(report_data, jsonfile, indent=2, default=str)

        return filename

    def get_resources_by_service(self) -> Dict[str, List[Resource]]:
        """
        Group resources by service.

        Returns:
            Dict mapping service names to lists of resources.
        """
        result = {}
        for resource in self.resources:
            if resource.service not in result:
                result[resource.service] = []
            result[resource.service].append(resource)
        return result

    def get_resources_by_account(self) -> Dict[str, List[Resource]]:
        """
        Group resources by account.

        Returns:
            Dict mapping account IDs to lists of resources.
        """
        result = {}
        for resource in self.resources:
            if resource.account_id not in result:
                result[resource.account_id] = []
            result[resource.account_id].append(resource)
        return result
