{"summary": {"CR_Number": "GECHG1234567", "Operation_Type": "Deletion", "Timestamp": "2025-05-12 19:29:23", "Total_Resources": 53, "Validated_Resources": 53, "Dry_Run_Success": 0, "Dry_Run_Failed": 0, "Deletion_Success": 22, "Deletion_Failed": 31, "Resources_With_Dependencies": 0}, "operation_type": "DELETE", "resources": [{"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-prd-UAI2004829/8ba285cac7d39a9d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-sparks-prd-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.07s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-stg-UAI2004829/4dee790b9220ed70", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-sparks-stg-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.36s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947", "Deleted_Date": "2025-05-12 19:30:22", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "34.27s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-npi-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-npi-sparks-stg-UAI2004829/7d0c809bd9e2dc90", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-npi-sparks-stg-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.01s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-gs-npi-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680 has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "32.45s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully initiated deletion of load balancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Deleted_Date": "2025-05-12 19:31:12", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.37s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully initiated deletion of load balancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Deleted_Date": "2025-05-12 19:31:20", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.23s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully initiated deletion of load balancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Deleted_Date": "2025-05-12 19:31:27", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.00s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully initiated deletion of load balancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Deleted_Date": "2025-05-12 19:31:35", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.32s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "test-instance-emgs-sparks-qa-aws-app-xLorRk", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:test-instance-emgs-sparks-qa-aws-app-xLorRk", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: test-instance-emgs-sparks-qa-aws-app-xLorRk because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.90s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials_for_i-05eb965ac3fd8fb23-5oODKX because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.85s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-02e58313d98d848b6", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-02e58313d98d848b6", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Security Group sg-02e58313d98d848b6 deleted successfully", "Deleted_Date": "2025-05-12 19:31:53", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.30s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials-for-i-002be9747294e8877-V87TL4", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials-for-i-002be9747294e8877-V87TL4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials-for-i-002be9747294e8877-V87TL4 because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.65s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.73s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.43s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0199ae65cc9c2346b", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0199ae65cc9c2346b", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0199ae65cc9c2346b deleted successfully", "Deleted_Date": "2025-05-12 19:32:18", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.61s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.08s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-09df14aa14c7e6408", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-09df14aa14c7e6408", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-09df14aa14c7e6408 deleted successfully", "Deleted_Date": "2025-05-12 19:32:30", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.58s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.16s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.04s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.48s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.66s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-00a54eeb559a244ba", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-00a54eeb559a244ba", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-00a54eeb559a244ba deleted successfully", "Deleted_Date": "2025-05-12 19:33:09", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.39s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.62s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-0c13321fab05fc3a2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-0c13321fab05fc3a2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Security Group sg-0c13321fab05fc3a2 deleted successfully", "Deleted_Date": "2025-05-12 19:33:23", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.64s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-03255a7c76bfd0a7c", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-03255a7c76bfd0a7c", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Security Group sg-03255a7c76bfd0a7c deleted successfully", "Deleted_Date": "2025-05-12 19:33:30", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.55s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08c1f59c7edbadcd9", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08c1f59c7edbadcd9", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-08c1f59c7edbadcd9' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.31s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.37s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-087df90f8ba056c60", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-087df90f8ba056c60", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Resource has dependencies: resource sg-087df90f8ba056c60 has a dependent object", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.52s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.08s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f/324b6ea53bdb69c2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.12s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.96s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f/58545befb00779d0", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.03s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-09d098800c98f744a", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-09d098800c98f744a", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Resource has dependencies: resource sg-09d098800c98f744a has a dependent object", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.20s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.02s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.01s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.93s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-002be9747294e8877", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-002be9747294e8877", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from terminated to terminated", "Deleted_Date": "2025-05-12 19:34:50", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.83s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-05eb965ac3fd8fb23", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-05eb965ac3fd8fb23", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from terminated to terminated", "Deleted_Date": "2025-05-12 19:34:57", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.99s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0321aa3341012dc5d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0321aa3341012dc5d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0321aa3341012dc5d deleted successfully", "Deleted_Date": "2025-05-12 19:35:03", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.82s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08292e054f597a702", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08292e054f597a702", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-08292e054f597a702 deleted successfully", "Deleted_Date": "2025-05-12 19:35:10", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.99s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0f23739b81fce377d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0f23739b81fce377d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0f23739b81fce377d deleted successfully", "Deleted_Date": "2025-05-12 19:35:17", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.91s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0a61a13260c3b8461", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0a61a13260c3b8461", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0a61a13260c3b8461 deleted successfully", "Deleted_Date": "2025-05-12 19:35:24", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.01s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0b51ed17665aabeae", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0b51ed17665aabeae", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Volume vol-0b51ed17665aabeae is in use and force_detach_volumes is not enabled", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.31s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-002d9c016266809f2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-002d9c016266809f2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-002d9c016266809f2 deleted successfully", "Deleted_Date": "2025-05-12 19:35:37", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.94s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0cd56d66953e6c11f", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0cd56d66953e6c11f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0cd56d66953e6c11f deleted successfully", "Deleted_Date": "2025-05-12 19:35:45", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.95s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-05b68331cc9022940", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-05b68331cc9022940", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Volume vol-05b68331cc9022940 is in use and force_detach_volumes is not enabled", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.31s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a25ca18b9d2ffbe", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a25ca18b9d2ffbe", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-08a25ca18b9d2ffbe deleted successfully", "Deleted_Date": "2025-05-12 19:35:58", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.96s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0d221edcc77bf155d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0d221edcc77bf155d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0d221edcc77bf155d deleted successfully", "Deleted_Date": "2025-05-12 19:36:05", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.07s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a697f12101a69f5", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a697f12101a69f5", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-08a697f12101a69f5 deleted successfully", "Deleted_Date": "2025-05-12 19:36:12", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.97s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-01ff028a35ce74562", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-01ff028a35ce74562", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Instance i-01ff028a35ce74562 has termination protection enabled", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.36s"}]}