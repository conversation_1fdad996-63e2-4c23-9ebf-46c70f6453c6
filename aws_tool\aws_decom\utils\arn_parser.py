"""
ARN parsing utilities for AWS Resource Decommissioning Tool.
"""

import re
from typing import Dict, Optional, Tuple, Any
import logging

# ARN patterns for different resource types
ARN_PATTERNS = {
    'ec2:instance': r'arn:aws:ec2:(.*?):(.*?):instance/(.*?)$',
    'ec2:image': r'arn:aws:ec2:(.*?):(.*?):image/(.*?)$',
    'ec2:volume': r'arn:aws:ec2:(.*?):(.*?):volume/(.*?)$',
    'ec2:snapshot': r'arn:aws:ec2:(.*?):(.*?):snapshot/(.*?)$',
    'ec2:security-group': r'arn:aws:ec2:(.*?):(.*?):security-group/(.*?)$',
    'lambda:function': r'arn:aws:lambda:(.*?):(.*?):function:(.*?)$',
    'sns:topic': r'arn:aws:sns:(.*?):(.*?):(.*?)$',
    's3:bucket': r'arn:aws:s3:::(.*?)$',
    'cloudwatch:alarm': r'arn:aws:cloudwatch:(.*?):(.*?):alarm:(.*?)$',
    'secretsmanager:secret': r'arn:aws:secretsmanager:(.*?):(.*?):secret:(.*?)$',
    'states:stateMachine': r'arn:aws:states:(.*?):(.*?):stateMachine:(.*?)$',
    'events:rule': r'arn:aws:events:(.*?):(.*?):rule/(.*?)$',
    'cloudformation:stack': r'arn:aws:cloudformation:(.*?):(.*?):stack/(.*?)(?:/.*)?$',
    'elasticloadbalancing:listener': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener/(.*?)/(.*?)$',
    'elasticloadbalancing:targetgroup': r'arn:aws:elasticloadbalancing:(.*?):(.*?):targetgroup/(.*?)/(.*?)$',
    'elasticloadbalancing:listener-rule': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener-rule/(.*?)/(.*?)$',
    'ssm:parameter': r'arn:aws:ssm:(.*?):(.*?):parameter/(.*?)$',
    'backup:backup-plan': r'arn:aws:backup:(.*?):(.*?):backup-plan:(.*?)$',
    'backup:backup-vault': r'arn:aws:backup:(.*?):(.*?):backup-vault:(.*?)$',
    'sns:subscription': r'arn:aws:sns:(.*?):(.*?):.*?:.*?$',
}

class ArnParser:
    """
    Parser for AWS ARNs (Amazon Resource Names).
    """
    
    def __init__(self):
        """Initialize the ARN parser."""
        self.logger = logging.getLogger(__name__)
    
    def parse_arn(self, arn: str) -> Dict[str, Any]:
        """
        Parse an AWS ARN into its components.
        
        Args:
            arn: The ARN to parse.
            
        Returns:
            Dict containing parsed ARN components:
            - service: AWS service (e.g., 'ec2', 's3')
            - resource_type: Type of resource (e.g., 'instance', 'bucket')
            - region: AWS region
            - account_id: AWS account ID
            - resource_id: Resource identifier
            - full_arn: The original ARN
        """
        # Clean the ARN
        arn = arn.strip()
        
        # Check if ARN is valid
        if not arn.startswith('arn:aws:'):
            self.logger.warning(f"Invalid ARN format: {arn}")
            return self._create_unknown_resource(arn)
        
        # Try to match against known patterns
        for resource_type, pattern in ARN_PATTERNS.items():
            match = re.match(pattern, arn)
            if match:
                service, specific_type = resource_type.split(':')
                region = match.group(1)
                account_id = match.group(2)
                resource_id = match.group(3)
                
                return {
                    'service': service,
                    'resource_type': specific_type,
                    'region': region,
                    'account_id': account_id,
                    'resource_id': resource_id,
                    'full_arn': arn
                }
        
        # If no pattern matches, try generic parsing
        return self._parse_generic_arn(arn)
    
    def _parse_generic_arn(self, arn: str) -> Dict[str, Any]:
        """
        Parse an ARN that doesn't match any known pattern.
        
        Args:
            arn: The ARN to parse.
            
        Returns:
            Dict containing parsed ARN components.
        """
        parts = arn.split(':')
        
        # Check if ARN has enough parts
        if len(parts) < 6:
            self.logger.warning(f"ARN doesn't have enough parts: {arn}")
            return self._create_unknown_resource(arn)
        
        service = parts[2]
        region = parts[3]
        account_id = parts[4]
        resource = parts[5]
        
        # Try to extract resource type and ID
        resource_type = 'unknown'
        resource_id = resource
        
        # Handle resource paths with slashes
        if '/' in resource:
            segments = resource.split('/')
            if len(segments) >= 2:
                resource_type = segments[0]
                resource_id = '/'.join(segments[1:])
        # Handle resource paths with colons
        elif ':' in resource and len(parts) > 6:
            resource_type = resource
            resource_id = parts[6]
        
        self.logger.info(f"Parsed generic ARN: {arn} -> {service}:{resource_type}")
        
        return {
            'service': service,
            'resource_type': resource_type,
            'region': region,
            'account_id': account_id,
            'resource_id': resource_id,
            'full_arn': arn
        }
    
    def _create_unknown_resource(self, arn: str) -> Dict[str, Any]:
        """
        Create a resource dict for an unknown ARN.
        
        Args:
            arn: The ARN that couldn't be parsed.
            
        Returns:
            Dict containing unknown resource information.
        """
        return {
            'service': 'unknown',
            'resource_type': 'unknown',
            'region': 'unknown',
            'account_id': 'unknown',
            'resource_id': arn,
            'full_arn': arn
        }
    
    def is_valid_arn(self, arn: str) -> bool:
        """
        Check if an ARN is valid.
        
        Args:
            arn: The ARN to check.
            
        Returns:
            True if the ARN is valid, False otherwise.
        """
        resource = self.parse_arn(arn)
        return resource['service'] != 'unknown'
