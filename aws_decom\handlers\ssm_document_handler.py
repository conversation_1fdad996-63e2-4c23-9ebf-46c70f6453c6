"""
SSM Document handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class SSMDocumentHandler(BaseResourceHandler):
    """
    Handler for SSM Documents.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the SSM Document handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an SSM Document exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Check if document exists
            response = ssm.describe_document(Name=doc_name)
            
            if response and 'Document' in response:
                # Check if document is owned by AWS
                owner = response['Document'].get('Owner', '')
                if owner == 'Amazon':
                    return False, f"SSM Document {doc_name} is owned by AWS and cannot be deleted"
                
                return True, f"SSM Document {doc_name} exists and can be deleted"
            else:
                return False, f"SSM Document {doc_name} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating SSM Document")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of an SSM Document.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Check for document versions
            try:
                versions_response = ssm.list_document_versions(Name=doc_name)
                
                for version in versions_response.get('DocumentVersions', []):
                    version_number = version.get('DocumentVersion', 'Unknown')
                    dependencies.append(ResourceDependency(
                        resource_type="ssm:document-version",
                        resource_id=f"{doc_name}:{version_number}",
                        relationship="version_of",
                        blocking=False  # Not blocking because versions are deleted with the document
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking document versions for {doc_name}: {e}")
            
            # Check for document shares
            try:
                shares_response = ssm.describe_document_permission(
                    Name=doc_name,
                    PermissionType='Share'
                )
                
                if shares_response.get('AccountIds'):
                    for account_id in shares_response.get('AccountIds', []):
                        dependencies.append(ResourceDependency(
                            resource_type="iam:account",
                            resource_id=account_id,
                            relationship="shared_with",
                            blocking=False  # Not blocking because shares are deleted with the document
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking document shares for {doc_name}: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for SSM Document {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of SSM Document deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Check if document exists
            response = ssm.describe_document(Name=doc_name)
            
            if response and 'Document' in response:
                # Check if document is owned by AWS
                owner = response['Document'].get('Owner', '')
                if owner == 'Amazon':
                    return False, f"SSM Document {doc_name} is owned by AWS and cannot be deleted"
                
                # Check for dependencies
                dependencies = self.check_dependencies(resource, session)
                blocking_dependencies = [dep for dep in dependencies if dep.blocking]
                
                if blocking_dependencies:
                    dep_str = ", ".join([f"{dep.relationship} {dep.resource_type} {dep.resource_id}" for dep in blocking_dependencies])
                    return False, f"SSM Document {doc_name} has blocking dependencies: {dep_str}"
                
                return True, f"Dry run successful: Would delete SSM Document {doc_name}"
            else:
                return False, f"SSM Document {doc_name} not found"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of SSM Document")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an SSM Document.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Check if document exists and is not owned by AWS
            try:
                response = ssm.describe_document(Name=doc_name)
                
                if response and 'Document' in response:
                    # Check if document is owned by AWS
                    owner = response['Document'].get('Owner', '')
                    if owner == 'Amazon':
                        return False, f"SSM Document {doc_name} is owned by AWS and cannot be deleted"
            except ClientError as e:
                return self.handle_client_error(e, "checking SSM Document ownership")
            
            # Delete the document
            ssm.delete_document(Name=doc_name)
            
            return True, f"Successfully deleted SSM Document {doc_name}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of SSM Document")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of an SSM Document.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Get document details
            response = ssm.describe_document(Name=doc_name)
            
            if response and 'Document' in response:
                # SSM Documents don't have a creation date in the API response
                # We could use the creation date of the first version as an approximation
                try:
                    versions_response = ssm.list_document_versions(Name=doc_name)
                    
                    # Sort versions by version number (ascending)
                    versions = sorted(
                        versions_response.get('DocumentVersions', []),
                        key=lambda v: int(v.get('DocumentVersion', '0'))
                    )
                    
                    if versions:
                        # Get the creation date of the first version
                        created_date = versions[0].get('CreatedDate')
                        if created_date:
                            return created_date.strftime("%Y-%m-%d %H:%M:%S")
                except ClientError as e:
                    self.logger.warning(f"Error getting document versions for {doc_name}: {e}")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation date for SSM Document {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an SSM Document.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ssm = session.client('ssm', region_name=resource.region)
            
            # Extract document name from resource ID
            doc_name = resource.resource_id
            
            # Get document details to get the ARN
            try:
                response = ssm.describe_document(Name=doc_name)
                if not response or 'Document' not in response:
                    return {}
                
                # Construct the ARN if not provided in the response
                if 'DocumentArn' not in response['Document']:
                    sts = session.client('sts')
                    account_id = sts.get_caller_identity()['Account']
                    doc_arn = f"arn:aws:ssm:{resource.region}:{account_id}:document/{doc_name}"
                else:
                    doc_arn = response['Document']['DocumentArn']
                
                # Get tags for the document
                tags_response = ssm.list_tags_for_resource(ResourceType='Document', ResourceId=doc_arn)
                
                tags = {}
                for tag in tags_response.get('TagList', []):
                    tags[tag['Key']] = tag['Value']
                
                return tags
            
            except ClientError as e:
                self.logger.warning(f"Error getting document ARN: {e}")
                return {}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for SSM Document {resource.resource_id}: {e}")
            return {}
