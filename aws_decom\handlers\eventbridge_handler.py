"""
EventBridge Rule handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class Event<PERSON>ridgeRuleHandler(BaseResourceHandler):
    """
    Handler for EventBridge Rules.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EventBridge Rule handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EventBridge Rule exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            events = session.client('events', region_name=resource.region)
            
            # Extract rule name from ARN or resource ID
            rule_name = resource.resource_id
            if '/' in rule_name:
                rule_name = rule_name.split('/')[-1]
            
            # Check if rule exists
            response = events.describe_rule(Name=rule_name)
            
            # Check if rule is managed by AWS
            if response.get('ManagedBy'):
                return False, f"Rule {rule_name} is managed by {response['ManagedBy']} and cannot be deleted"
            
            # Check for targets
            targets = events.list_targets_by_rule(Rule=rule_name)
            
            if targets.get('Targets'):
                self.logger.info(f"Rule {rule_name} has {len(targets['Targets'])} targets")
            
            return True, f"Rule {rule_name} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EventBridge Rule")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EventBridge Rule deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            events = session.client('events', region_name=resource.region)
            
            # Extract rule name from ARN or resource ID
            rule_name = resource.resource_id
            if '/' in rule_name:
                rule_name = rule_name.split('/')[-1]
            
            # Check for targets
            targets = events.list_targets_by_rule(Rule=rule_name)
            
            for target in targets.get('Targets', []):
                target_arn = target.get('Arn', '')
                target_id = target.get('Id', '')
                
                # Determine target type based on ARN
                if 'lambda' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="lambda:function",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting Lambda
                    ))
                elif 'sqs' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="sqs:queue",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting SQS
                    ))
                elif 'sns' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="sns:topic",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting SNS
                    ))
                elif 'states' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="states:stateMachine",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting Step Functions
                    ))
                elif 'kinesis' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="kinesis:stream",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting Kinesis
                    ))
                elif 'firehose' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="firehose:deliverystream",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting Firehose
                    ))
                elif 'events' in target_arn:
                    dependencies.append(ResourceDependency(
                        resource_type="events:event-bus",
                        resource_id=target_arn.split(':')[-1],
                        relationship="targets",
                        blocking=False  # Not blocking because rule can be deleted without affecting event bus
                    ))
                else:
                    dependencies.append(ResourceDependency(
                        resource_type="unknown:target",
                        resource_id=target_id,
                        relationship="targets",
                        blocking=False  # Not blocking because we don't know what it is
                    ))
            
            # Check for CloudFormation stacks that created this rule
            try:
                cfn = session.client('cloudformation', region_name=resource.region)
                stacks = cfn.list_stacks(StackStatusFilter=[
                    'CREATE_COMPLETE', 'UPDATE_COMPLETE', 'UPDATE_ROLLBACK_COMPLETE'
                ])
                
                for stack in stacks.get('StackSummaries', []):
                    stack_name = stack.get('StackName')
                    
                    resources = cfn.list_stack_resources(StackName=stack_name)
                    
                    for res in resources.get('StackResourceSummaries', []):
                        if res.get('ResourceType') == 'AWS::Events::Rule' and res.get('PhysicalResourceId') == rule_name:
                            dependencies.append(ResourceDependency(
                                resource_type="cloudformation:stack",
                                resource_id=stack_name,
                                relationship="managed_by",
                                blocking=True  # Blocking because stack manages the resource
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudFormation stack dependencies: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EventBridge Rule {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EventBridge Rule deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            events = session.client('events', region_name=resource.region)
            
            # Extract rule name from ARN or resource ID
            rule_name = resource.resource_id
            if '/' in rule_name:
                rule_name = rule_name.split('/')[-1]
            
            # Check if rule exists
            response = events.describe_rule(Name=rule_name)
            
            # Check if rule is managed by AWS
            if response.get('ManagedBy'):
                return False, f"Rule {rule_name} is managed by {response['ManagedBy']} and cannot be deleted"
            
            # Check for targets
            targets = events.list_targets_by_rule(Rule=rule_name)
            
            if targets.get('Targets'):
                self.logger.info(f"Dry run: Would remove {len(targets['Targets'])} targets from rule {rule_name}")
            
            self.logger.info(f"Dry run: Would delete rule {rule_name}")
            
            return True, f"Rule {rule_name} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of EventBridge Rule")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an EventBridge Rule.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            events = session.client('events', region_name=resource.region)
            
            # Extract rule name from ARN or resource ID
            rule_name = resource.resource_id
            if '/' in rule_name:
                rule_name = rule_name.split('/')[-1]
            
            # Check if rule exists
            response = events.describe_rule(Name=rule_name)
            
            # Check if rule is managed by AWS
            if response.get('ManagedBy'):
                return False, f"Rule {rule_name} is managed by {response['ManagedBy']} and cannot be deleted"
            
            # Remove all targets from the rule
            targets = events.list_targets_by_rule(Rule=rule_name)
            
            if targets.get('Targets'):
                target_ids = [target['Id'] for target in targets['Targets']]
                events.remove_targets(Rule=rule_name, Ids=target_ids)
                self.logger.info(f"Removed {len(target_ids)} targets from rule {rule_name}")
            
            # Delete the rule
            events.delete_rule(Name=rule_name)
            
            return True, f"Rule {rule_name} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting EventBridge Rule")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an EventBridge Rule.
        
        Note: EventBridge API doesn't provide creation date for rules directly.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        # EventBridge API doesn't provide creation date for rules
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EventBridge Rule.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            events = session.client('events', region_name=resource.region)
            
            # Extract rule name from ARN or resource ID
            rule_name = resource.resource_id
            if '/' in rule_name:
                rule_name = rule_name.split('/')[-1]
            
            # Get rule ARN
            response = events.describe_rule(Name=rule_name)
            rule_arn = response.get('Arn')
            
            if not rule_arn:
                return {}
            
            # Get tags for the rule
            tags_response = events.list_tags_for_resource(ResourceARN=rule_arn)
            
            tags = tags_response.get('Tags', {})
            
            return tags
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EventBridge Rule {resource.resource_id}: {e}")
            return {}
