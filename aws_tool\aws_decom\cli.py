"""
Command-line interface for AWS Resource Decommissioning Tool.
"""

import argparse
import logging
import logging.config
import os
import sys
import yaml
from typing import List, Dict, Any
import colorama
from colorama import Fore, Style
from tabulate import tabulate

from .core.config_manager import ConfigManager
from .core.session_manager import SessionManager
from .core.resource_manager import ResourceManager
from .core.validator import Validator
from .utils.input_manager import InputManager
from .utils.dependency_graph import DependencyGraph
from .handlers.handler_factory import ResourceHandlerFactory

# Initialize colorama
colorama.init(autoreset=True)

class CLI:
    """
    Command-line interface for AWS Resource Decommissioning Tool.
    """

    def __init__(self):
        """Initialize the CLI."""
        # Set up configuration
        self.config_manager = ConfigManager("config/config.yaml")

        # Set up logging
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

        # Create directories
        self._create_directories()

        # Initialize components
        self.session_manager = SessionManager(self.config_manager)
        self.resource_manager = ResourceManager(self.config_manager, self.session_manager)
        self.validator = Validator(self.config_manager)
        self.input_manager = InputManager()
        self.handler_factory = ResourceHandlerFactory(self.config_manager)
        self.dependency_graph = DependencyGraph(self.config_manager.get("paths.reports_dir", "reports"))

    def _setup_logging(self) -> None:
        """Set up logging configuration."""
        try:
            # Load logging configuration
            with open("config/logging_config.yaml", 'r') as f:
                logging_config = yaml.safe_load(f)

            # Create log directory if it doesn't exist
            log_dir = self.config_manager.get("paths.logs_dir", "logs")
            os.makedirs(log_dir, exist_ok=True)

            # Configure logging
            logging.config.dictConfig(logging_config)

        except Exception as e:
            # Fall back to basic configuration
            logging.basicConfig(
                level=logging.INFO,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler("aws_decom.log")
                ]
            )
            logging.warning(f"Error setting up logging configuration: {e}")

    def _create_directories(self) -> None:
        """Create necessary directories."""
        # Create logs directory
        logs_dir = self.config_manager.get("paths.logs_dir", "logs")
        os.makedirs(logs_dir, exist_ok=True)

        # Create reports directory
        reports_dir = self.config_manager.get("paths.reports_dir", "reports")
        os.makedirs(reports_dir, exist_ok=True)

    def parse_args(self) -> argparse.Namespace:
        """
        Parse command-line arguments.

        Returns:
            Parsed arguments.
        """
        parser = argparse.ArgumentParser(
            description="AWS Resource Decommissioning Tool",
            formatter_class=argparse.ArgumentDefaultsHelpFormatter
        )

        # Input file
        parser.add_argument(
            "--input",
            required=True,
            help="Path to input file containing ARNs (CSV, JSON, or TXT)"
        )

        # Change request number
        parser.add_argument(
            "--cr-no",
            required=True,
            help="Change request number (e.g., GECHG1234567)"
        )

        # Operation mode
        group = parser.add_mutually_exclusive_group(required=True)
        group.add_argument(
            "--dry-run",
            action="store_true",
            help="Perform a dry run without actually deleting resources"
        )
        group.add_argument(
            "--delete",
            action="store_true",
            help="Delete resources (USE WITH CAUTION)"
        )

        # Force deletion
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force deletion of resources with dependencies (USE WITH EXTREME CAUTION)"
        )

        # Skip validation and dry-run checks
        parser.add_argument(
            "--skip-checks",
            action="store_true",
            help="Skip validation and dry-run checks and directly delete resources (EXTREMELY DANGEROUS - USE WITH EXTREME CAUTION)"
        )

        # Output format
        parser.add_argument(
            "--output-format",
            choices=["csv", "json", "both"],
            default="both",
            help="Output report format"
        )

        # Generate dependency graph
        parser.add_argument(
            "--dependency-graph",
            action="store_true",
            help="Generate dependency graph visualization"
        )

        # Verbose output
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Enable verbose output"
        )

        # Version
        parser.add_argument(
            "--version",
            action="version",
            version="AWS Resource Decommissioning Tool v1.0.0"
        )

        return parser.parse_args()

    def run(self) -> int:
        """
        Run the CLI.

        Returns:
            Exit code.
        """
        # Parse arguments
        args = self.parse_args()

        # Set log level based on verbose flag
        if args.verbose:
            logging.getLogger("aws_decom").setLevel(logging.DEBUG)

        # Validate CR number
        valid_cr, cr_message = self.validator.validate_cr_number(args.cr_no)
        if not valid_cr:
            self.logger.error(cr_message)
            print(f"{Fore.RED}{cr_message}")
            return 1

        # Load ARNs from input file
        arns = self.input_manager.load_arns_from_file(args.input)
        if not arns:
            self.logger.error(f"No ARNs found in input file: {args.input}")
            print(f"{Fore.RED}No ARNs found in input file: {args.input}")
            return 1

        # Validate ARNs
        valid_arns, invalid_arns = self.validator.validate_arns(arns)
        if invalid_arns:
            self.logger.warning(f"Found {len(invalid_arns)} invalid ARNs")
            print(f"{Fore.YELLOW}Warning: Found {len(invalid_arns)} invalid ARNs")

        if not valid_arns:
            self.logger.error("No valid ARNs found")
            print(f"{Fore.RED}Error: No valid ARNs found")
            return 1

        # Validate supported resource types
        supported_types = self.handler_factory.get_supported_resource_types()
        supported_arns, unsupported_arns = self.validator.validate_supported_resource_types(valid_arns, supported_types)

        if unsupported_arns:
            self.logger.warning(f"Found {len(unsupported_arns)} unsupported resource types")
            print(f"{Fore.YELLOW}Warning: Found {len(unsupported_arns)} unsupported resource types")

        if not supported_arns:
            self.logger.error("No supported resource types found")
            print(f"{Fore.RED}Error: No supported resource types found")
            return 1

        # Create resources from ARNs
        resources = []
        for arn in supported_arns:
            resource = self.resource_manager.create_resource_from_arn(arn, args.cr_no)
            if resource:
                resources.append(resource)

        # Group resources by service, service+type, and account
        resources_by_service = self.resource_manager.group_resources_by_service(resources)
        resources_by_service_and_type = self.resource_manager.group_resources_by_service_and_type(resources)
        resources_by_account = self.resource_manager.group_resources_by_account(resources)

        # Display summary
        self._display_summary(resources_by_service, resources_by_service_and_type, resources_by_account)

        # Display disclaimer
        self._display_disclaimer(args.dry_run, args.skip_checks)

        # Confirm operation
        if not self._confirm_operation(args.dry_run, args.force, args.skip_checks):
            self.logger.info("Operation cancelled by user")
            print(f"{Fore.YELLOW}Operation cancelled")
            return 0

        # Apply force deletion setting if specified
        if args.force:
            self.config_manager.config["resources"]["dependencies"]["cascade_delete"] = True
            self.logger.warning("Force deletion enabled - dependencies will be automatically deleted")
            print(f"{Fore.RED}Force deletion enabled - dependencies will be automatically deleted")

        # Process resources
        report = self.resource_manager.process_resources(supported_arns, args.cr_no, args.dry_run, args.skip_checks)

        # Save report
        reports_dir = self.config_manager.get("paths.reports_dir", "reports")
        if args.output_format in ["csv", "both"]:
            csv_path = report.save_csv(reports_dir)
            print(f"{Fore.GREEN}CSV report saved to: {csv_path}")

        if args.output_format in ["json", "both"]:
            json_path = report.save_json(reports_dir)
            print(f"{Fore.GREEN}JSON report saved to: {json_path}")

        # Display results
        self._display_results(report)

        # Generate dependency graph if requested
        if args.dependency_graph:
            try:
                # Generate resource dependency graph
                graph_path = self.dependency_graph.create_graph(report.resources, args.cr_no)
                if graph_path:
                    print(f"{Fore.GREEN}Dependency graph saved to: {graph_path}")

                # Generate service-level dependency graph
                service_graph_path = self.dependency_graph.create_service_graph(report.resources, args.cr_no)
                if service_graph_path:
                    print(f"{Fore.GREEN}Service dependency graph saved to: {service_graph_path}")
            except Exception as e:
                self.logger.error(f"Error generating dependency graph: {e}")
                print(f"{Fore.RED}Error generating dependency graph: {e}")

        return 0

    def _display_summary(self, resources_by_service: Dict, resources_by_service_and_type: Dict, resources_by_account: Dict) -> None:
        """
        Display a summary of resources to be processed.

        Args:
            resources_by_service: Resources grouped by service.
            resources_by_service_and_type: Resources grouped by service and resource type.
            resources_by_account: Resources grouped by account.
        """
        print(f"\n{Fore.CYAN}=== Resource Summary ===")

        # Display resources by service and type
        print(f"\n{Fore.CYAN}Resources by Service and Type:")
        service_type_table = []
        for service, type_dict in resources_by_service_and_type.items():
            for resource_type, resources in type_dict.items():
                service_type_table.append([service, resource_type, len(resources)])

        print(tabulate(service_type_table, headers=["Service", "Resource Type", "Count"], tablefmt="simple"))

        # Display resources by service (summary)
        print(f"\n{Fore.CYAN}Resources by Service (Summary):")
        service_table = []
        for service, resources in resources_by_service.items():
            service_table.append([service, len(resources)])

        print(tabulate(service_table, headers=["Service", "Count"], tablefmt="simple"))

        # Display resources by account
        print(f"\n{Fore.CYAN}Resources by Account:")
        account_table = []
        for account_id, resources in resources_by_account.items():
            account_table.append([account_id, len(resources)])

        print(tabulate(account_table, headers=["Account ID", "Count"], tablefmt="simple"))

        # Display total
        total_resources = sum(len(resources) for resources in resources_by_service.values())
        print(f"\n{Fore.CYAN}Total Resources: {total_resources}")

    def _display_disclaimer(self, dry_run: bool, skip_checks: bool = False) -> None:
        """
        Display a disclaimer based on the operation mode.

        Args:
            dry_run: Whether this is a dry run.
            skip_checks: Whether validation and dry-run checks are skipped.
        """
        print("\n" + "=" * 80)

        if dry_run:
            print(f"{Fore.YELLOW}DISCLAIMER - DRY RUN MODE")
            print(f"{Fore.YELLOW}This is a DRY RUN. No resources will be deleted.")
            print(f"{Fore.YELLOW}The operation will only validate resources and simulate deletion.")
        else:
            print(f"{Fore.RED}DISCLAIMER - DELETION MODE")
            print(f"{Fore.RED}This operation will PERMANENTLY DELETE the resources listed above.")
            print(f"{Fore.RED}This action CANNOT be undone.")
            print(f"{Fore.RED}Make sure you have backups if needed.")

            if skip_checks:
                print("\n" + "!" * 80)
                print(f"{Fore.RED}WARNING - SKIPPING VALIDATION AND DRY-RUN CHECKS")
                print(f"{Fore.RED}You have chosen to skip validation and dry-run checks.")
                print(f"{Fore.RED}Resources will be deleted WITHOUT any safety checks.")
                print(f"{Fore.RED}This is EXTREMELY DANGEROUS and may lead to unexpected consequences.")
                print(f"{Fore.RED}Only use this option if you are absolutely certain about what you're doing.")
                print("!" * 80)

        print("=" * 80)

    def _confirm_operation(self, dry_run: bool, force: bool, skip_checks: bool = False) -> bool:
        """
        Confirm the operation with the user.

        Args:
            dry_run: Whether this is a dry run.
            force: Whether force deletion is enabled.
            skip_checks: Whether validation and dry-run checks are skipped.

        Returns:
            True if the user confirms, False otherwise.
        """
        if dry_run:
            confirm = input(f"\n{Fore.YELLOW}Proceed with dry run? (y/n): ").strip().lower()
            return confirm == 'y'
        else:
            # First confirmation
            confirm1 = input(f"\n{Fore.RED}Proceed with deletion? (y/n): ").strip().lower()
            if confirm1 != 'y':
                return False

            # Second confirmation for deletion
            confirm2 = input(f"\n{Fore.RED}Type 'DELETE' to confirm: ").strip()
            if confirm2 != 'DELETE':
                return False

            # Third confirmation for force deletion
            if force:
                confirm3 = input(f"\n{Fore.RED}FORCE deletion will automatically delete dependencies. Type 'FORCE DELETE' to confirm: ").strip()
                if confirm3 != 'FORCE DELETE':
                    return False

            # Fourth confirmation for skipping checks
            if skip_checks:
                confirm4 = input(f"\n{Fore.RED}DANGER: You are about to delete resources WITHOUT any safety checks. Type 'I UNDERSTAND THE RISKS' to confirm: ").strip()
                if confirm4 != 'I UNDERSTAND THE RISKS':
                    return False

            return True

    def _display_results(self, report: Any) -> None:
        """
        Display the results of the operation.

        Args:
            report: DecommissioningReport containing results.
        """
        summary = report.get_summary()
        operation_type = summary.get("Operation_Type", "Operation")

        print(f"\n{Fore.CYAN}=== {operation_type} Results ===")

        # Display summary statistics
        print(f"\n{Fore.CYAN}Summary:")
        summary_table = []
        for key, value in summary.items():
            if key not in ["CR_Number", "Timestamp"]:
                summary_table.append([key.replace("_", " "), value])

        print(tabulate(summary_table, tablefmt="simple"))

        # Display success/failure by service and resource type
        print(f"\n{Fore.CYAN}Results by Service and Resource Type:")
        service_type_results = {}

        for resource in report.resources:
            key = (resource.service, resource.resource_type)
            if key not in service_type_results:
                service_type_results[key] = {"success": 0, "failed": 0, "skipped": 0}

            if resource.dry_run_success is True:
                service_type_results[key]["success"] += 1
            elif resource.dry_run_success is False:
                service_type_results[key]["failed"] += 1
            else:
                service_type_results[key]["skipped"] += 1

        service_type_table = []
        for (service, resource_type), results in service_type_results.items():
            service_type_table.append([
                service,
                resource_type,
                results["success"],
                results["failed"],
                results["skipped"]
            ])

        print(tabulate(service_type_table, headers=["Service", "Resource Type", "Success", "Failed", "Skipped"], tablefmt="simple"))

        # Display success/failure by service (summary)
        print(f"\n{Fore.CYAN}Results by Service (Summary):")
        service_results = {}

        for resource in report.resources:
            if resource.service not in service_results:
                service_results[resource.service] = {"success": 0, "failed": 0, "skipped": 0}

            if resource.dry_run_success is True:
                service_results[resource.service]["success"] += 1
            elif resource.dry_run_success is False:
                service_results[resource.service]["failed"] += 1
            else:
                service_results[resource.service]["skipped"] += 1

        service_table = []
        for service, results in service_results.items():
            service_table.append([
                service,
                results["success"],
                results["failed"],
                results["skipped"]
            ])

        print(tabulate(service_table, headers=["Service", "Success", "Failed", "Skipped"], tablefmt="simple"))

        # Display overall result
        if report.dry_run_failed > 0:
            print(f"\n{Fore.YELLOW}Operation completed with {report.dry_run_failed} failures.")
        else:
            print(f"\n{Fore.GREEN}Operation completed successfully.")

def main():
    """Main entry point for the CLI."""
    cli = CLI()
    sys.exit(cli.run())

if __name__ == "__main__":
    main()
