# | Requirement | Notes
1 | Input is a list of ARNs | (likely CSV, JSON, etc.)
2 | Based on ARN's account ID, switch boto3 session | (no AssumeRole, switch AWS profile dynamically)
3 | AWS Profiles follow a naming convention like support-{account_id} | Example: support-************
4 | Support dry-run and real deletion modes | dry-run is default for safety
5 | Boto3 session must use SSL with certifi | 
6 | Structured logging and error handling are required | 
7 | If AWS profile for that account is missing, gracefully skip and log | 
8 | Should support many AWS resource types, easily extensible | 

## System Architecture

[Input File (ARNs)] 
        ↓
[Session Manager] → (Choose profile: support-<account_id>)
        ↓
[Resource Parser] → (Parse Service/Resource Type from ARN)
        ↓
[Policy Checker] → (Apply Dry-Run mode / Real Delete mode)
        ↓
[Resource Deletion Handler] → (Delete or Dry Run)
        ↓
[Logger & Reporter] → (Success / Skipped / Error Report)


## Major Components

Module | Functionality
input_manager.py | Load ARNs from .CSV file only,only arn no header
session_manager.py | Create boto3 Session using profile: support-{account_id}
resource_parser.py | Parse each ARN → service and resource ID
deleter/ (directory) | Delete logic per AWS service (ec2, s3, lambda, cloudformation, etc.)
runner.py | Core orchestration: session switch, dry-run, real delete
reporter.py | Final CSV/JSON logs, error tracking, skipped resources
utils.py | Helpers (retry wrappers, logging setup, etc.)

## Service Flowchart

START
  |
  V
Read Input File (ARNs)
  |
  V
Prompt for CR Number (+ validate)
  |
  V
Parse ARN -> [Service, Resource Type, Region, Account ID]
  |
  V
GROUP RESOURCES by:
  - Service
  - Resource Type
  - Account
  - Region
  |
  V
[INITIAL SUMMARY]
  - Service
  - Resource Type
  - Count
  |
  V
SHOW DISCLAIMER
  - Different for DRY-RUN vs DELETE
  |
  V
PROMPT for User Confirmation to Proceed
  |
  V
FOR EACH RESOURCE:
  |
  +--> Switch boto3 session (profile = support-{AccountID})
         |
         +--> Create correct boto3 client
         |
         +--> VALIDATE resource exists
         |
         +--> IF dry-run:
         |      - Log simulated deletion
         |
         +--> IF delete:
                - Attempt real deletion
                - Handle known dependency errors
  |
  V
Record Result:
  - CR Number
  - Account
  - Region
  - Service
  - Resource Type
  - Resource ID
  - ARN
  - Action (dry-run/delete)
  - Status (success/failed)
  - Error (if any)
  - Duration
  - Timestamp
  |
  V
GENERATE FINAL REPORT (CSV)
  |
  V
EXIT (with summary message)


Supported AWS Services and Resource Types (Initial Scope)

Service | Resource Type | Notes
EC2 | Instance (instance) | 
EC2 | Volume (volume) | 
EC2 | Snapshot (snapshot) | 
EC2 | AMI (image) | 
EC2 | Security Group (security-group) | Must not be "in use"
S3 | Bucket (bucket) | Optional delete objects inside (flag)
Lambda | Function (function) | 
Secrets Manager | Secret (secret) | 
CloudWatch | Alarm (alarm) | 
CloudFormation | Stack (stack) | Delete all resources underneath
ELBv2 | Load Balancer (loadbalancer) | 
ELBv2 | Target Group (targetgroup) | 
ELBv2 | Listener (listener) | 
ELBv2 | Listener Rule (rule) | 
SSM | Parameter (parameter) | 
SNS | Topic (topic) | 
SNS | Subscription (subscription) | 
Step Functions | State Machine (stateMachine) | 
EventBridge | Rule (rule) | 
AWS Backup | Backup Plan (backup-plan) | 
AWS Backup | Backup Vault (backup-vault) | 


Small Diagram Version (simplified)

Input File --> Parse ARNs --> Group Resources --> Show Summary --> Disclaimer --> Confirm?

               |
               V
      For Each Resource:
         - Validate
         - Switch Account/Profile
         - Dry-run/Delete
         - Record Result
         
               |
               V
         Final CSV Report


Visual Modular Design
cli.py         --> Argument parsing (input file, dry-run/delete, cr-number)
utils.py       --> CR number validation, ARN parsing helpers
session.py     --> boto3 session switcher based on profile + account
resource_ops/  --> Modules for each service (ec2_ops.py, s3_ops.py, lambda_ops.py, etc.)
reporter.py    --> CSV file writer
runner.py      --> Core engine (calls all modules)

Before deleting a resource, the program will intelligently check:

Is this resource attached/linked to another resource?

Would deleting it cause a failure or dependency violation?

Should we modify/detach things first?

Example Situations:
Resource | Linked To | Notes
EC2 Volume | EC2 Instance | Must be detached before delete
ELB Listener | Load Balancer | Cannot delete listener if LB is deleted first
ELB Target Group | Load Balancer | Must deregister targets before delete
Security Group | ENIs (Elastic Network Interfaces) | Must not be attached
CloudFormation Stack | Child Resources | Stack deletion handles sub-resources
S3 Bucket | Objects inside | Must be emptied before bucket deletion

When validating a resource:

Before dry-run or delete, query AWS for linked relationships.

If links exist:

In dry-run: Show relationship info ("Attached to X, cannot delete unless detached").

In delete:

Optional auto-detach if safe

Or fail gracefully and log it properly

In CSV report, relationship problems will appear under Error Message.

What Happens If Relationship Found?

Force Deletion Option
For certain resources, offer a --force flag.
If set:

Auto-detach (e.g., detach volume from instance).

Auto-delete dependent resources (dangerous, extra confirmation needed).

Must have DOUBLE CONFIRMATION:

First ask "Are you sure?".

Then ask again "Type FORCE DELETE to confirm."

Initial Summary Phase
Show:

Service

Resource Type

Count

Service | Resource Type | Count
EC2 | Instance | 15
S3 | Bucket | 5

Disclaimer Phase
Show different disclaimers:

For Dry-Run → "No real actions will be performed."

For Delete → "Real resources will be deleted. Proceed with caution."

User Confirmation Phase: 
confirm for both dry-run and delete 
Confirm twice if deletion mode (and extra if --force used).

Validation + Dependency Discovery Phase
Validate existence of each resource.

Discover dependencies (linked resources).

Warn if dependencies block deletion.


Dry-Run/Delete Execution Phase
If Dry-Run:

Simulate delete.

Log detected dependencies.



If Delete:

Respect dependencies:

Delete in correct order.

If --force:

Attempt auto-detach.

Auto-delete child resources (if safe).

Else safe-fail and log.

Reporting Phase
Create a CSV report:

CR number

Timestamp

Service

Resource Type

ARN

Action attempted (dry-run/delete)

Result (Success/Failed)

Error Message (if any)

Dependency Detected (Yes/No)

Duration


Final Summary Phase
Overall statistics:

Number attempted

Number succeeded

Number failed

Dependency blocks

Output Artifacts
CSV report
Log file (detailed execution)

run script ex: 
python cleanup.py --input-file input.csv --dry-run
python cleanup.py --input-file input.csv --delete --force  


create seperate dir for reports and logs 

should have extensive logging ability and proper error handaling capabilty