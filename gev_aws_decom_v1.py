import argparse
import boto3
import logging
import os
import re
import csv
from botocore.exceptions import ClientError
from datetime import datetime
from tqdm import tqdm
import colorama
from colorama import Fore, Style

# AWS ARN Patterns for parsing and identifying resource types
ARN_PATTERNS = {
    'ec2': r'arn:aws:ec2:(.*?):(.*?):instance/(.*?)$',
    'ec2:image': r'arn:aws:ec2:(.*?):(.*?):image/(.*?)$',
    'ec2:volume': r'arn:aws:ec2:(.*?):(.*?):volume/(.*?)$',
    'ec2:snapshot': r'arn:aws:ec2:(.*?):(.*?):snapshot/(.*?)$',
    'ec2:securitygroup': r'arn:aws:ec2:(.*?):(.*?):security-group/(.*?)$',
    'lambda': r'arn:aws:lambda:(.*?):(.*?):function:(.*?)$',
    'sns': r'arn:aws:sns:(.*?):(.*?):(.*?)$',
    's3': r'arn:aws:s3:::(.*?)$',
    'cloudwatch:alarm': r'arn:aws:cloudwatch:(.*?):(.*?):alarm:(.*?)$',
    'secretsmanager:secret': r'arn:aws:secretsmanager:(.*?):(.*?):secret:(.*?)$',
    'states:stateMachine': r'arn:aws:states:(.*?):(.*?):stateMachine:(.*?)$',
    'events:rule': r'arn:aws:events:(.*?):(.*?):rule/(.*?)$',
    'cloudformation:stack': r'arn:aws:cloudformation:(.*?):(.*?):stack/(.*?)(?:/.*)?$',
    'elbv2:listener': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener/(.*?)/(.*?)$',
    'elbv2:targetgroup': r'arn:aws:elasticloadbalancing:(.*?):(.*?):targetgroup/(.*?)/(.*?)$',
    'elbv2:listener-rule': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener-rule/(.*?)/(.*?)$',
    'ssm:parameter': r'arn:aws:ssm:(.*?):(.*?):parameter/(.*?)$',
    'backup:backup-plan': r'arn:aws:backup:(.*?):(.*?):backup-plan:(.*?)$',
    'backup:backup-vault': r'arn:aws:backup:(.*?):(.*?):backup-vault:(.*?)$',
    'sns:subscription': r'arn:aws:sns:(.*?):(.*?):.*?:.*?$',
}

# Logging setup
logging.basicConfig(level=logging.INFO)
colorama.init(autoreset=True)





# Parse the ARN from input
def parse_arn(arn):
    for rtype, pattern in ARN_PATTERNS.items():
        match = re.match(pattern, arn)
        if match:
            return rtype, arn, match.groups()[-1]  # Return resource type, ARN, and resource ID
    return 'unknown', arn, arn  # Return unknown for unsupported resources


# Function to check if the resource exists
def validate_resource_existence(arn, resource_type, session):
    try:
        client = session.client(resource_type.split(':')[0])
        if resource_type.startswith('ec2'):
            if resource_type == 'ec2:instance':
                instance_id = arn.split(':')[-1]
                client.describe_instances(InstanceIds=[instance_id])
            elif resource_type == 'ec2:volume':
                volume_id = arn.split(':')[-1]
                client.describe_volumes(VolumeIds=[volume_id])
            elif resource_type == 'ec2:snapshot':
                snapshot_id = arn.split(':')[-1]
                client.describe_snapshots(SnapshotIds=[snapshot_id])
            elif resource_type == 'ec2:securitygroup':
                security_group_id = arn.split(':')[-1]
                client.describe_security_groups(GroupIds=[security_group_id])
        elif resource_type.startswith('s3'):
            bucket_name = arn.split(':')[-1]
            client.head_bucket(Bucket=bucket_name)
        elif resource_type.startswith('lambda'):
            function_name = arn.split(':')[-1]
            client.get_function(FunctionName=function_name)
        elif resource_type.startswith('sns'):
            topic_arn = arn.split(':')[-1]
            client.get_topic_attributes(TopicArn=topic_arn)
        elif resource_type.startswith('cloudwatch:alarm'):
            alarm_name = arn.split(':')[-1]
            client.describe_alarms(AlarmNames=[alarm_name])
        elif resource_type.startswith('secretsmanager:secret'):
            secret_name = arn.split(':')[-1]
            client.describe_secret(SecretId=secret_name)
        elif resource_type.startswith('states:stateMachine'):
            state_machine_arn = arn.split(':')[-1]
            client.describe_state_machine(stateMachineArn=state_machine_arn)
        elif resource_type.startswith('events:rule'):
            rule_name = arn.split(':')[-1]
            client.describe_rule(Name=rule_name)
        elif resource_type.startswith('cloudformation:stack'):
            stack_name = arn.split(':')[-1]
            client.describe_stacks(StackName=stack_name)
        elif resource_type.startswith('elbv2'):
            load_balancer_name = arn.split(':')[-2]
            client.describe_load_balancers(Names=[load_balancer_name])
        elif resource_type.startswith('ssm:parameter'):
            parameter_name = arn.split(':')[-1]
            client.get_parameter(Name=parameter_name)
        elif resource_type.startswith('backup:backup-plan'):
            backup_plan_name = arn.split(':')[-1]
            client.describe_backup_plan(BackupPlanName=backup_plan_name)
        return True
    except ClientError as e:
        logging.error(f"Error validating resource {arn}: {str(e)}")
        return False

# Dry-run operation (Simulation)
def dry_run_operation(arn, resource_type, session, cr_no):
    logging.info(f"Dry-run: Simulating deletion of {resource_type} with ARN {arn}")

    try:
        # Simulate deletion based on resource type
        if resource_type == 'ec2:instance':
            instance_id = arn.split(':')[-1]
            logging.info(f"Simulating termination of EC2 instance: {instance_id}")
        elif resource_type == 'ec2:image':
            image_id = arn.split(':')[-1]
            logging.info(f"Simulating deletion of EC2 image: {image_id}")
        elif resource_type == 'ec2:volume':
            volume_id = arn.split(':')[-1]
            logging.info(f"Simulating deletion of EC2 volume: {volume_id}")
        elif resource_type == 'ec2:snapshot':
            snapshot_id = arn.split(':')[-1]
            logging.info(f"Simulating deletion of EC2 snapshot: {snapshot_id}")
        elif resource_type == 'ec2:securitygroup':
            security_group_id = arn.split(':')[-1]
            logging.info(f"Simulating deletion of EC2 security group: {security_group_id}")
        elif resource_type == 'lambda':
            function_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Lambda function: {function_name}")
        elif resource_type == 'sns':
            topic_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of SNS topic: {topic_arn}")
        elif resource_type == 's3':
            bucket_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of S3 bucket: {bucket_name}")
        elif resource_type == 'cloudwatch:alarm':
            alarm_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of CloudWatch alarm: {alarm_name}")
        elif resource_type == 'secretsmanager:secret':
            secret_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Secrets Manager secret: {secret_name}")
        elif resource_type == 'states:stateMachine':
            state_machine_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Step Functions state machine: {state_machine_arn}")
        elif resource_type == 'events:rule':
            rule_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of EventBridge rule: {rule_name}")
        elif resource_type == 'cloudformation:stack':
            stack_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of CloudFormation stack: {stack_name}")
        elif resource_type == 'elbv2:listener':
            listener_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Elastic Load Balancer listener: {listener_arn}")
        elif resource_type == 'elbv2:targetgroup':
            target_group_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Elastic Load Balancer target group: {target_group_arn}")
        elif resource_type == 'elbv2:listener-rule':
            listener_rule_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of Elastic Load Balancer listener rule: {listener_rule_arn}")
        elif resource_type == 'ssm:parameter':
            parameter_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of SSM parameter: {parameter_name}")
        elif resource_type == 'backup:backup-plan':
            backup_plan_id = arn.split(':')[-1]
            logging.info(f"Simulating deletion of AWS Backup plan: {backup_plan_id}")
        elif resource_type == 'backup:backup-vault':
            backup_vault_name = arn.split(':')[-1]
            logging.info(f"Simulating deletion of AWS Backup vault: {backup_vault_name}")
        elif resource_type == 'sns:subscription':
            subscription_arn = arn.split(':')[-1]
            logging.info(f"Simulating deletion of SNS subscription: {subscription_arn}")
        else:
            logging.warning(f"Unsupported resource type {resource_type} for ARN {arn}")

        # Simulate success for the dry-run
        return True
    
    except Exception as e:
        logging.error(f"Error in dry-run for resource {arn}: {str(e)}")
        return False

# Actual delete operation
def delete_operation(arn, resource_type, session, cr_no):
    logging.info(f"{Fore.RED}Deleting resource {resource_type} with ARN {arn}")
    try:
        client = session.client(resource_type.split(':')[0])
        if resource_type.startswith('ec2'):
            if resource_type == 'ec2:instance':
                instance_id = arn.split(':')[-1]
                client.terminate_instances(InstanceIds=[instance_id])
        # Add more delete operations for other resource types as needed
        return True
    except ClientError as e:
        logging.error(f"Error deleting resource {arn}: {str(e)}")
        return False

# Generate CSV file for output
def generate_csv_output(results, cr_no):
    timestamp = datetime.now().strftime('%y-%m-%d_%H-%M')
    output_file = f"results/AWS_RESOURCE_{'DRYRUN' if args.dry_run else 'DECOM'}_{cr_no}_{timestamp}.csv"
    headers = ['CR No', 'Account Name', 'Account ID', 'Region', 'Service', 'Resource Type', 'Resource ID', 'Status', 'Error', 'Duration Sec']
    
    with open(output_file, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        for result in results:
            writer.writerow(result)
    
    logging.info(f"Results written to {output_file}")

# Get session for a given AWS profile
def get_session(profile_name):
    session = boto3.Session(profile_name=profile_name)
    return session

# Initial summary table
def generate_initial_summary(resources):
    summary = {}
    for resource_type in resources:
        summary[resource_type] = summary.get(resource_type, 0) + 1
    
    table = f"{'Service':<20} {'Resource Type':<20} {'Count':<10}\n"
    table += '-' * 50 + '\n'
    for resource_type, count in summary.items():
        table += f"{resource_type:<20} {resource_type.split(':')[1]:<20} {count:<10}\n"
    
    logging.info(f"Initial Summary:\n{table}")

# Main function to execute cleanup or dry-run operations
def main():
    parser = argparse.ArgumentParser(description="AWS Resource Cleanup Tool")
    parser.add_argument('--input', required=True, help="CSV file with ARNs")
    parser.add_argument('--dry-run', action='store_true', help="Simulate deletion without actual deletion")
    parser.add_argument('--delete', action='store_true', help="Delete resources (use --dry-run for simulation)")
    parser.add_argument('--profile', required=True, help="AWS profile to use")
    parser.add_argument('--cr', required=True, help="Change Request Number")

    args = parser.parse_args()
    cr_no = args.cr
    profile_name = args.profile
    
    # Read ARNs from input CSV file
    arns = []
    with open(args.input, 'r') as file:
        arns = file.readlines()

    # Generate initial summary table
    generate_initial_summary(arns)
    
    # Confirm with user before proceeding to deletion
    if not args.dry_run:
        confirm = input(f"Are you sure you want to DELETE resources with CR No: {cr_no}? (yes/no): ")
        if confirm.lower() != 'yes':
            logging.info("Operation cancelled.")
            return
    
    session = get_session(profile_name)
    
    results = []
    for arn in tqdm(arns, desc="Processing Resources", unit="resource"):
        resource_type, arn, resource_id = parse_arn(arn.strip())
        if resource_type == 'unknown':
            logging.warning(f"Unsupported ARN: {arn}")
            results.append([cr_no, 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', arn, 'Unsupported', 'N/A', 'N/A'])
            continue
        
        # Simulate or delete resource based on mode
        start_time = datetime.now()
        if args.dry_run:
            status = dry_run_operation(arn.strip(), resource_type, session, cr_no)
        else:
            status = delete_operation(arn.strip(), resource_type, session, cr_no)
        
        duration = (datetime.now() - start_time).total_seconds()
        
        # Store result
        results.append([cr_no, 'Account Name', 'Account ID', 'Region', resource_type, resource_type.split(':')[1], resource_id, 'Success' if status else 'Failed', 'N/A', duration])
    
    # Generate final CSV output
    generate_csv_output(results, cr_no)

if __name__ == '__main__':
    main()
