CR_Number,Account_Name,Account_ID,Service,Resource_Type,Resource_ID,Region,ARN,Created_Date,Validated,Validation_Message,Dry_Run_Success,Dry_Run_Message,Deletion_Success,Deletion_Message,Deleted_Date,Dependencies,Validation_Duration,Dry_Run_Duration,Deletion_Duration
GECHG1234567,Unknown,************,cloudformation,stack,UAI2004829-sparks-prd-i5,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-sparks-prd-i5/********-ee03-11ea-b35a-02dbc193ed28,2020-09-03 16:32:06,Yes,Stack UAI2004829-sparks-prd-i5 exists and can be deleted,Yes,Stack UAI2004829-sparks-prd-i5 can be deleted,Yes,Stack UAI2004829-sparks-prd-i5 deletion initiated,2025-05-12 18:28:50,None,3.74s,3.09s,3.56s
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5s-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2,,Yes,Target group i5s-sparks-prd-UAI2004829 exists and can be deleted,Yes,Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2,Yes,Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2,2025-05-12 18:30:43,registered_with generic:resource 10.222.99.115,4.02s,35.22s,36.31s
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5p-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-prd-UAI2004829/8ba285cac7d39a9d,,Yes,Target group i5p-sparks-prd-UAI2004829 exists and can be deleted,No,Target group i5p-sparks-prd-UAI2004829 not found,N/A,,,None,4.70s,3.52s,N/A
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5p-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-stg-UAI2004829/4dee790b9220ed70,,Yes,Target group i5p-sparks-stg-UAI2004829 exists and can be deleted,No,"Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929",N/A,,,"registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929",4.34s,N/A,N/A
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5s-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947,,Yes,Target group i5s-sparks-stg-UAI2004829 exists and can be deleted,No,"Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52",N/A,,,"registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52",4.75s,N/A,N/A
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5s-npi-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480,,Yes,Target group i5s-npi-sparks-stg-UAI2004829 exists and can be deleted,Yes,Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480,Yes,Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480,2025-05-12 18:34:20,registered_with generic:resource 10.222.117.130,4.07s,33.67s,35.94s
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5p-npi-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-npi-sparks-stg-UAI2004829/7d0c809bd9e2dc90,,Yes,Target group i5p-npi-sparks-stg-UAI2004829 exists and can be deleted,No,"Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4",N/A,,,"registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4",3.82s,N/A,N/A
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5p-gs-npi-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb,,Yes,Target group i5p-gs-npi-sparks-prd-UAI2004829 exists and can be deleted,Yes,Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb,Yes,Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb,2025-05-12 18:36:49,registered_with generic:resource 10.222.99.115,4.10s,33.64s,34.68s
GECHG1234567,Unknown,************,elasticloadbalancing,targetgroup,i5s-gs-npi-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680,,Yes,Target group i5s-gs-npi-sparks-prd-UAI2004829 exists and can be deleted,No,"Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f",N/A,,,"registered_with generic:resource 10.222.99.115, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f",3.74s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-sparks-prd-UAI2004829/134384004e42bdaf,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf,,No,Error: ValidationError - The load balancer name 'app/i5-sparks-prd-UAI2004829/134384004e42bdaf' cannot be longer than '32' characters,No,Resource not validated,N/A,,,None,2.99s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-sparks-stg-UAI2004829/ee1511167d12c523,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523,,No,Error: ValidationError - The load balancer name 'app/i5-sparks-stg-UAI2004829/ee1511167d12c523' cannot be longer than '32' characters,No,Resource not validated,N/A,,,None,2.88s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133,,No,Error: ValidationError - The load balancer name 'app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133' cannot be longer than '32' characters,No,Resource not validated,N/A,,,None,2.93s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db,,No,Error: ValidationError - The load balancer name 'app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db' cannot be longer than '32' characters,No,Resource not validated,N/A,,,None,2.88s,N/A,N/A
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,test-instance-emgs-sparks-qa-aws-app-xLorRk,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:test-instance-emgs-sparks-qa-aws-app-xLorRk,,No,Resource not found: Secrets Manager can't find the specified secret.,No,Resource not validated,N/A,,,None,3.27s,N/A,N/A
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,credentials_for_i-05eb965ac3fd8fb23-5oODKX,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:credentials_for_i-05eb965ac3fd8fb23-5oODKX,,No,Resource not found: Secrets Manager can't find the specified secret.,No,Resource not validated,N/A,,,None,5.71s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-02e58313d98d848b6,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-02e58313d98d848b6,,No,Security Group sg-02e58313d98d848b6 is being used by instances: i-05eb965ac3fd8fb23,No,Resource not validated,N/A,,,None,4.50s,N/A,N/A
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,credentials-for-i-002be9747294e8877-V87TL4,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:credentials-for-i-002be9747294e8877-V87TL4,,No,Resource not found: Secrets Manager can't find the specified secret.,No,Resource not validated,N/A,,,None,2.64s,N/A,N/A
GECHG1234567,Unknown,************,cloudwatch,alarm,UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7,,Yes,Alarm UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 exists and can be deleted,Yes,Alarm UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 can be deleted,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,3.11s,2.82s,3.00s
GECHG1234567,Unknown,************,cloudwatch,alarm,UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5,,Yes,Alarm UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 exists and can be deleted,Yes,Alarm UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 can be deleted,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,2.91s,2.99s,2.81s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0199ae65cc9c2346b,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0199ae65cc9c2346b,,No,Volume vol-0199ae65cc9c2346b is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.93s,N/A,N/A
GECHG1234567,Unknown,************,cloudwatch,alarm,UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW,,Yes,Alarm UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW exists and can be deleted,Yes,Alarm UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW can be deleted,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,2.88s,2.84s,3.08s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-09df14aa14c7e6408,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-09df14aa14c7e6408,,No,Volume vol-09df14aa14c7e6408 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,3.06s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,2.98s,N/A,N/A
GECHG1234567,Unknown,************,cloudwatch,alarm,UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B,,Yes,Alarm UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B exists and can be deleted,Yes,Alarm UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B can be deleted,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,2.92s,2.82s,2.90s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,2.90s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,2.86s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,2.84s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-00a54eeb559a244ba,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-00a54eeb559a244ba,,No,Volume vol-00a54eeb559a244ba is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,3.09s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,4.86s,N/A,N/A
GECHG1234567,Unknown,************,cloudformation,stack,UAI2004829-sparks-stg-i5,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-sparks-stg-i5/c35431d0-ec5d-11ea-aa84-06badcce5684,2020-09-01 14:16:44,Yes,Stack UAI2004829-sparks-stg-i5 exists and can be deleted,Yes,Stack UAI2004829-sparks-stg-i5 can be deleted,Yes,Stack UAI2004829-sparks-stg-i5 deletion initiated,2025-05-12 18:40:48,None,4.67s,3.32s,3.85s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-0c13321fab05fc3a2,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-0c13321fab05fc3a2,,No,"Security Group sg-0c13321fab05fc3a2 is being used by network interfaces: eni-044801dafa58870a7, eni-09d2729757d9b0baa",No,Resource not validated,N/A,,,None,4.54s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-03255a7c76bfd0a7c,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-03255a7c76bfd0a7c,,No,"Security Group sg-03255a7c76bfd0a7c is being used by network interfaces: eni-09d9fd9342a8403bb, eni-03d88784681662040",No,Resource not validated,N/A,,,None,4.38s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08c1f59c7edbadcd9,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08c1f59c7edbadcd9,,No,Volume vol-08c1f59c7edbadcd9 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.86s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,3.02s,N/A,N/A
GECHG1234567,Unknown,************,cloudformation,stack,UAI2004829-npi-sparks-stg-i5,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-npi-sparks-stg-i5/8afd5a20-a376-11eb-9496-066f6230c66b,2021-04-22 14:25:10,Yes,Stack UAI2004829-npi-sparks-stg-i5 exists and can be deleted,Yes,Stack UAI2004829-npi-sparks-stg-i5 can be deleted,Yes,Stack UAI2004829-npi-sparks-stg-i5 deletion initiated,2025-05-12 18:41:37,None,3.39s,2.87s,3.58s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4,,No,Error: ValidationError - 'app' is not a valid listener ARN,No,Resource not validated,N/A,,,None,2.78s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-087df90f8ba056c60,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-087df90f8ba056c60,,No,"Security Group sg-087df90f8ba056c60 is being used by network interfaces: eni-037112f1a1d94d95a, eni-06fb22907186a9c83",No,Resource not validated,N/A,,,None,4.21s,N/A,N/A
GECHG1234567,Unknown,************,cloudformation,stack,UAI2004829-gs-npi-sparks-prd-i5,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-gs-npi-sparks-prd-i5/739afec0-a379-11eb-8760-0a1c071ebc27,2021-04-22 14:45:59,Yes,Stack UAI2004829-gs-npi-sparks-prd-i5 exists and can be deleted,Yes,Stack UAI2004829-gs-npi-sparks-prd-i5 can be deleted,Yes,Stack UAI2004829-gs-npi-sparks-prd-i5 deletion initiated,2025-05-12 18:42:11,None,3.43s,2.86s,3.48s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.59s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f/324b6ea53bdb69c2,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.79s,N/A,N/A
GECHG1234567,Unknown,************,cloudformation,stack,ren-uai2004829-emgs-sparks-qa-win2019EC2,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/ren-uai2004829-emgs-sparks-qa-win2019EC2/aa1c7b80-d17b-11ee-abcc-026ee85557df,2024-02-22 12:12:36,Yes,Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 exists and can be deleted,Yes,Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 can be deleted,Yes,Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 deletion initiated,2025-05-12 18:42:44,None,3.43s,2.75s,3.39s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.79s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f/58545befb00779d0,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.76s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-09d098800c98f744a,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-09d098800c98f744a,,No,"Security Group sg-09d098800c98f744a is being used by network interfaces: eni-0fa7cecc085475be4, eni-0a7af7611b2943369",No,Resource not validated,N/A,,,None,4.56s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.95s,N/A,N/A
GECHG1234567,Unknown,************,cloudformation,stack,ren-test-instance-uai2004829-emgs-sparks-ec2,eu-west-1,arn:aws:cloudformation:eu-west-1:************:stack/ren-test-instance-uai2004829-emgs-sparks-ec2/bc0d1ef0-d6e7-11ee-a39b-06537f2d69af,2024-02-29 09:48:47,Yes,Stack ren-test-instance-uai2004829-emgs-sparks-ec2 exists and can be deleted,Yes,Stack ren-test-instance-uai2004829-emgs-sparks-ec2 can be deleted,Yes,Stack ren-test-instance-uai2004829-emgs-sparks-ec2 deletion initiated,2025-05-12 18:43:31,None,3.34s,2.79s,3.57s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.81s,N/A,N/A
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f,,No,Error: ValidationError - 'app' is not a valid listener rule ARN,No,Resource not validated,N/A,,,None,2.84s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,instance,i-002be9747294e8877,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-002be9747294e8877,,No,Instance i-002be9747294e8877 is already terminated,No,Resource not validated,N/A,,,None,2.82s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,instance,i-05eb965ac3fd8fb23,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-05eb965ac3fd8fb23,,No,Instance i-05eb965ac3fd8fb23 is already shutting down,No,Resource not validated,N/A,,,None,2.83s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0321aa3341012dc5d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0321aa3341012dc5d,,No,Volume vol-0321aa3341012dc5d is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.96s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08292e054f597a702,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08292e054f597a702,,No,Volume vol-08292e054f597a702 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.91s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0f23739b81fce377d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0f23739b81fce377d,,No,Volume vol-0f23739b81fce377d is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.89s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0a61a13260c3b8461,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0a61a13260c3b8461,,No,Volume vol-0a61a13260c3b8461 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.85s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0b51ed17665aabeae,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0b51ed17665aabeae,,No,Volume vol-0b51ed17665aabeae is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,3.07s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-002d9c016266809f2,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-002d9c016266809f2,,No,Volume vol-002d9c016266809f2 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.85s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0cd56d66953e6c11f,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0cd56d66953e6c11f,,No,Volume vol-0cd56d66953e6c11f is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.81s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-05b68331cc9022940,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-05b68331cc9022940,,No,Volume vol-05b68331cc9022940 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.81s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08a25ca18b9d2ffbe,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08a25ca18b9d2ffbe,,No,Volume vol-08a25ca18b9d2ffbe is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.82s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0d221edcc77bf155d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0d221edcc77bf155d,,No,Volume vol-0d221edcc77bf155d is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.82s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08a697f12101a69f5,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08a697f12101a69f5,,No,Volume vol-08a697f12101a69f5 is in use and force_detach_volumes is not enabled,No,Resource not validated,N/A,,,None,2.88s,N/A,N/A
GECHG1234567,ent-emea-energy,************,ec2,instance,i-01ff028a35ce74562,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-01ff028a35ce74562,,No,Instance i-01ff028a35ce74562 has termination protection enabled,No,Resource not validated,N/A,,,None,3.50s,N/A,N/A
GECHG1234567,Unknown,************,ec2,instance,i-0915c5900e50f84c8,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-0915c5900e50f84c8,2023-04-19 05:35:03,Yes,Instance i-0915c5900e50f84c8 exists and can be terminated,Yes,"Dry run successful: Request would have succeeded, but DryRun flag is set.",Yes,Instance state changed from running to shutting-down,2025-05-12 18:45:23,"attached_to ec2:volume vol-08a697f12101a69f5, attached_to ec2:volume vol-0321aa3341012dc5d, attached_to ec2:volume vol-08292e054f597a702, attached_to ec2:volume vol-0d221edcc77bf155d, attached_to ec2:volume vol-002d9c016266809f2, attached_to ec2:network-interface eni-f4c533f1",3.57s,2.75s,3.58s
GECHG1234567,Unknown,************,ec2,instance,i-05c4fe16dcfb7f9ad,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-05c4fe16dcfb7f9ad,2025-05-09 22:30:28,Yes,Instance i-05c4fe16dcfb7f9ad exists and can be terminated,Yes,"Dry run successful: Request would have succeeded, but DryRun flag is set.",Yes,Instance state changed from running to shutting-down,2025-05-12 18:45:46,"attached_to ec2:volume vol-0cd56d66953e6c11f, attached_to ec2:volume vol-08a25ca18b9d2ffbe, attached_to ec2:network-interface eni-36b77a35",3.59s,2.79s,3.58s
GECHG1234567,Unknown,************,ec2,instance,i-0eddd32173f4a26c2,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-0eddd32173f4a26c2,2025-05-09 22:30:28,Yes,Instance i-0eddd32173f4a26c2 exists and can be terminated,Yes,"Dry run successful: Request would have succeeded, but DryRun flag is set.",Yes,Instance state changed from running to shutting-down,2025-05-12 18:46:11,"attached_to ec2:volume vol-0a61a13260c3b8461, attached_to ec2:volume vol-0f23739b81fce377d, attached_to ec2:network-interface eni-a67643a6",3.61s,2.94s,3.73s
GECHG1234567,Unknown,************,ec2,snapshot,snap-071322acf3e25c156,eu-west-1,arn:aws:ec2:eu-west-1:************:snapshot/snap-071322acf3e25c156,2025-02-21 16:30:39,Yes,Snapshot snap-071322acf3e25c156 exists and can be deleted,Yes,"Dry run successful: Request would have succeeded, but DryRun flag is set.",Yes,Snapshot snap-071322acf3e25c156 deleted successfully,2025-05-12 18:46:36,None,4.80s,2.72s,4.80s
