"""
ELB Target Group handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class ELBTargetGroupHandler(BaseResourceHandler):
    """
    Handler for Elastic Load Balancer Target Groups.
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the ELB Target Group handler.

        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)

    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a target group exists and can be deleted.

        Args:
            resource: Resource to validate.
            session: boto3 session.

        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)

            # Extract target group ARN from resource ID
            tg_id = resource.resource_id

            # Check if target group exists
            response = elbv2.describe_target_groups(
                TargetGroupArns=[tg_id] if tg_id.startswith('arn:') else []
            )

            if not response.get('TargetGroups'):
                return False, f"Target group {tg_id} not found"

            return True, f"Target group {tg_id} exists and can be deleted"

        except ClientError as e:
            return self.handle_client_error(e, "validating target group")

    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a target group.

        Args:
            resource: Resource to check.
            session: boto3 session.

        Returns:
            List of dependencies.
        """
        dependencies = []

        try:
            elbv2 = session.client('elbv2', region_name=resource.region)

            # Extract target group ARN from resource ID
            tg_id = resource.resource_id

            # Get target group ARN if we only have the name
            if not tg_id.startswith('arn:'):
                try:
                    # Try to find the target group by name
                    response = elbv2.describe_target_groups(Names=[tg_id])
                    if response.get('TargetGroups'):
                        tg_id = response['TargetGroups'][0]['TargetGroupArn']
                    else:
                        self.logger.warning(f"Target group with name {tg_id} not found")
                        return dependencies
                except ClientError as e:
                    self.logger.warning(f"Error finding target group by name {tg_id}: {e}")
                    return dependencies

            # Check for registered targets
            try:
                targets_response = elbv2.describe_target_health(TargetGroupArn=tg_id)

                for target in targets_response.get('TargetHealthDescriptions', []):
                    target_id = target['Target']['Id']
                    target_port = target['Target'].get('Port')

                    # Determine target type based on ID format
                    if target_id.startswith('i-'):
                        target_type = "ec2:instance"
                    elif target_id.startswith('ip-'):
                        target_type = "generic:ip"
                    else:
                        target_type = "generic:resource"

                    dependencies.append(ResourceDependency(
                        resource_type=target_type,
                        resource_id=target_id,
                        relationship="registered_with",
                        blocking=False  # Not blocking because targets can be deregistered
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking targets for target group {tg_id}: {e}")

            # Check for load balancer associations
            try:
                # Get target group details
                tg_response = elbv2.describe_target_groups(TargetGroupArns=[tg_id])

                if tg_response.get('TargetGroups'):
                    target_group = tg_response['TargetGroups'][0]

                    # Check load balancer ARNs
                    for lb in target_group.get('LoadBalancerArns', []):
                        dependencies.append(ResourceDependency(
                            resource_type="elasticloadbalancing:loadbalancer",
                            resource_id=lb,
                            relationship="associated_with",
                            blocking=True  # Blocking because target group must be disassociated from load balancers
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking load balancers for target group {tg_id}: {e}")

            # Check for listener rules using this target group
            try:
                # We need to check all listeners on all load balancers
                # This is a bit inefficient but there's no direct API to find rules by target group

                # First, get all load balancers
                lb_response = elbv2.describe_load_balancers()

                for lb in lb_response.get('LoadBalancers', []):
                    lb_arn = lb['LoadBalancerArn']

                    # Get all listeners for this load balancer
                    try:
                        listeners_response = elbv2.describe_listeners(LoadBalancerArn=lb_arn)

                        for listener in listeners_response.get('Listeners', []):
                            listener_arn = listener['ListenerArn']

                            # Check if the default action points to our target group
                            for action in listener.get('DefaultActions', []):
                                if action.get('Type') == 'forward' and action.get('TargetGroupArn') == tg_id:
                                    dependencies.append(ResourceDependency(
                                        resource_type="elasticloadbalancing:listener",
                                        resource_id=listener_arn,
                                        relationship="forwards_to",
                                        blocking=True  # Blocking because listener must be modified
                                    ))

                            # Get all rules for this listener
                            try:
                                rules_response = elbv2.describe_rules(ListenerArn=listener_arn)

                                for rule in rules_response.get('Rules', []):
                                    rule_arn = rule['RuleArn']

                                    # Check if any action points to our target group
                                    for action in rule.get('Actions', []):
                                        if action.get('Type') == 'forward' and action.get('TargetGroupArn') == tg_id:
                                            dependencies.append(ResourceDependency(
                                                resource_type="elasticloadbalancing:listener-rule",
                                                resource_id=rule_arn,
                                                relationship="forwards_to",
                                                blocking=True  # Blocking because rule must be modified
                                            ))
                            except ClientError as e:
                                self.logger.warning(f"Error checking rules for listener {listener_arn}: {e}")
                    except ClientError as e:
                        self.logger.warning(f"Error checking listeners for load balancer {lb_arn}: {e}")
            except ClientError as e:
                self.logger.warning(f"Error checking load balancers: {e}")

            return dependencies

        except ClientError as e:
            self.logger.error(f"Error checking dependencies for target group {resource.resource_id}: {e}")
            return dependencies

    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of target group deletion.

        Args:
            resource: Resource to delete.
            session: boto3 session.

        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)

            # Extract target group ARN from resource ID
            tg_id = resource.resource_id

            # Check if target group exists
            if tg_id.startswith('arn:'):
                response = elbv2.describe_target_groups(TargetGroupArns=[tg_id])
            else:
                try:
                    response = elbv2.describe_target_groups(Names=[tg_id])
                    if response.get('TargetGroups'):
                        tg_id = response['TargetGroups'][0]['TargetGroupArn']
                except ClientError as e:
                    self.logger.warning(f"Error finding target group by name {tg_id}: {e}")
                    response = {'TargetGroups': []}

            if not response.get('TargetGroups'):
                return False, f"Target group {tg_id} not found"

            # Check for dependencies
            dependencies = self.check_dependencies(resource, session)
            blocking_dependencies = [dep for dep in dependencies if dep.blocking]

            if blocking_dependencies:
                dep_str = ", ".join([f"{dep.relationship} {dep.resource_type} {dep.resource_id}" for dep in blocking_dependencies])
                return False, f"Target group {tg_id} has blocking dependencies: {dep_str}"

            # Check for registered targets
            try:
                # At this point, tg_id should be an ARN (either it was originally or we converted it above)
                targets_response = elbv2.describe_target_health(TargetGroupArn=tg_id)

                if targets_response.get('TargetHealthDescriptions'):
                    target_count = len(targets_response['TargetHealthDescriptions'])
                    return True, f"Dry run successful: Would deregister {target_count} targets and delete target group {tg_id}"
            except ClientError as e:
                self.logger.warning(f"Error checking targets for target group {tg_id}: {e}")

            return True, f"Dry run successful: Would delete target group {tg_id}"

        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of target group")

    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a target group.

        Args:
            resource: Resource to delete.
            session: boto3 session.

        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)

            # Extract target group ARN from resource ID
            tg_id = resource.resource_id

            # Check if target group exists
            if tg_id.startswith('arn:'):
                response = elbv2.describe_target_groups(TargetGroupArns=[tg_id])
            else:
                try:
                    response = elbv2.describe_target_groups(Names=[tg_id])
                    if response.get('TargetGroups'):
                        tg_id = response['TargetGroups'][0]['TargetGroupArn']
                except ClientError as e:
                    self.logger.warning(f"Error finding target group by name {tg_id}: {e}")
                    response = {'TargetGroups': []}

            if not response.get('TargetGroups'):
                return False, f"Target group {tg_id} not found"

            # Check for dependencies
            dependencies = self.check_dependencies(resource, session)
            blocking_dependencies = [dep for dep in dependencies if dep.blocking]

            if blocking_dependencies:
                dep_str = ", ".join([f"{dep.relationship} {dep.resource_type} {dep.resource_id}" for dep in blocking_dependencies])
                return False, f"Target group {tg_id} has blocking dependencies: {dep_str}"

            # Deregister all targets
            try:
                # At this point, tg_id should be an ARN (either it was originally or we converted it above)
                targets_response = elbv2.describe_target_health(TargetGroupArn=tg_id)

                if targets_response.get('TargetHealthDescriptions'):
                    targets = []
                    for target in targets_response['TargetHealthDescriptions']:
                        targets.append(target['Target'])

                    if targets:
                        elbv2.deregister_targets(
                            TargetGroupArn=tg_id,
                            Targets=targets
                        )
                        self.logger.info(f"Deregistered {len(targets)} targets from target group {tg_id}")
            except ClientError as e:
                self.logger.warning(f"Error deregistering targets from target group {tg_id}: {e}")
                return False, f"Error deregistering targets: {str(e)}"

            # Delete the target group
            elbv2.delete_target_group(TargetGroupArn=tg_id)

            return True, f"Successfully deleted target group {tg_id}"

        except ClientError as e:
            return self.handle_client_error(e, "deletion of target group")

    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a target group.

        Args:
            resource: Resource to get creation date for.
            session: boto3 session.

        Returns:
            Creation date as string or None if not available.
        """
        # Target groups don't have a creation date available through the API
        return None

    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a target group.

        Args:
            resource: Resource to get tags for.
            session: boto3 session.

        Returns:
            Dict of tags.
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)

            # Extract target group ARN from resource ID
            tg_id = resource.resource_id

            # Get target group ARN if we only have the name
            if not tg_id.startswith('arn:'):
                try:
                    # Try to find the target group by name
                    response = elbv2.describe_target_groups(Names=[tg_id])
                    if response.get('TargetGroups'):
                        tg_id = response['TargetGroups'][0]['TargetGroupArn']
                    else:
                        self.logger.warning(f"Target group with name {tg_id} not found")
                        return {}
                except ClientError as e:
                    self.logger.warning(f"Error finding target group by name {tg_id}: {e}")
                    return {}

            # Get tags for the target group
            response = elbv2.describe_tags(ResourceArns=[tg_id])

            tags = {}
            for tag_desc in response.get('TagDescriptions', []):
                if tag_desc.get('ResourceArn') == tg_id:
                    for tag in tag_desc.get('Tags', []):
                        tags[tag['Key']] = tag['Value']

            return tags

        except ClientError as e:
            self.logger.error(f"Error getting tags for target group {resource.resource_id}: {e}")
            return {}
