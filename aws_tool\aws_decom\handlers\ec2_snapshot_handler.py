"""
EC2 Snapshot handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tu<PERSON>, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EC2SnapshotHandler(BaseResourceHandler):
    """
    Handler for EC2 snapshots.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EC2 snapshot handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EC2 snapshot exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if snapshot exists
            response = ec2.describe_snapshots(SnapshotIds=[resource.resource_id])
            
            # Check if there are any snapshots
            if not response.get('Snapshots'):
                return False, f"Snapshot {resource.resource_id} not found"
            
            # Get snapshot state
            snapshot = response['Snapshots'][0]
            state = snapshot.get('State')
            
            # Check if snapshot is in a valid state for deletion
            if state not in ['completed', 'error']:
                return False, f"Snapshot {resource.resource_id} is in {state} state and cannot be deleted"
            
            # Check if snapshot is being used by an AMI
            try:
                ami_response = ec2.describe_images(Filters=[
                    {'Name': 'block-device-mapping.snapshot-id', 'Values': [resource.resource_id]}
                ])
                
                if ami_response.get('Images'):
                    force_delete = self.config.get("resources.services.ec2.force_snapshot_deletion", False)
                    if not force_delete:
                        ami_ids = [image['ImageId'] for image in ami_response['Images']]
                        return False, f"Snapshot {resource.resource_id} is used by AMIs: {', '.join(ami_ids)}"
            except ClientError as e:
                self.logger.warning(f"Error checking AMI usage: {e}")
            
            return True, f"Snapshot {resource.resource_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EC2 snapshot")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EC2 snapshot deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if snapshot is being used by an AMI
            try:
                ami_response = ec2.describe_images(Filters=[
                    {'Name': 'block-device-mapping.snapshot-id', 'Values': [resource.resource_id]}
                ])
                
                for image in ami_response.get('Images', []):
                    dependencies.append(ResourceDependency(
                        resource_type="ec2:image",
                        resource_id=image['ImageId'],
                        relationship="used_by",
                        blocking=True  # Blocking because AMI must be deregistered first
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking AMI usage: {e}")
            
            # Check if snapshot is shared with other accounts
            try:
                snapshot_response = ec2.describe_snapshots(SnapshotIds=[resource.resource_id])
                
                if snapshot_response.get('Snapshots'):
                    snapshot = snapshot_response['Snapshots'][0]
                    
                    # Check for public sharing
                    if snapshot.get('Public', False):
                        dependencies.append(ResourceDependency(
                            resource_type="ec2:snapshot-permission",
                            resource_id=f"{resource.resource_id}/public",
                            relationship="shared_with",
                            blocking=False  # Not blocking because permissions are removed with snapshot
                        ))
                    
                    # Check for specific account sharing
                    try:
                        permission_response = ec2.describe_snapshot_attribute(
                            SnapshotId=resource.resource_id,
                            Attribute='createVolumePermission'
                        )
                        
                        for permission in permission_response.get('CreateVolumePermissions', []):
                            if 'UserId' in permission:
                                dependencies.append(ResourceDependency(
                                    resource_type="ec2:snapshot-permission",
                                    resource_id=f"{resource.resource_id}/{permission['UserId']}",
                                    relationship="shared_with",
                                    blocking=False  # Not blocking because permissions are removed with snapshot
                                ))
                    except ClientError as e:
                        self.logger.warning(f"Error checking snapshot permissions: {e}")
            except ClientError as e:
                self.logger.warning(f"Error checking snapshot details: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EC2 snapshot {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EC2 snapshot deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Try to delete with DryRun=True
            ec2.delete_snapshot(
                SnapshotId=resource.resource_id,
                DryRun=True
            )
            
            # If we get here, something went wrong (DryRun should raise an exception)
            return False, "Unexpected success in dry run mode"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of EC2 snapshot")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an EC2 snapshot.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if snapshot is being used by an AMI
            try:
                ami_response = ec2.describe_images(Filters=[
                    {'Name': 'block-device-mapping.snapshot-id', 'Values': [resource.resource_id]}
                ])
                
                if ami_response.get('Images'):
                    force_delete = self.config.get("resources.services.ec2.force_snapshot_deletion", False)
                    if not force_delete:
                        ami_ids = [image['ImageId'] for image in ami_response['Images']]
                        return False, f"Snapshot {resource.resource_id} is used by AMIs: {', '.join(ami_ids)}"
                    else:
                        # Deregister AMIs that use this snapshot
                        for image in ami_response['Images']:
                            ami_id = image['ImageId']
                            self.logger.info(f"Deregistering AMI {ami_id} that uses snapshot {resource.resource_id}")
                            
                            try:
                                ec2.deregister_image(ImageId=ami_id)
                            except ClientError as e:
                                self.logger.error(f"Error deregistering AMI {ami_id}: {e}")
                                return False, f"Error deregistering AMI {ami_id}: {str(e)}"
            except ClientError as e:
                self.logger.warning(f"Error checking AMI usage: {e}")
            
            # Delete the snapshot
            ec2.delete_snapshot(SnapshotId=resource.resource_id)
            
            return True, f"Snapshot {resource.resource_id} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting EC2 snapshot")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an EC2 snapshot.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_snapshots(SnapshotIds=[resource.resource_id])
            
            if not response.get('Snapshots'):
                return None
            
            snapshot = response['Snapshots'][0]
            start_time = snapshot.get('StartTime')
            
            if start_time:
                return start_time.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for EC2 snapshot {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EC2 snapshot.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_snapshots(SnapshotIds=[resource.resource_id])
            
            if not response.get('Snapshots'):
                return {}
            
            snapshot = response['Snapshots'][0]
            tags = snapshot.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EC2 snapshot {resource.resource_id}: {e}")
            return {}
