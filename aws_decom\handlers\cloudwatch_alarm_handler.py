"""
CloudWatch Alarm handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class CloudWatchAlarmHandler(BaseResourceHandler):
    """
    Handler for CloudWatch Alarms.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the CloudWatch Alarm handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a CloudWatch Alarm exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cloudwatch = session.client('cloudwatch', region_name=resource.region)
            
            # Extract alarm name from ARN or resource ID
            alarm_name = resource.resource_id
            if '/' in alarm_name:
                alarm_name = alarm_name.split('/')[-1]
            
            # Check if alarm exists
            response = cloudwatch.describe_alarms(AlarmNames=[alarm_name])
            
            # Check if there are any alarms
            if not response.get('MetricAlarms') and not response.get('CompositeAlarms'):
                return False, f"Alarm {alarm_name} not found"
            
            # Check if alarm is a composite alarm
            is_composite = False
            if response.get('CompositeAlarms'):
                is_composite = True
                self.logger.info(f"Alarm {alarm_name} is a composite alarm")
            
            # Check if alarm is being used by any actions
            alarm = None
            if is_composite and response.get('CompositeAlarms'):
                alarm = response['CompositeAlarms'][0]
            elif response.get('MetricAlarms'):
                alarm = response['MetricAlarms'][0]
            
            if alarm:
                actions = []
                actions.extend(alarm.get('AlarmActions', []))
                actions.extend(alarm.get('OKActions', []))
                actions.extend(alarm.get('InsufficientDataActions', []))
                
                if actions:
                    self.logger.info(f"Alarm {alarm_name} has {len(actions)} actions")
            
            return True, f"Alarm {alarm_name} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating CloudWatch Alarm")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent CloudWatch Alarm deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            cloudwatch = session.client('cloudwatch', region_name=resource.region)
            
            # Extract alarm name from ARN or resource ID
            alarm_name = resource.resource_id
            if '/' in alarm_name:
                alarm_name = alarm_name.split('/')[-1]
            
            # Check if alarm exists
            response = cloudwatch.describe_alarms(AlarmNames=[alarm_name])
            
            # Check if alarm is a composite alarm
            is_composite = False
            if response.get('CompositeAlarms'):
                is_composite = True
            
            # Get alarm details
            alarm = None
            if is_composite and response.get('CompositeAlarms'):
                alarm = response['CompositeAlarms'][0]
            elif response.get('MetricAlarms'):
                alarm = response['MetricAlarms'][0]
            
            if not alarm:
                return dependencies
            
            # Check for actions
            actions = []
            actions.extend(alarm.get('AlarmActions', []))
            actions.extend(alarm.get('OKActions', []))
            actions.extend(alarm.get('InsufficientDataActions', []))
            
            for action in actions:
                # Parse action ARN to determine type
                if ':sns:' in action:
                    # SNS topic action
                    dependencies.append(ResourceDependency(
                        resource_type="sns:topic",
                        resource_id=action.split(':')[-1],
                        relationship="notifies",
                        blocking=False  # Not blocking because alarm can be deleted without affecting SNS topic
                    ))
                elif ':autoscaling:' in action:
                    # Auto Scaling action
                    dependencies.append(ResourceDependency(
                        resource_type="autoscaling:policy",
                        resource_id=action.split(':')[-1],
                        relationship="triggers",
                        blocking=False  # Not blocking because alarm can be deleted without affecting ASG policy
                    ))
                elif ':swf:' in action:
                    # Simple Workflow action
                    dependencies.append(ResourceDependency(
                        resource_type="swf:action",
                        resource_id=action.split(':')[-1],
                        relationship="triggers",
                        blocking=False  # Not blocking because alarm can be deleted without affecting SWF
                    ))
                elif ':lambda:' in action:
                    # Lambda function action
                    dependencies.append(ResourceDependency(
                        resource_type="lambda:function",
                        resource_id=action.split(':')[-1],
                        relationship="invokes",
                        blocking=False  # Not blocking because alarm can be deleted without affecting Lambda
                    ))
                else:
                    # Unknown action type
                    dependencies.append(ResourceDependency(
                        resource_type="unknown:action",
                        resource_id=action,
                        relationship="triggers",
                        blocking=False  # Not blocking because we don't know what it is
                    ))
            
            # Check if this alarm is used by any composite alarms
            try:
                composite_response = cloudwatch.describe_alarms(AlarmTypes=['CompositeAlarm'])
                
                for composite_alarm in composite_response.get('CompositeAlarms', []):
                    alarm_rule = composite_alarm.get('AlarmRule', '')
                    
                    # Check if this alarm is referenced in the composite alarm rule
                    if f"'{alarm_name}'" in alarm_rule or f'"{alarm_name}"' in alarm_rule:
                        dependencies.append(ResourceDependency(
                            resource_type="cloudwatch:alarm",
                            resource_id=composite_alarm['AlarmName'],
                            relationship="used_by",
                            blocking=True  # Blocking because composite alarm depends on this alarm
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking composite alarm dependencies: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for CloudWatch Alarm {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of CloudWatch Alarm deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cloudwatch = session.client('cloudwatch', region_name=resource.region)
            
            # Extract alarm name from ARN or resource ID
            alarm_name = resource.resource_id
            if '/' in alarm_name:
                alarm_name = alarm_name.split('/')[-1]
            
            # Check if alarm exists
            response = cloudwatch.describe_alarms(AlarmNames=[alarm_name])
            
            # Check if there are any alarms
            if not response.get('MetricAlarms') and not response.get('CompositeAlarms'):
                return False, f"Alarm {alarm_name} not found"
            
            # Check if alarm is a composite alarm
            is_composite = False
            if response.get('CompositeAlarms'):
                is_composite = True
                self.logger.info(f"Dry run: Would delete composite alarm {alarm_name}")
            else:
                self.logger.info(f"Dry run: Would delete metric alarm {alarm_name}")
            
            return True, f"Alarm {alarm_name} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of CloudWatch Alarm")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a CloudWatch Alarm.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cloudwatch = session.client('cloudwatch', region_name=resource.region)
            
            # Extract alarm name from ARN or resource ID
            alarm_name = resource.resource_id
            if '/' in alarm_name:
                alarm_name = alarm_name.split('/')[-1]
            
            # Delete the alarm
            cloudwatch.delete_alarms(AlarmNames=[alarm_name])
            
            return True, f"Alarm {alarm_name} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting CloudWatch Alarm")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of a CloudWatch Alarm.
        
        Note: CloudWatch API doesn't provide creation date for alarms directly.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        # CloudWatch API doesn't provide creation date for alarms
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a CloudWatch Alarm.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            cloudwatch = session.client('cloudwatch', region_name=resource.region)
            
            # Extract alarm name from ARN or resource ID
            alarm_name = resource.resource_id
            if '/' in alarm_name:
                alarm_name = alarm_name.split('/')[-1]
            
            # Get alarm ARN
            response = cloudwatch.describe_alarms(AlarmNames=[alarm_name])
            
            alarm_arn = None
            if response.get('MetricAlarms'):
                alarm_arn = response['MetricAlarms'][0]['AlarmArn']
            elif response.get('CompositeAlarms'):
                alarm_arn = response['CompositeAlarms'][0]['AlarmArn']
            
            if not alarm_arn:
                return {}
            
            # Get tags for the alarm
            tags_response = cloudwatch.list_tags_for_resource(ResourceARN=alarm_arn)
            
            tags = tags_response.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for CloudWatch Alarm {resource.resource_id}: {e}")
            return {}
