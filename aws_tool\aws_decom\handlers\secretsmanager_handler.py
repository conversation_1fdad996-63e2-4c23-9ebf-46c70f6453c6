"""
Secrets Manager Secret handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class SecretsManagerSecretHandler(BaseResourceHandler):
    """
    Handler for Secrets Manager Secrets.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the Secrets Manager Secret handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a Secrets Manager Secret exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Check if secret exists
            response = secretsmanager.describe_secret(SecretId=secret_id)
            
            # Check if secret is already scheduled for deletion
            if 'DeletedDate' in response:
                return False, f"Secret {secret_id} is already scheduled for deletion"
            
            # Check if secret is being replicated to other regions
            if 'ReplicationStatus' in response:
                replication_regions = [rep['Region'] for rep in response.get('ReplicationStatus', [])]
                if replication_regions:
                    self.logger.info(f"Secret {secret_id} is replicated to regions: {', '.join(replication_regions)}")
            
            return True, f"Secret {secret_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating Secrets Manager Secret")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent Secrets Manager Secret deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Check if secret exists
            response = secretsmanager.describe_secret(SecretId=secret_id)
            
            # Check if secret is being replicated to other regions
            if 'ReplicationStatus' in response:
                for replication in response.get('ReplicationStatus', []):
                    region = replication.get('Region')
                    if region:
                        dependencies.append(ResourceDependency(
                            resource_type="secretsmanager:replica",
                            resource_id=f"{secret_id}/{region}",
                            relationship="replicated_to",
                            blocking=False  # Not blocking because replicas are deleted with the primary
                        ))
            
            # Check for resource policies
            try:
                policy_response = secretsmanager.get_resource_policy(SecretId=secret_id)
                
                if policy_response.get('ResourcePolicy'):
                    dependencies.append(ResourceDependency(
                        resource_type="secretsmanager:policy",
                        resource_id=f"{secret_id}/policy",
                        relationship="has",
                        blocking=False  # Not blocking because policy is deleted with the secret
                    ))
            except ClientError as e:
                if e.response['Error']['Code'] != 'ResourceNotFoundException':
                    self.logger.warning(f"Error checking resource policy: {e}")
            
            # Check for Lambda functions using this secret
            try:
                lambda_client = session.client('lambda', region_name=resource.region)
                functions = lambda_client.list_functions()
                
                for function in functions.get('Functions', []):
                    # Check environment variables for secret ARN
                    env_vars = function.get('Environment', {}).get('Variables', {})
                    for var_name, var_value in env_vars.items():
                        if resource.full_arn in var_value:
                            dependencies.append(ResourceDependency(
                                resource_type="lambda:function",
                                resource_id=function['FunctionName'],
                                relationship="used_by",
                                blocking=True  # Blocking because Lambda function depends on this secret
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking Lambda dependencies: {e}")
            
            # Check for RDS instances using this secret
            try:
                rds_client = session.client('rds', region_name=resource.region)
                instances = rds_client.describe_db_instances()
                
                for instance in instances.get('DBInstances', []):
                    # Check if instance uses this secret for master credentials
                    if instance.get('MasterUserSecret', {}).get('SecretArn') == resource.full_arn:
                        dependencies.append(ResourceDependency(
                            resource_type="rds:db",
                            resource_id=instance['DBInstanceIdentifier'],
                            relationship="used_by",
                            blocking=True  # Blocking because RDS instance depends on this secret
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking RDS dependencies: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for Secrets Manager Secret {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of Secrets Manager Secret deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Check if secret exists
            response = secretsmanager.describe_secret(SecretId=secret_id)
            
            # Check if secret is already scheduled for deletion
            if 'DeletedDate' in response:
                return False, f"Secret {secret_id} is already scheduled for deletion"
            
            # Check if secret has replicas
            has_replicas = False
            if 'ReplicationStatus' in response:
                replicas = response.get('ReplicationStatus', [])
                if replicas:
                    has_replicas = True
                    self.logger.info(f"Dry run: Would delete secret {secret_id} with {len(replicas)} replicas")
            
            # Get recovery window
            recovery_window = self.config.get("resources.services.secretsmanager.recovery_window_in_days", 30)
            force_delete = self.config.get("resources.services.secretsmanager.force_delete_without_recovery", False)
            
            if force_delete:
                self.logger.info(f"Dry run: Would delete secret {secret_id} without recovery window")
            else:
                self.logger.info(f"Dry run: Would schedule secret {secret_id} for deletion with {recovery_window} days recovery window")
            
            return True, f"Secret {secret_id} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of Secrets Manager Secret")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a Secrets Manager Secret.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Get recovery window
            recovery_window = self.config.get("resources.services.secretsmanager.recovery_window_in_days", 30)
            force_delete = self.config.get("resources.services.secretsmanager.force_delete_without_recovery", False)
            
            # Delete the secret
            if force_delete:
                response = secretsmanager.delete_secret(
                    SecretId=secret_id,
                    ForceDeleteWithoutRecovery=True
                )
                return True, f"Secret {secret_id} deleted without recovery window"
            else:
                response = secretsmanager.delete_secret(
                    SecretId=secret_id,
                    RecoveryWindowInDays=recovery_window
                )
                return True, f"Secret {secret_id} scheduled for deletion with {recovery_window} days recovery window"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting Secrets Manager Secret")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of a Secrets Manager Secret.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Get secret details
            response = secretsmanager.describe_secret(SecretId=secret_id)
            
            created_date = response.get('CreatedDate')
            
            if created_date:
                return created_date.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for Secrets Manager Secret {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a Secrets Manager Secret.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            secretsmanager = session.client('secretsmanager', region_name=resource.region)
            
            # Extract secret ID from ARN or resource ID
            secret_id = resource.resource_id
            if ':' in secret_id and '/' in secret_id:
                # This is an ARN, extract the secret name
                secret_id = secret_id.split(':')[-1]
                if '/' in secret_id:
                    secret_id = secret_id.split('/')[-1]
            
            # Get secret details
            response = secretsmanager.describe_secret(SecretId=secret_id)
            
            tags = response.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for Secrets Manager Secret {resource.resource_id}: {e}")
            return {}
