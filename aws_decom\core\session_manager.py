"""
AWS session management for AWS Resource Decommissioning Tool.
"""

import os
import boto3
import certifi
import logging
from typing import Dict, Optional
from botocore.exceptions import ProfileNotFound, ClientError
from ..core.config_manager import ConfigManager

class SessionManager:
    """
    Manages AWS sessions for different accounts.
    
    Creates and caches boto3 sessions for different AWS accounts.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the session manager.
        
        Args:
            config_manager: Configuration manager instance.
        """
        self.logger = logging.getLogger(__name__)
        self.config = config_manager
        self.sessions = {}  # Cache of boto3 sessions
        
        # Set up SSL verification
        if self.config.get("general.ssl_verification", True):
            os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
            self.logger.debug("SSL verification enabled with certifi")
        else:
            self.logger.warning("SSL verification disabled - not recommended for production")
    
    def get_session(self, account_id: str, region: Optional[str] = None) -> Optional[boto3.Session]:
        """
        Get a boto3 session for the specified account.
        
        Args:
            account_id: AWS account ID.
            region: AWS region (optional, uses default from config if not specified).
            
        Returns:
            boto3.Session or None if session creation fails.
        """
        # Use cached session if available
        cache_key = f"{account_id}:{region or 'default'}"
        if cache_key in self.sessions:
            self.logger.debug(f"Using cached session for account {account_id}")
            return self.sessions[cache_key]
        
        # Create new session
        profile_prefix = self.config.get("general.profile_prefix", "support-")
        profile_name = f"{profile_prefix}{account_id}"
        default_region = self.config.get("general.default_region", "us-east-1")
        
        try:
            session = boto3.Session(
                profile_name=profile_name,
                region_name=region or default_region
            )
            
            # Verify session works by making a simple API call
            sts = session.client('sts')
            sts.get_caller_identity()
            
            # Cache the session
            self.sessions[cache_key] = session
            self.logger.info(f"Created session for account {account_id} using profile {profile_name}")
            
            return session
        
        except ProfileNotFound:
            self.logger.error(f"AWS profile {profile_name} not found")
            return None
        
        except ClientError as e:
            self.logger.error(f"Error creating session for account {account_id}: {e}")
            return None
    
    def get_account_name(self, session: boto3.Session) -> str:
        """
        Get the account name (alias) for the specified session.
        
        Args:
            session: boto3 session.
            
        Returns:
            Account name or "Unknown" if not found.
        """
        try:
            iam = session.client('iam')
            response = iam.list_account_aliases()
            aliases = response.get('AccountAliases', [])
            
            if aliases:
                return aliases[0]
            
            # If no alias, use account ID
            sts = session.client('sts')
            account_id = sts.get_caller_identity()['Account']
            return f"Account {account_id}"
            
        except ClientError as e:
            self.logger.warning(f"Could not get account name: {e}")
            return "Unknown"
    
    def get_client(self, session: boto3.Session, service: str, region: Optional[str] = None) -> boto3.client:
        """
        Get a boto3 client for the specified service.
        
        Args:
            session: boto3 session.
            service: AWS service name.
            region: AWS region (optional, uses session region if not specified).
            
        Returns:
            boto3 client.
        """
        ssl_verify = self.config.get("general.ssl_verification", True)
        
        return session.client(
            service,
            region_name=region,
            verify=ssl_verify
        )
