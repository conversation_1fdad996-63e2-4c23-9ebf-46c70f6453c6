"""
Setup script for AWS Resource Decommissioning Tool.
"""

from setuptools import setup, find_packages

setup(
    name="aws-decom",
    version="1.0.0",
    description="Enterprise-grade AWS Resource Decommissioning Tool",
    author="<PERSON> Vernova",
    author_email="<EMAIL>",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        "boto3>=1.20.0",
        "colorama>=0.4.4",
        "tabulate>=0.8.9",
        "pyyaml>=6.0",
        "python-json-logger>=2.0.2",
        "certifi>=2021.10.8",
        "graphviz>=0.20.1",
        "tqdm>=4.64.0",
    ],
    entry_points={
        "console_scripts": [
            "aws-decom=aws_decom.cli:main",
        ],
    },
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.6",
)
