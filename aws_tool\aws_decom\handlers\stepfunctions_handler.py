"""
Step Functions State Machine handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class StepFunctionsStateMachineHandler(BaseResourceHandler):
    """
    Handler for Step Functions State Machines.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the Step Functions State Machine handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a Step Functions State Machine exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Check if state machine exists
            response = stepfunctions.describe_state_machine(stateMachineArn=state_machine_arn)
            
            # Check if state machine has running executions
            try:
                executions = stepfunctions.list_executions(
                    stateMachineArn=state_machine_arn,
                    statusFilter='RUNNING',
                    maxResults=1
                )
                
                if executions.get('executions'):
                    return False, f"State Machine {state_machine_arn} has running executions"
            except ClientError as e:
                self.logger.warning(f"Error checking running executions: {e}")
            
            return True, f"State Machine {state_machine_arn} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating Step Functions State Machine")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent Step Functions State Machine deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Check for running executions
            try:
                executions = stepfunctions.list_executions(
                    stateMachineArn=state_machine_arn,
                    statusFilter='RUNNING'
                )
                
                for execution in executions.get('executions', []):
                    dependencies.append(ResourceDependency(
                        resource_type="states:execution",
                        resource_id=execution['executionArn'].split(':')[-1],
                        relationship="execution_of",
                        blocking=True  # Blocking because executions must complete first
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking running executions: {e}")
            
            # Check for EventBridge rules that target this state machine
            try:
                events = session.client('events', region_name=resource.region)
                rules = events.list_rules()
                
                for rule in rules.get('Rules', []):
                    rule_name = rule.get('Name')
                    
                    targets = events.list_targets_by_rule(Rule=rule_name)
                    
                    for target in targets.get('Targets', []):
                        if target.get('Arn') == state_machine_arn:
                            dependencies.append(ResourceDependency(
                                resource_type="events:rule",
                                resource_id=rule_name,
                                relationship="targets",
                                blocking=True  # Blocking because rule depends on this state machine
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking EventBridge rule dependencies: {e}")
            
            # Check for CloudFormation stacks that created this state machine
            try:
                cfn = session.client('cloudformation', region_name=resource.region)
                stacks = cfn.list_stacks(StackStatusFilter=[
                    'CREATE_COMPLETE', 'UPDATE_COMPLETE', 'UPDATE_ROLLBACK_COMPLETE'
                ])
                
                for stack in stacks.get('StackSummaries', []):
                    stack_name = stack.get('StackName')
                    
                    resources = cfn.list_stack_resources(StackName=stack_name)
                    
                    for res in resources.get('StackResourceSummaries', []):
                        if res.get('ResourceType') == 'AWS::StepFunctions::StateMachine' and res.get('PhysicalResourceId') == state_machine_arn:
                            dependencies.append(ResourceDependency(
                                resource_type="cloudformation:stack",
                                resource_id=stack_name,
                                relationship="managed_by",
                                blocking=True  # Blocking because stack manages the resource
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudFormation stack dependencies: {e}")
            
            # Get state machine definition to check for Lambda functions and other resources
            try:
                response = stepfunctions.describe_state_machine(stateMachineArn=state_machine_arn)
                definition = response.get('definition', '{}')
                
                # Parse the definition to find Lambda functions and other resources
                # This is a simplified approach; a full parser would be more complex
                if 'Lambda' in definition and 'arn:aws:lambda' in definition:
                    import re
                    lambda_arns = re.findall(r'arn:aws:lambda:[^"\']*', definition)
                    
                    for lambda_arn in lambda_arns:
                        dependencies.append(ResourceDependency(
                            resource_type="lambda:function",
                            resource_id=lambda_arn.split(':')[-1],
                            relationship="invokes",
                            blocking=False  # Not blocking because state machine can be deleted without affecting Lambda
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking state machine definition: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for Step Functions State Machine {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of Step Functions State Machine deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Check if state machine exists
            response = stepfunctions.describe_state_machine(stateMachineArn=state_machine_arn)
            
            # Check for running executions
            executions = stepfunctions.list_executions(
                stateMachineArn=state_machine_arn,
                statusFilter='RUNNING',
                maxResults=1
            )
            
            if executions.get('executions'):
                return False, f"State Machine {state_machine_arn} has running executions"
            
            self.logger.info(f"Dry run: Would delete state machine {state_machine_arn}")
            
            return True, f"State Machine {state_machine_arn} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of Step Functions State Machine")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a Step Functions State Machine.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Check for running executions
            executions = stepfunctions.list_executions(
                stateMachineArn=state_machine_arn,
                statusFilter='RUNNING',
                maxResults=1
            )
            
            if executions.get('executions'):
                return False, f"State Machine {state_machine_arn} has running executions"
            
            # Delete the state machine
            stepfunctions.delete_state_machine(stateMachineArn=state_machine_arn)
            
            return True, f"State Machine {state_machine_arn} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting Step Functions State Machine")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of a Step Functions State Machine.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Get state machine details
            response = stepfunctions.describe_state_machine(stateMachineArn=state_machine_arn)
            
            creation_date = response.get('creationDate')
            
            if creation_date:
                return creation_date.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for Step Functions State Machine {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a Step Functions State Machine.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            stepfunctions = session.client('stepfunctions', region_name=resource.region)
            
            # Get state machine ARN
            state_machine_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not state_machine_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{resource.resource_id}"
            
            # Get tags for the state machine
            response = stepfunctions.list_tags_for_resource(resourceArn=state_machine_arn)
            
            tags = response.get('tags', [])
            
            return {tag['key']: tag['value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for Step Functions State Machine {resource.resource_id}: {e}")
            return {}
