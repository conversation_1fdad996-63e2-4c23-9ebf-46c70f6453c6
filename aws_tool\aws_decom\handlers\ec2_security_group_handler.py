"""
EC2 Security Group handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tu<PERSON>, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EC2SecurityGroupHandler(BaseResourceHandler):
    """
    Handler for EC2 Security Groups.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EC2 Security Group handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EC2 Security Group exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if security group exists
            response = ec2.describe_security_groups(GroupIds=[resource.resource_id])
            
            # Check if there are any security groups
            if not response.get('SecurityGroups'):
                return False, f"Security Group {resource.resource_id} not found"
            
            # Check if it's the default security group
            security_group = response['SecurityGroups'][0]
            group_name = security_group.get('GroupName', '')
            
            if group_name == 'default':
                return False, f"Security Group {resource.resource_id} is the default security group and cannot be deleted"
            
            # Check if security group is being used by any instances
            try:
                instance_response = ec2.describe_instances(Filters=[
                    {'Name': 'instance.group-id', 'Values': [resource.resource_id]}
                ])
                
                instances = []
                for reservation in instance_response.get('Reservations', []):
                    for instance in reservation.get('Instances', []):
                        if instance.get('State', {}).get('Name') not in ['terminated', 'shutting-down']:
                            instances.append(instance['InstanceId'])
                
                if instances:
                    return False, f"Security Group {resource.resource_id} is being used by instances: {', '.join(instances)}"
            except ClientError as e:
                self.logger.warning(f"Error checking instance usage: {e}")
            
            # Check if security group is being used by any network interfaces
            try:
                eni_response = ec2.describe_network_interfaces(Filters=[
                    {'Name': 'group-id', 'Values': [resource.resource_id]}
                ])
                
                enis = []
                for eni in eni_response.get('NetworkInterfaces', []):
                    enis.append(eni['NetworkInterfaceId'])
                
                if enis:
                    return False, f"Security Group {resource.resource_id} is being used by network interfaces: {', '.join(enis)}"
            except ClientError as e:
                self.logger.warning(f"Error checking network interface usage: {e}")
            
            # Check if security group is referenced by other security groups
            try:
                sg_response = ec2.describe_security_groups(Filters=[
                    {'Name': 'ip-permission.group-id', 'Values': [resource.resource_id]}
                ])
                
                referencing_groups = []
                for sg in sg_response.get('SecurityGroups', []):
                    if sg['GroupId'] != resource.resource_id:  # Skip self-references
                        referencing_groups.append(sg['GroupId'])
                
                if referencing_groups:
                    return False, f"Security Group {resource.resource_id} is referenced by other security groups: {', '.join(referencing_groups)}"
            except ClientError as e:
                self.logger.warning(f"Error checking security group references: {e}")
            
            return True, f"Security Group {resource.resource_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EC2 Security Group")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EC2 Security Group deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if security group is being used by any instances
            try:
                instance_response = ec2.describe_instances(Filters=[
                    {'Name': 'instance.group-id', 'Values': [resource.resource_id]}
                ])
                
                for reservation in instance_response.get('Reservations', []):
                    for instance in reservation.get('Instances', []):
                        if instance.get('State', {}).get('Name') not in ['terminated', 'shutting-down']:
                            dependencies.append(ResourceDependency(
                                resource_type="ec2:instance",
                                resource_id=instance['InstanceId'],
                                relationship="used_by",
                                blocking=True  # Blocking because instances must be terminated or SG detached first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking instance usage: {e}")
            
            # Check if security group is being used by any network interfaces
            try:
                eni_response = ec2.describe_network_interfaces(Filters=[
                    {'Name': 'group-id', 'Values': [resource.resource_id]}
                ])
                
                for eni in eni_response.get('NetworkInterfaces', []):
                    dependencies.append(ResourceDependency(
                        resource_type="ec2:network-interface",
                        resource_id=eni['NetworkInterfaceId'],
                        relationship="used_by",
                        blocking=True  # Blocking because ENIs must be deleted or SG detached first
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking network interface usage: {e}")
            
            # Check if security group is referenced by other security groups
            try:
                sg_response = ec2.describe_security_groups(Filters=[
                    {'Name': 'ip-permission.group-id', 'Values': [resource.resource_id]}
                ])
                
                for sg in sg_response.get('SecurityGroups', []):
                    if sg['GroupId'] != resource.resource_id:  # Skip self-references
                        dependencies.append(ResourceDependency(
                            resource_type="ec2:security-group",
                            resource_id=sg['GroupId'],
                            relationship="referenced_by",
                            blocking=True  # Blocking because references must be removed first
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking security group references: {e}")
            
            # Check if security group is used by any load balancers
            try:
                elb_client = session.client('elbv2', region_name=resource.region)
                lb_response = elb_client.describe_load_balancers()
                
                for lb in lb_response.get('LoadBalancers', []):
                    for sg_id in lb.get('SecurityGroups', []):
                        if sg_id == resource.resource_id:
                            dependencies.append(ResourceDependency(
                                resource_type="elasticloadbalancing:loadbalancer",
                                resource_id=lb['LoadBalancerArn'].split('/')[-1],
                                relationship="used_by",
                                blocking=True  # Blocking because LB must be deleted or SG detached first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking load balancer usage: {e}")
            
            # Check if security group is used by any RDS instances
            try:
                rds_client = session.client('rds', region_name=resource.region)
                db_response = rds_client.describe_db_instances()
                
                for db in db_response.get('DBInstances', []):
                    for vpc_sg in db.get('VpcSecurityGroups', []):
                        if vpc_sg.get('VpcSecurityGroupId') == resource.resource_id:
                            dependencies.append(ResourceDependency(
                                resource_type="rds:db",
                                resource_id=db['DBInstanceIdentifier'],
                                relationship="used_by",
                                blocking=True  # Blocking because DB must be deleted or SG detached first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking RDS usage: {e}")
            
            # Check if security group is used by any ElastiCache clusters
            try:
                elasticache_client = session.client('elasticache', region_name=resource.region)
                cache_response = elasticache_client.describe_cache_clusters()
                
                for cluster in cache_response.get('CacheClusters', []):
                    for sg in cluster.get('SecurityGroups', []):
                        if sg.get('SecurityGroupId') == resource.resource_id:
                            dependencies.append(ResourceDependency(
                                resource_type="elasticache:cluster",
                                resource_id=cluster['CacheClusterId'],
                                relationship="used_by",
                                blocking=True  # Blocking because cluster must be deleted or SG detached first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking ElastiCache usage: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EC2 Security Group {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EC2 Security Group deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Try to delete with DryRun=True
            ec2.delete_security_group(
                GroupId=resource.resource_id,
                DryRun=True
            )
            
            # If we get here, something went wrong (DryRun should raise an exception)
            return False, "Unexpected success in dry run mode"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of EC2 Security Group")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an EC2 Security Group.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Delete the security group
            ec2.delete_security_group(GroupId=resource.resource_id)
            
            return True, f"Security Group {resource.resource_id} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting EC2 Security Group")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an EC2 Security Group.
        
        Note: EC2 API doesn't provide creation date for security groups directly.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        # EC2 API doesn't provide creation date for security groups
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EC2 Security Group.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_security_groups(GroupIds=[resource.resource_id])
            
            if not response.get('SecurityGroups'):
                return {}
            
            security_group = response['SecurityGroups'][0]
            tags = security_group.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EC2 Security Group {resource.resource_id}: {e}")
            return {}
