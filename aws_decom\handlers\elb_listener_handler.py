"""
ELB Listener handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EL<PERSON>istenerHandler(BaseResourceHandler):
    """
    Handler for Elastic Load Balancer Listeners.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the ELB Listener handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a listener exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract listener ARN from resource ID
            listener_id = resource.resource_id
            
            # Check if listener exists
            response = elbv2.describe_listeners(ListenerArns=[listener_id])
            
            if not response.get('Listeners'):
                return False, f"Listener {listener_id} not found"
            
            return True, f"Listener {listener_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating listener")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a listener.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract listener ARN from resource ID
            listener_id = resource.resource_id
            
            # Check for listener rules
            try:
                rules_response = elbv2.describe_rules(ListenerArn=listener_id)
                
                for rule in rules_response.get('Rules', []):
                    # Skip the default rule (it's automatically managed with the listener)
                    if rule.get('IsDefault', False):
                        continue
                    
                    rule_arn = rule['RuleArn']
                    dependencies.append(ResourceDependency(
                        resource_type="elasticloadbalancing:listener-rule",
                        resource_id=rule_arn,
                        relationship="belongs_to",
                        blocking=False  # Not blocking because rules are deleted with the listener
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking rules for listener {listener_id}: {e}")
            
            # Check for target groups used by this listener
            try:
                listener_response = elbv2.describe_listeners(ListenerArns=[listener_id])
                
                if listener_response.get('Listeners'):
                    listener = listener_response['Listeners'][0]
                    
                    # Check default actions
                    for action in listener.get('DefaultActions', []):
                        if action.get('Type') == 'forward' and 'TargetGroupArn' in action:
                            dependencies.append(ResourceDependency(
                                resource_type="elasticloadbalancing:targetgroup",
                                resource_id=action['TargetGroupArn'],
                                relationship="forwards_to",
                                blocking=False  # Not blocking because the listener can be deleted regardless
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking target groups for listener {listener_id}: {e}")
            
            # Get the load balancer this listener belongs to
            try:
                listener_response = elbv2.describe_listeners(ListenerArns=[listener_id])
                
                if listener_response.get('Listeners'):
                    listener = listener_response['Listeners'][0]
                    lb_arn = listener.get('LoadBalancerArn')
                    
                    if lb_arn:
                        dependencies.append(ResourceDependency(
                            resource_type="elasticloadbalancing:loadbalancer",
                            resource_id=lb_arn,
                            relationship="belongs_to",
                            blocking=True  # Blocking because the load balancer must exist
                        ))
            except ClientError as e:
                self.logger.warning(f"Error getting load balancer for listener {listener_id}: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for listener {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of listener deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract listener ARN from resource ID
            listener_id = resource.resource_id
            
            # Check if listener exists
            response = elbv2.describe_listeners(ListenerArns=[listener_id])
            
            if not response.get('Listeners'):
                return False, f"Listener {listener_id} not found"
            
            # Check for rules
            rule_count = 0
            try:
                rules_response = elbv2.describe_rules(ListenerArn=listener_id)
                
                # Count non-default rules
                for rule in rules_response.get('Rules', []):
                    if not rule.get('IsDefault', False):
                        rule_count += 1
            except ClientError as e:
                self.logger.warning(f"Error checking rules for listener {listener_id}: {e}")
            
            if rule_count > 0:
                return True, f"Dry run successful: Would delete {rule_count} rules and listener {listener_id}"
            else:
                return True, f"Dry run successful: Would delete listener {listener_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of listener")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a listener.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract listener ARN from resource ID
            listener_id = resource.resource_id
            
            # Check if listener exists
            response = elbv2.describe_listeners(ListenerArns=[listener_id])
            
            if not response.get('Listeners'):
                return False, f"Listener {listener_id} not found"
            
            # Delete the listener (this will also delete all rules)
            elbv2.delete_listener(ListenerArn=listener_id)
            
            return True, f"Successfully deleted listener {listener_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of listener")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a listener.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        # Listeners don't have a creation date available through the API
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a listener.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract listener ARN from resource ID
            listener_id = resource.resource_id
            
            # Get tags for the listener
            response = elbv2.describe_tags(ResourceArns=[listener_id])
            
            tags = {}
            for tag_desc in response.get('TagDescriptions', []):
                if tag_desc.get('ResourceArn') == listener_id:
                    for tag in tag_desc.get('Tags', []):
                        tags[tag['Key']] = tag['Value']
            
            return tags
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for listener {resource.resource_id}: {e}")
            return {}
