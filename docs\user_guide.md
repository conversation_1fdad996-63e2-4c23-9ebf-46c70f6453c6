# AWS Resource Decommissioning Tool - User Guide

## Introduction

The AWS Resource Decommissioning Tool is an enterprise-grade solution for safely and efficiently decommissioning AWS resources across multiple accounts. This guide provides detailed instructions on how to use the tool effectively.

## Installation

### Prerequisites

- Python 3.6 or higher
- AWS CLI configured with appropriate profiles
- Graphviz (optional, for dependency visualization)

### Installation Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/aws-decom.git
   cd aws-decom
   ```

2. Install the package:
   ```bash
   pip install -e .
   ```

3. Install Graphviz (optional, for dependency visualization):
   - On Windows: Download and install from [Graphviz website](https://graphviz.org/download/)
   - On macOS: `brew install graphviz`
   - On Ubuntu/Debian: `apt-get install graphviz`

## Configuration

### AWS Profiles

The tool uses AWS profiles for authentication. Profiles should follow the naming convention:

```
support-{account_id}
```

For example, for account `************`, the profile should be named `support-************`.

To configure AWS profiles, use the AWS CLI:

```bash
aws configure --profile support-************
```

### Configuration Files

Configuration is stored in YAML files in the `config` directory:

- `config.yaml`: Main configuration file
- `logging_config.yaml`: Logging configuration

You can override configuration settings using environment variables prefixed with `AWS_DECOM_`.

#### Example Configuration Override

```bash
export AWS_DECOM_GENERAL_DEFAULT_REGION=us-west-2
```

## Usage

### Basic Usage

```bash
# Dry run mode (no actual deletions)
aws-decom --input resources.csv --cr-no GECHG1234567 --dry-run

# Deletion mode (will delete resources)
aws-decom --input resources.csv --cr-no GECHG1234567 --delete

# Force deletion mode (will delete resources and their dependencies)
aws-decom --input resources.csv --cr-no GECHG1234567 --delete --force
```

### Command-Line Options

| Option | Description |
|--------|-------------|
| `--input FILE` | Path to input file containing ARNs (CSV, JSON, or TXT) |
| `--cr-no NUMBER` | Change request number (e.g., GECHG1234567) |
| `--dry-run` | Perform a dry run without actually deleting resources |
| `--delete` | Delete resources (USE WITH CAUTION) |
| `--force` | Force deletion of resources with dependencies (USE WITH EXTREME CAUTION) |
| `--output-format FORMAT` | Output report format (csv, json, or both) |
| `--dependency-graph` | Generate dependency graph visualization |
| `--verbose` | Enable verbose output |
| `--version` | Show version information and exit |

### Input File Format

The input file should contain one ARN per line. The following formats are supported:

- CSV: First column should contain ARNs
- JSON: List of ARNs or objects with 'arn' field
- TXT: One ARN per line

#### Example CSV:
```
arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0
arn:aws:s3:::my-bucket
arn:aws:lambda:us-east-1:************:function:my-function
```

#### Example JSON:
```json
{
  "arns": [
    "arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0",
    "arn:aws:s3:::my-bucket",
    "arn:aws:lambda:us-east-1:************:function:my-function"
  ]
}
```

### Workflow

1. **Preparation**:
   - Create a CSV file with the ARNs of resources to be decommissioned
   - Ensure AWS profiles are configured correctly

2. **Validation (Dry Run)**:
   - Run the tool in dry-run mode to validate resources and check for dependencies
   - Review the generated reports and dependency graphs

3. **Decommissioning**:
   - If validation is successful, run the tool in deletion mode
   - Review the confirmation prompts carefully before proceeding
   - For resources with dependencies, consider using the `--force` flag (with caution)

4. **Verification**:
   - Review the generated reports to confirm successful deletions
   - Check the AWS console to verify resources have been deleted

## Reports and Outputs

### CSV and JSON Reports

Reports are generated in the `reports` directory in CSV and JSON formats. They include:

- Resource details (ARN, type, ID, etc.)
- Operation results (success/failure)
- Error messages
- Timing information
- Dependency information

### Dependency Graphs

If the `--dependency-graph` option is used, the tool generates two types of dependency graphs:

1. **Resource Dependency Graph**: Shows dependencies between individual resources
2. **Service Dependency Graph**: Shows dependencies between AWS services

Graphs are saved as PNG files in the `reports` directory.

## Supported Resource Types

The tool supports the following AWS resource types:

- EC2: Instances, Volumes, Snapshots, AMIs, Security Groups
- S3: Buckets
- Lambda: Functions
- CloudWatch: Alarms
- Secrets Manager: Secrets
- SNS: Topics, Subscriptions
- Step Functions: State Machines
- EventBridge: Rules
- CloudFormation: Stacks
- ELB: Load Balancers, Target Groups, Listeners, Rules
- SSM: Parameters
- AWS Backup: Backup Plans, Backup Vaults

## Troubleshooting

### Common Issues

1. **Profile Not Found**:
   - Ensure AWS profiles are configured correctly
   - Check that profile names follow the `support-{account_id}` convention

2. **Permission Denied**:
   - Ensure the AWS profiles have appropriate permissions
   - Check IAM policies for the necessary delete permissions

3. **Resource Not Found**:
   - Verify that the ARN is correct
   - Check if the resource has already been deleted

4. **Dependency Violations**:
   - Use the dependency graph to identify dependencies
   - Consider using the `--force` flag (with caution)

### Logs

Logs are stored in the `logs` directory:

- `aws_decom.log`: Main log file
- `error.log`: Error log file

Use the `--verbose` flag to increase logging detail.

## Best Practices

1. **Always run in dry-run mode first**:
   - Validate resources and check for dependencies
   - Review the generated reports and dependency graphs

2. **Use meaningful change request numbers**:
   - Follow your organization's change management process
   - Use the CR number to track decommissioning activities

3. **Handle dependencies carefully**:
   - Understand the impact of deleting resources with dependencies
   - Use the dependency graph to visualize relationships

4. **Backup important data**:
   - Ensure important data is backed up before deletion
   - Consider creating snapshots of volumes and databases

5. **Monitor the decommissioning process**:
   - Review logs and reports
   - Verify deletions in the AWS console

## Support and Feedback

For support or to provide feedback, please contact your organization's AWS administrator or open an issue on the GitHub repository.
