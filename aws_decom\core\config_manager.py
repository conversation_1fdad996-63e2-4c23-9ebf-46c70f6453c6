"""
Configuration manager for AWS Resource Decommissioning Tool.
"""

import os
import yaml
from typing import Any, Dict, Optional
import logging

class ConfigManager:
    """
    Manages configuration for the AWS Resource Decommissioning Tool.
    
    Loads configuration from YAML files and environment variables.
    """
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file.
        """
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        self.config = self._load_config()
        self._apply_environment_overrides()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file.
        
        Returns:
            Dict containing configuration.
        """
        try:
            with open(self.config_path, 'r') as config_file:
                config = yaml.safe_load(config_file)
                self.logger.debug(f"Loaded configuration from {self.config_path}")
                return config
        except FileNotFoundError:
            self.logger.warning(f"Configuration file not found at {self.config_path}. Using defaults.")
            return self._get_default_config()
        except yaml.YAMLError as e:
            self.logger.error(f"Error parsing configuration file: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration.
        
        Returns:
            Dict containing default configuration.
        """
        return {
            "general": {
                "profile_prefix": "support-",
                "default_region": "us-east-1",
                "ssl_verification": True,
                "max_retries": 3,
                "retry_delay": 5,
                "timeout": 60
            },
            "paths": {
                "logs_dir": "logs",
                "reports_dir": "reports"
            },
            "validation": {
                "cr_number_pattern": "^GECHG\\d{7}$"
            }
        }
    
    def _apply_environment_overrides(self) -> None:
        """
        Apply environment variable overrides to configuration.
        
        Environment variables should be prefixed with AWS_DECOM_.
        For example, AWS_DECOM_GENERAL_DEFAULT_REGION would override general.default_region.
        """
        prefix = "AWS_DECOM_"
        for env_var, value in os.environ.items():
            if env_var.startswith(prefix):
                # Convert environment variable name to config path
                # e.g., AWS_DECOM_GENERAL_DEFAULT_REGION -> general.default_region
                config_path = env_var[len(prefix):].lower().replace("_", ".")
                self._set_config_value(config_path, value)
                self.logger.debug(f"Overrode {config_path} with environment variable {env_var}")
    
    def _set_config_value(self, path: str, value: str) -> None:
        """
        Set a configuration value by path.
        
        Args:
            path: Dot-separated path to the configuration value.
            value: Value to set.
        """
        parts = path.split(".")
        config = self.config
        
        # Navigate to the nested dictionary
        for part in parts[:-1]:
            if part not in config:
                config[part] = {}
            config = config[part]
        
        # Set the value, converting to appropriate type
        key = parts[-1]
        if isinstance(config.get(key), bool):
            config[key] = value.lower() in ("true", "yes", "1")
        elif isinstance(config.get(key), int):
            try:
                config[key] = int(value)
            except ValueError:
                self.logger.warning(f"Could not convert {value} to int for {path}")
        elif isinstance(config.get(key), float):
            try:
                config[key] = float(value)
            except ValueError:
                self.logger.warning(f"Could not convert {value} to float for {path}")
        else:
            config[key] = value
    
    def get(self, path: str, default: Any = None) -> Any:
        """
        Get a configuration value by path.
        
        Args:
            path: Dot-separated path to the configuration value.
            default: Default value to return if path is not found.
            
        Returns:
            Configuration value or default.
        """
        parts = path.split(".")
        config = self.config
        
        # Navigate to the nested dictionary
        for part in parts[:-1]:
            if part not in config:
                return default
            config = config[part]
        
        # Get the value
        return config.get(parts[-1], default)
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get the entire configuration.
        
        Returns:
            Dict containing the entire configuration.
        """
        return self.config
