"""
SNS Topic handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class SNSTopicHandler(BaseResourceHandler):
    """
    Handler for SNS Topics.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the SNS Topic handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an SNS Topic exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Check if topic exists
            topic_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not topic_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                topic_arn = f"arn:aws:sns:{region}:{account_id}:{resource.resource_id}"
            
            # Get topic attributes to verify it exists
            sns.get_topic_attributes(TopicArn=topic_arn)
            
            # Check for subscriptions
            subscriptions = sns.list_subscriptions_by_topic(TopicArn=topic_arn)
            
            if subscriptions.get('Subscriptions'):
                self.logger.info(f"Topic {topic_arn} has {len(subscriptions['Subscriptions'])} subscriptions")
            
            return True, f"Topic {topic_arn} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating SNS Topic")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent SNS Topic deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get topic ARN
            topic_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not topic_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                topic_arn = f"arn:aws:sns:{region}:{account_id}:{resource.resource_id}"
            
            # Check for subscriptions
            subscriptions = sns.list_subscriptions_by_topic(TopicArn=topic_arn)
            
            for subscription in subscriptions.get('Subscriptions', []):
                subscription_arn = subscription.get('SubscriptionArn')
                
                # Skip pending subscriptions
                if subscription_arn == 'PendingConfirmation':
                    continue
                
                endpoint = subscription.get('Endpoint', '')
                protocol = subscription.get('Protocol', '')
                
                dependencies.append(ResourceDependency(
                    resource_type="sns:subscription",
                    resource_id=subscription_arn,
                    relationship="subscribes_to",
                    blocking=False  # Not blocking because subscriptions are deleted with the topic
                ))
            
            # Check for CloudWatch alarms that publish to this topic
            try:
                cloudwatch = session.client('cloudwatch', region_name=resource.region)
                alarms = cloudwatch.describe_alarms()
                
                for alarm in alarms.get('MetricAlarms', []) + alarms.get('CompositeAlarms', []):
                    actions = []
                    actions.extend(alarm.get('AlarmActions', []))
                    actions.extend(alarm.get('OKActions', []))
                    actions.extend(alarm.get('InsufficientDataActions', []))
                    
                    if topic_arn in actions:
                        dependencies.append(ResourceDependency(
                            resource_type="cloudwatch:alarm",
                            resource_id=alarm['AlarmName'],
                            relationship="publishes_to",
                            blocking=True  # Blocking because alarm depends on this topic
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudWatch alarm dependencies: {e}")
            
            # Check for EventBridge rules that target this topic
            try:
                events = session.client('events', region_name=resource.region)
                rules = events.list_rules()
                
                for rule in rules.get('Rules', []):
                    rule_name = rule.get('Name')
                    
                    targets = events.list_targets_by_rule(Rule=rule_name)
                    
                    for target in targets.get('Targets', []):
                        if target.get('Arn') == topic_arn:
                            dependencies.append(ResourceDependency(
                                resource_type="events:rule",
                                resource_id=rule_name,
                                relationship="targets",
                                blocking=True  # Blocking because rule depends on this topic
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking EventBridge rule dependencies: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for SNS Topic {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of SNS Topic deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get topic ARN
            topic_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not topic_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                topic_arn = f"arn:aws:sns:{region}:{account_id}:{resource.resource_id}"
            
            # Check if topic exists
            sns.get_topic_attributes(TopicArn=topic_arn)
            
            # Check for subscriptions
            subscriptions = sns.list_subscriptions_by_topic(TopicArn=topic_arn)
            
            if subscriptions.get('Subscriptions'):
                self.logger.info(f"Dry run: Would delete topic {topic_arn} with {len(subscriptions['Subscriptions'])} subscriptions")
            else:
                self.logger.info(f"Dry run: Would delete topic {topic_arn}")
            
            return True, f"Topic {topic_arn} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of SNS Topic")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an SNS Topic.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get topic ARN
            topic_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not topic_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                topic_arn = f"arn:aws:sns:{region}:{account_id}:{resource.resource_id}"
            
            # Delete the topic
            sns.delete_topic(TopicArn=topic_arn)
            
            return True, f"Topic {topic_arn} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting SNS Topic")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an SNS Topic.
        
        Note: SNS API doesn't provide creation date for topics directly.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        # SNS API doesn't provide creation date for topics
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an SNS Topic.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get topic ARN
            topic_arn = resource.full_arn
            
            # If resource_id is not a full ARN, construct it
            if not topic_arn.startswith('arn:'):
                account_id = resource.account_id
                region = resource.region
                topic_arn = f"arn:aws:sns:{region}:{account_id}:{resource.resource_id}"
            
            # Get tags for the topic
            response = sns.list_tags_for_resource(ResourceArn=topic_arn)
            
            tags = response.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for SNS Topic {resource.resource_id}: {e}")
            return {}
