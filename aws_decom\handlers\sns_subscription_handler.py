"""
SNS Subscription handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class SNSSubscriptionHandler(BaseResourceHandler):
    """
    Handler for SNS Subscriptions.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the SNS Subscription handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an SNS Subscription exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get subscription ARN
            subscription_arn = resource.full_arn
            
            # If resource_id is not a full ARN, use it as is
            if not subscription_arn.startswith('arn:'):
                subscription_arn = resource.resource_id
            
            # Check if subscription exists
            response = sns.get_subscription_attributes(SubscriptionArn=subscription_arn)
            
            # Check if subscription is pending confirmation
            if subscription_arn == 'PendingConfirmation':
                return False, "Subscription is pending confirmation and cannot be deleted"
            
            # Get subscription attributes
            attributes = response.get('Attributes', {})
            topic_arn = attributes.get('TopicArn')
            protocol = attributes.get('Protocol')
            endpoint = attributes.get('Endpoint')
            
            self.logger.info(f"Subscription {subscription_arn} is for topic {topic_arn} with protocol {protocol} and endpoint {endpoint}")
            
            return True, f"Subscription {subscription_arn} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating SNS Subscription")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent SNS Subscription deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get subscription ARN
            subscription_arn = resource.full_arn
            
            # If resource_id is not a full ARN, use it as is
            if not subscription_arn.startswith('arn:'):
                subscription_arn = resource.resource_id
            
            # Check if subscription exists
            response = sns.get_subscription_attributes(SubscriptionArn=subscription_arn)
            
            # Get subscription attributes
            attributes = response.get('Attributes', {})
            topic_arn = attributes.get('TopicArn')
            protocol = attributes.get('Protocol')
            endpoint = attributes.get('Endpoint')
            
            # Add topic as a dependency
            if topic_arn:
                dependencies.append(ResourceDependency(
                    resource_type="sns:topic",
                    resource_id=topic_arn.split(':')[-1],
                    relationship="subscribes_to",
                    blocking=False  # Not blocking because subscription can be deleted without affecting topic
                ))
            
            # Add endpoint as a dependency if it's an AWS resource
            if endpoint and protocol:
                if protocol == 'lambda' and endpoint.startswith('arn:aws:lambda:'):
                    dependencies.append(ResourceDependency(
                        resource_type="lambda:function",
                        resource_id=endpoint.split(':')[-1],
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting Lambda
                    ))
                elif protocol == 'sqs' and endpoint.startswith('arn:aws:sqs:'):
                    dependencies.append(ResourceDependency(
                        resource_type="sqs:queue",
                        resource_id=endpoint.split(':')[-1],
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting SQS
                    ))
                elif protocol == 'http' or protocol == 'https':
                    dependencies.append(ResourceDependency(
                        resource_type="http:endpoint",
                        resource_id=endpoint,
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting HTTP endpoint
                    ))
                elif protocol == 'email' or protocol == 'email-json':
                    dependencies.append(ResourceDependency(
                        resource_type="email:address",
                        resource_id=endpoint,
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting email
                    ))
                elif protocol == 'sms':
                    dependencies.append(ResourceDependency(
                        resource_type="sms:number",
                        resource_id=endpoint,
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting SMS
                    ))
                elif protocol == 'application':
                    dependencies.append(ResourceDependency(
                        resource_type="sns:application",
                        resource_id=endpoint.split(':')[-1],
                        relationship="delivers_to",
                        blocking=False  # Not blocking because subscription can be deleted without affecting application
                    ))
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for SNS Subscription {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of SNS Subscription deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get subscription ARN
            subscription_arn = resource.full_arn
            
            # If resource_id is not a full ARN, use it as is
            if not subscription_arn.startswith('arn:'):
                subscription_arn = resource.resource_id
            
            # Check if subscription exists
            response = sns.get_subscription_attributes(SubscriptionArn=subscription_arn)
            
            # Get subscription attributes
            attributes = response.get('Attributes', {})
            topic_arn = attributes.get('TopicArn')
            protocol = attributes.get('Protocol')
            endpoint = attributes.get('Endpoint')
            
            self.logger.info(f"Dry run: Would delete subscription {subscription_arn} for topic {topic_arn} with protocol {protocol} and endpoint {endpoint}")
            
            return True, f"Subscription {subscription_arn} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of SNS Subscription")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an SNS Subscription.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get subscription ARN
            subscription_arn = resource.full_arn
            
            # If resource_id is not a full ARN, use it as is
            if not subscription_arn.startswith('arn:'):
                subscription_arn = resource.resource_id
            
            # Delete the subscription
            sns.unsubscribe(SubscriptionArn=subscription_arn)
            
            return True, f"Subscription {subscription_arn} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting SNS Subscription")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an SNS Subscription.
        
        Note: SNS API doesn't provide creation date for subscriptions directly.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        # SNS API doesn't provide creation date for subscriptions
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an SNS Subscription.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            sns = session.client('sns', region_name=resource.region)
            
            # Get subscription ARN
            subscription_arn = resource.full_arn
            
            # If resource_id is not a full ARN, use it as is
            if not subscription_arn.startswith('arn:'):
                subscription_arn = resource.resource_id
            
            # Get tags for the subscription
            response = sns.list_tags_for_resource(ResourceArn=subscription_arn)
            
            tags = response.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for SNS Subscription {resource.resource_id}: {e}")
            return {}
