"""
ELB (Elastic Load Balancing) handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class ELBLoadBalancerHandler(BaseResourceHandler):
    """
    Handler for Elastic Load Balancers (Application, Network, and Gateway Load Balancers).
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the ELB handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a load balancer exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Check if load balancer exists
            response = elbv2.describe_load_balancers(
                LoadBalancerArns=[lb_id] if lb_id.startswith('arn:') else [],
                Names=[lb_id] if not lb_id.startswith('arn:') else []
            )
            
            if not response.get('LoadBalancers'):
                return False, f"Load balancer {lb_id} not found"
            
            # Check if load balancer is in a deletable state
            lb = response['LoadBalancers'][0]
            lb_state = lb.get('State', {}).get('Code', '')
            
            if lb_state == 'active':
                return True, f"Load balancer {lb_id} exists and can be deleted"
            else:
                return False, f"Load balancer {lb_id} is in state {lb_state} and cannot be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating load balancer")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a load balancer.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Get load balancer ARN if we only have the name
            if not lb_id.startswith('arn:'):
                response = elbv2.describe_load_balancers(Names=[lb_id])
                if response.get('LoadBalancers'):
                    lb_id = response['LoadBalancers'][0]['LoadBalancerArn']
            
            # Check for target groups
            try:
                tg_response = elbv2.describe_target_groups(LoadBalancerArn=lb_id)
                
                for target_group in tg_response.get('TargetGroups', []):
                    dependencies.append(ResourceDependency(
                        resource_type="elasticloadbalancing:targetgroup",
                        resource_id=target_group['TargetGroupArn'],
                        relationship="attached_to",
                        blocking=False  # Not blocking because target groups are automatically detached
                    ))
                    
                    # Check for targets in each target group
                    try:
                        targets_response = elbv2.describe_target_health(TargetGroupArn=target_group['TargetGroupArn'])
                        
                        for target in targets_response.get('TargetHealthDescriptions', []):
                            target_id = target['Target']['Id']
                            target_type = "ec2:instance" if target_group.get('TargetType') == 'instance' else "generic:resource"
                            
                            dependencies.append(ResourceDependency(
                                resource_type=target_type,
                                resource_id=target_id,
                                relationship="target_of",
                                blocking=False  # Not blocking because targets can be deregistered
                            ))
                    except ClientError as e:
                        self.logger.warning(f"Error checking targets for target group {target_group['TargetGroupArn']}: {e}")
            except ClientError as e:
                self.logger.warning(f"Error checking target groups for load balancer {lb_id}: {e}")
            
            # Check for listeners
            try:
                listeners_response = elbv2.describe_listeners(LoadBalancerArn=lb_id)
                
                for listener in listeners_response.get('Listeners', []):
                    dependencies.append(ResourceDependency(
                        resource_type="elasticloadbalancing:listener",
                        resource_id=listener['ListenerArn'],
                        relationship="attached_to",
                        blocking=False  # Not blocking because listeners are automatically deleted
                    ))
            except ClientError as e:
                self.logger.warning(f"Error checking listeners for load balancer {lb_id}: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for load balancer {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of load balancer deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Get load balancer ARN if we only have the name
            if not lb_id.startswith('arn:'):
                response = elbv2.describe_load_balancers(Names=[lb_id])
                if response.get('LoadBalancers'):
                    lb_id = response['LoadBalancers'][0]['LoadBalancerArn']
                else:
                    return False, f"Load balancer {lb_id} not found"
            
            # Check if load balancer exists
            response = elbv2.describe_load_balancers(LoadBalancerArns=[lb_id])
            
            if not response.get('LoadBalancers'):
                return False, f"Load balancer {lb_id} not found"
            
            # Check for dependencies
            dependencies = self.check_dependencies(resource, session)
            blocking_dependencies = [dep for dep in dependencies if dep.blocking]
            
            if blocking_dependencies:
                dep_str = ", ".join([f"{dep.relationship} {dep.resource_type} {dep.resource_id}" for dep in blocking_dependencies])
                return False, f"Load balancer {lb_id} has blocking dependencies: {dep_str}"
            
            return True, f"Dry run successful: Would delete load balancer {lb_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of load balancer")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a load balancer.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Get load balancer ARN if we only have the name
            if not lb_id.startswith('arn:'):
                response = elbv2.describe_load_balancers(Names=[lb_id])
                if response.get('LoadBalancers'):
                    lb_id = response['LoadBalancers'][0]['LoadBalancerArn']
                else:
                    return False, f"Load balancer {lb_id} not found"
            
            # Delete the load balancer
            elbv2.delete_load_balancer(LoadBalancerArn=lb_id)
            
            return True, f"Successfully initiated deletion of load balancer {lb_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of load balancer")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a load balancer.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Get load balancer details
            response = elbv2.describe_load_balancers(
                LoadBalancerArns=[lb_id] if lb_id.startswith('arn:') else [],
                Names=[lb_id] if not lb_id.startswith('arn:') else []
            )
            
            if not response.get('LoadBalancers'):
                return None
            
            lb = response['LoadBalancers'][0]
            created_time = lb.get('CreatedTime')
            
            if created_time:
                return created_time.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation date for load balancer {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a load balancer.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract load balancer name or ARN from resource ID
            lb_id = resource.resource_id
            
            # Get load balancer ARN if we only have the name
            if not lb_id.startswith('arn:'):
                response = elbv2.describe_load_balancers(Names=[lb_id])
                if response.get('LoadBalancers'):
                    lb_id = response['LoadBalancers'][0]['LoadBalancerArn']
                else:
                    return {}
            
            # Get tags for the load balancer
            response = elbv2.describe_tags(ResourceArns=[lb_id])
            
            tags = {}
            for tag_desc in response.get('TagDescriptions', []):
                if tag_desc.get('ResourceArn') == lb_id:
                    for tag in tag_desc.get('Tags', []):
                        tags[tag['Key']] = tag['Value']
            
            return tags
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for load balancer {resource.resource_id}: {e}")
            return {}
