#!/usr/bin/env python3

import os
import sys
import csv
import boto3
import certifi
import logging
import argparse
import threading
import time
from tqdm import tqdm
from tabulate import tabulate
from colorama import Fore, Style, init
from datetime import datetime
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound

# Initialize colorama
init(autoreset=True)

# Setup logging
timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
log_file = f'aws_cleanup_{timestamp}.log'
logging.basicConfig(filename=log_file, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Force SSL certificates
os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()

# Thread Lock
lock = threading.Lock()

# --- Helper Functions ---

def get_boto3_session(account_id):
    profile = f"support-{account_id}"
    try:
        session = boto3.Session(profile_name=profile, region_name="us-east-1")
        return session
    except (ProfileNotFound, NoCredentialsError) as e:
        logging.error(f"Failed to create boto3 session for {account_id}: {e}")
        sys.exit(1)

def parse_arns(input_csv):
    resources = []
    with open(input_csv, 'r') as f:
        reader = csv.reader(f)
        for row in reader:
            arn = row[0].strip()
            parts = arn.split(':')
            if len(parts) >= 6:
                service = parts[2]
                account_id = parts[4]
                resource_info = parts[5]
                resources.append({
                    'arn': arn,
                    'service': service,
                    'account_id': account_id,
                    'resource_info': resource_info
                })
    return resources

def summarize_resources(resources):
    summary = {}

    for r in resources:
        service = r.get('service', 'unknown')
        resource_type = r.get('resource_type', 'unknown')

        key = (service, resource_type)
        if key not in summary:
            summary[key] = 0
        summary[key] += 1

    table = []
    for (service, resource_type), count in summary.items():
        table.append([service, resource_type, count])

    print(Fore.CYAN + tabulate(table, headers=["Service", "Resource Type", "Count"], tablefmt="fancy_grid"))

def confirm_deletion():
    print(Fore.RED + Style.BRIGHT + "\nWARNING: You are about to DELETE AWS resources permanently!")
    confirm = input(Fore.YELLOW + "Type 'yes' to confirm deletion: ").lower()
    if confirm != 'yes':
        print(Fore.GREEN + "Aborting. No resources were deleted.")
        sys.exit(0)

def delete_resource(session, service, resource_info):
    try:
        if service == "ec2":
            ec2 = session.client('ec2')
            if resource_info.startswith("instance/"):
                instance_id = resource_info.split('/')[-1]
                ec2.terminate_instances(InstanceIds=[instance_id])
            elif resource_info.startswith("volume/"):
                volume_id = resource_info.split('/')[-1]
                ec2.delete_volume(VolumeId=volume_id)
            elif resource_info.startswith("snapshot/"):
                snapshot_id = resource_info.split('/')[-1]
                ec2.delete_snapshot(SnapshotId=snapshot_id)
            elif resource_info.startswith("image/"):
                image_id = resource_info.split('/')[-1]
                ec2.deregister_image(ImageId=image_id)
            elif resource_info.startswith("security-group/"):
                sg_id = resource_info.split('/')[-1]
                ec2.delete_security_group(GroupId=sg_id)
            else:
                raise Exception("Unknown EC2 resource type.")

        elif service == "s3":
            s3 = session.resource('s3')
            bucket_name = resource_info
            bucket = s3.Bucket(bucket_name)
            bucket.objects.all().delete()
            bucket.delete()

        elif service == "lambda":
            lam = session.client('lambda')
            function_name = resource_info.split(':')[-1]
            lam.delete_function(FunctionName=function_name)

        elif service == "cloudwatch":
            cw = session.client('cloudwatch')
            cw.delete_alarms(AlarmNames=[resource_info])

        elif service == "secretsmanager":
            sm = session.client('secretsmanager')
            secret_name = resource_info
            sm.delete_secret(SecretId=secret_name, ForceDeleteWithoutRecovery=True)

        elif service == "sns":
            sns = session.client('sns')
            if resource_info.startswith("subscription/"):
                subscription_arn = resource_info.split('/')[-1]
                sns.unsubscribe(SubscriptionArn=subscription_arn)
            else:
                topic_arn = resource_info
                sns.delete_topic(TopicArn=topic_arn)

        elif service == "states":
            sfn = session.client('stepfunctions')
            state_machine_arn = f"arn:aws:states:{session.region_name}:{session.client('sts').get_caller_identity()['Account']}:stateMachine:{resource_info.split(':')[-1]}"
            sfn.delete_state_machine(stateMachineArn=state_machine_arn)

        elif service == "cloudformation":
            cfn = session.client('cloudformation')
            stack_name = resource_info.split('/')[-1]
            cfn.delete_stack(StackName=stack_name)

        elif service == "ssm":
            ssm = session.client('ssm')
            param_name = resource_info
            ssm.delete_parameter(Name=param_name)

        elif service == "elasticloadbalancing":
            elb = session.client('elbv2')
            if "listener-rule/" in resource_info:
                rule_arn = resource_info.split("listener-rule/")[-1]
                elb.delete_rule(RuleArn=rule_arn)
            elif "listener/" in resource_info:
                listener_arn = resource_info.split("listener/")[-1]
                elb.delete_listener(ListenerArn=listener_arn)
            elif "targetgroup/" in resource_info:
                tg_arn = resource_info.split("targetgroup/")[-1]
                elb.delete_target_group(TargetGroupArn=tg_arn)
            else:
                raise Exception("Unknown ELB resource type.")

        else:
            raise NotImplementedError(f"Deletion for {service} not implemented yet.")

        return "Deleted", "N/A"

    except Exception as e:
        return "Failed", str(e)

def dry_run_resource(session, service, resource_info):
    try:
        if service == "ec2":
            ec2 = session.client('ec2')
            if resource_info.startswith("instance/"):
                instance_id = resource_info.split('/')[-1]
                ec2.describe_instances(InstanceIds=[instance_id])
            elif resource_info.startswith("volume/"):
                volume_id = resource_info.split('/')[-1]
                ec2.describe_volumes(VolumeIds=[volume_id])
            elif resource_info.startswith("snapshot/"):
                snapshot_id = resource_info.split('/')[-1]
                ec2.describe_snapshots(SnapshotIds=[snapshot_id])
            elif resource_info.startswith("image/"):
                image_id = resource_info.split('/')[-1]
                ec2.describe_images(ImageIds=[image_id])
            elif resource_info.startswith("security-group/"):
                sg_id = resource_info.split('/')[-1]
                ec2.describe_security_groups(GroupIds=[sg_id])

        elif service == "s3":
            s3 = session.client('s3')
            s3.head_bucket(Bucket=resource_info)

        elif service == "lambda":
            lam = session.client('lambda')
            lam.get_function(FunctionName=resource_info.split(':')[-1])

        elif service == "cloudwatch":
            cw = session.client('cloudwatch')
            cw.describe_alarms(AlarmNames=[resource_info])

        elif service == "secretsmanager":
            sm = session.client('secretsmanager')
            sm.describe_secret(SecretId=resource_info)

        elif service == "sns":
            sns = session.client('sns')
            sns.get_topic_attributes(TopicArn=resource_info)

        elif service == "states":
            sfn = session.client('stepfunctions')
            sfn.describe_state_machine(stateMachineArn=resource_info)

        elif service == "cloudformation":
            cfn = session.client('cloudformation')
            cfn.describe_stacks(StackName=resource_info.split('/')[-1])

        elif service == "ssm":
            ssm = session.client('ssm')
            ssm.get_parameter(Name=resource_info)

        elif service == "elasticloadbalancing":
            elb = session.client('elbv2')
            if "listener-rule/" in resource_info:
                elb.describe_rules(RuleArns=[resource_info.split("listener-rule/")[-1]])
            elif "listener/" in resource_info:
                elb.describe_listeners(ListenerArns=[resource_info.split("listener/")[-1]])
            elif "targetgroup/" in resource_info:
                elb.describe_target_groups(TargetGroupArns=[resource_info.split("targetgroup/")[-1]])

        else:
            raise NotImplementedError(f"Dry-run for {service} not implemented yet.")

        return "Exists", "N/A"

    except ClientError as e:
        if e.response['Error']['Code'] in ["NoSuchEntity", "404", "ResourceNotFoundException", "ValidationError"]:
            return "NotFound", str(e)
        else:
            return "Error", str(e)
    except Exception as e:
        return "Error", str(e)

def process_resource(resource, dry_run=True):
    session = get_boto3_session(resource['account_id'])
    start = time.time()

    if dry_run:
        status, error = dry_run_resource(session, resource['service'], resource['resource_info'])
    else:
        status, error = delete_resource(session, resource['service'], resource['resource_info'])

    end = time.time()
    duration = round(end - start, 2)

    return {
        'Account_ID': resource['account_id'],
        'Service': resource['service'],
        'Resource_Info': resource['resource_info'],
        'Status': status,
        'Error': error,
        'Duration_sec': duration
    }

def worker(resource, dry_run, results, pbar):
    outcome = process_resource(resource, dry_run=dry_run)
    with lock:
        results.append(outcome)
        pbar.update(1)

def write_report(report_file, results):
    keys = results[0].keys()
    with open(report_file, 'w', newline='') as f:
        dict_writer = csv.DictWriter(f, keys)
        dict_writer.writeheader()
        dict_writer.writerows(results)

# --- Main ---

def main():
    parser = argparse.ArgumentParser(description="AWS Cleanup Script (Ultimate Version)")
    parser.add_argument('--input', required=True, help="Input CSV containing ARNs")
    parser.add_argument('--dry-run', action='store_true', help="Dry run mode")
    args = parser.parse_args()

    resources = parse_arns(args.input)
    summarize_resources(resources)

    if not args.dry_run:
        confirm_deletion()

    results = []
    threads = []
    pbar = tqdm(total=len(resources), desc="Processing Resources", unit="resource")

    for resource in resources:
        t = threading.Thread(target=worker, args=(resource, args.dry_run, results, pbar))
        t.start()
        threads.append(t)

    for t in threads:
        t.join()

    pbar.close()

    report_file = f"aws_cleanup_report_{timestamp}.csv"
    write_report(report_file, results)

    print(Fore.CYAN + f"\n✅ Cleanup completed. Report saved to {report_file}")
    print(Fore.CYAN + f"✅ Full log saved to {log_file}")

if __name__ == "__main__":
    main()
