{"summary": {"CR_Number": "GECHG1234567", "Operation_Type": "Deletion", "Timestamp": "2025-05-12 19:52:44", "Total_Resources": 53, "Validated_Resources": 53, "Dry_Run_Success": 0, "Dry_Run_Failed": 0, "Deletion_Success": 3, "Deletion_Failed": 50, "Resources_With_Dependencies": 0}, "operation_type": "DELETE", "resources": [{"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-prd-UAI2004829/8ba285cac7d39a9d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-sparks-prd-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.12s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-stg-UAI2004829/4dee790b9220ed70", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-sparks-stg-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.00s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5s-sparks-stg-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.28s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-npi-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-npi-sparks-stg-UAI2004829/7d0c809bd9e2dc90", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Target group i5p-npi-sparks-stg-UAI2004829 not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.19s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-gs-npi-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680", "Deleted_Date": "2025-05-12 19:53:48", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "29.95s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: LoadBalancerNotFound - One or more load balancers not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.01s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: LoadBalancerNotFound - One or more load balancers not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.04s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: LoadBalancerNotFound - One or more load balancers not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.85s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: LoadBalancerNotFound - One or more load balancers not found", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.99s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "test-instance-emgs-sparks-qa-aws-app-xLorRk", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:test-instance-emgs-sparks-qa-aws-app-xLorRk", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: test-instance-emgs-sparks-qa-aws-app-xLorRk because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.92s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials_for_i-05eb965ac3fd8fb23-5oODKX because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.50s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-02e58313d98d848b6", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-02e58313d98d848b6", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidGroup.NotFound - The security group 'sg-02e58313d98d848b6' does not exist", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.54s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials-for-i-002be9747294e8877-V87TL4", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials-for-i-002be9747294e8877-V87TL4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials-for-i-002be9747294e8877-V87TL4 because no identity-based policy allows the secretsmanager:DeleteSecret action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.95s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.36s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.22s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0199ae65cc9c2346b", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0199ae65cc9c2346b", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0199ae65cc9c2346b' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.29s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.38s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-09df14aa14c7e6408", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-09df14aa14c7e6408", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-09df14aa14c7e6408' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.38s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.32s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.19s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.36s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.29s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-00a54eeb559a244ba", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-00a54eeb559a244ba", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-00a54eeb559a244ba' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.46s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-0c13321fab05fc3a2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-0c13321fab05fc3a2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidGroup.NotFound - The security group 'sg-0c13321fab05fc3a2' does not exist", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.46s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-03255a7c76bfd0a7c", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-03255a7c76bfd0a7c", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidGroup.NotFound - The security group 'sg-03255a7c76bfd0a7c' does not exist", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.45s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08c1f59c7edbadcd9", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08c1f59c7edbadcd9", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-08c1f59c7edbadcd9' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.53s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.24s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-087df90f8ba056c60", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-087df90f8ba056c60", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Resource has dependencies: resource sg-087df90f8ba056c60 has a dependent object", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.40s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.32s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f/324b6ea53bdb69c2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.44s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.36s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f/58545befb00779d0", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.27s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-09d098800c98f744a", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-09d098800c98f744a", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Resource has dependencies: resource sg-09d098800c98f744a has a dependent object", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.68s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.34s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.28s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.64s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-002be9747294e8877", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-002be9747294e8877", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidInstanceID.NotFound - The instance ID 'i-002be9747294e8877' does not exist", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.71s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-05eb965ac3fd8fb23", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-05eb965ac3fd8fb23", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidInstanceID.NotFound - The instance ID 'i-05eb965ac3fd8fb23' does not exist", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.35s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0321aa3341012dc5d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0321aa3341012dc5d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0321aa3341012dc5d' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.54s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08292e054f597a702", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08292e054f597a702", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-08292e054f597a702' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.39s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0f23739b81fce377d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0f23739b81fce377d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0f23739b81fce377d' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "2.98s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0a61a13260c3b8461", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0a61a13260c3b8461", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0a61a13260c3b8461' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.42s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0b51ed17665aabeae", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0b51ed17665aabeae", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Volume vol-0b51ed17665aabeae deleted successfully", "Deleted_Date": "2025-05-12 19:58:47", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "39.33s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-002d9c016266809f2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-002d9c016266809f2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-002d9c016266809f2' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.25s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0cd56d66953e6c11f", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0cd56d66953e6c11f", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0cd56d66953e6c11f' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.32s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-05b68331cc9022940", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-05b68331cc9022940", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: IncorrectState - Unable to detach root volume 'vol-05b68331cc9022940' from instance 'i-01ff028a35ce74562'", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.69s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a25ca18b9d2ffbe", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a25ca18b9d2ffbe", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-08a25ca18b9d2ffbe' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.30s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0d221edcc77bf155d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0d221edcc77bf155d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-0d221edcc77bf155d' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.04s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a697f12101a69f5", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a697f12101a69f5", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "No", "Deletion_Message": "Error: InvalidVolume.NotFound - The volume 'vol-08a697f12101a69f5' does not exist.", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "3.06s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-01ff028a35ce74562", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-01ff028a35ce74562", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Validation skipped", "Dry_Run_Success": "N/A", "Dry_Run_Message": null, "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from running to shutting-down", "Deleted_Date": "2025-05-12 19:59:30", "Dependencies": "None", "Validation_Duration": "N/A", "Dry_Run_Duration": "N/A", "Deletion_Duration": "4.34s"}]}