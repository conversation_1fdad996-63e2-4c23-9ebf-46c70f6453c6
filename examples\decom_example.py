#!/usr/bin/env python3
"""
Example script demonstrating how to use the AWS Resource Decommissioning Tool programmatically.
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from aws_decom.core.config_manager import ConfigManager
from aws_decom.core.session_manager import SessionManager
from aws_decom.core.resource_manager import ResourceManager
from aws_decom.core.validator import Validator
from aws_decom.utils.input_manager import InputManager
from aws_decom.models.report import DecommissioningReport

def main():
    """Main function."""
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # Initialize components
    config_manager = ConfigManager("../config/config.yaml")
    session_manager = SessionManager(config_manager)
    resource_manager = ResourceManager(config_manager, session_manager)
    validator = Validator(config_manager)
    input_manager = InputManager()
    
    # Define parameters
    input_file = "resources.csv"
    cr_number = "GECHG1234567"
    dry_run = True
    
    # Validate CR number
    valid_cr, cr_message = validator.validate_cr_number(cr_number)
    if not valid_cr:
        logger.error(cr_message)
        sys.exit(1)
    
    # Load ARNs from input file
    arns = input_manager.load_arns_from_file(input_file)
    if not arns:
        logger.error(f"No ARNs found in input file: {input_file}")
        sys.exit(1)
    
    # Validate ARNs
    valid_arns, invalid_arns = validator.validate_arns(arns)
    if invalid_arns:
        logger.warning(f"Found {len(invalid_arns)} invalid ARNs")
    
    if not valid_arns:
        logger.error("No valid ARNs found")
        sys.exit(1)
    
    # Process resources
    logger.info(f"Processing {len(valid_arns)} resources in {'dry-run' if dry_run else 'deletion'} mode")
    report = resource_manager.process_resources(valid_arns, cr_number, dry_run)
    
    # Save report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = report.save_csv("../reports")
    json_path = report.save_json("../reports")
    
    logger.info(f"CSV report saved to: {csv_path}")
    logger.info(f"JSON report saved to: {json_path}")
    
    # Print summary
    summary = report.get_summary()
    logger.info(f"Total resources: {summary['Total_Resources']}")
    logger.info(f"Validated resources: {summary['Validated_Resources']}")
    logger.info(f"Dry run success: {summary['Dry_Run_Success']}")
    logger.info(f"Dry run failed: {summary['Dry_Run_Failed']}")
    
    if report.dry_run_failed > 0:
        logger.warning(f"Operation completed with {report.dry_run_failed} failures")
    else:
        logger.info("Operation completed successfully")

if __name__ == "__main__":
    main()
