# AWS Resource Decommissioning Tool - Enterprise Edition

An enterprise-grade tool for safely decommissioning AWS resources across multiple accounts.

## Features

- **Comprehensive Resource Support**: Handles EC2 instances, S3 buckets, Lambda functions, and many other AWS resource types
- **Multi-Account Support**: Works across multiple AWS accounts using profile switching
- **Dependency Detection**: Identifies and handles resource dependencies
- **Dry Run Mode**: Simulates deletion without actually deleting resources
- **Detailed Reporting**: Generates comprehensive CSV and JSON reports
- **Robust Error Handling**: Gracefully handles errors and provides detailed error messages
- **Configurable**: Extensive configuration options via YAML files and environment variables
- **Secure**: Uses SSL verification and follows AWS security best practices

## Installation

```bash
# Clone the repository
git clone https://github.com/your-org/aws-decom.git
cd aws-decom

# Install the package
pip install -e .
```

## Usage

```bash
# Dry run mode (no actual deletions)
aws-decom --input resources.csv --cr-no GECHG1234567 --dry-run

# Deletion mode (will delete resources)
aws-decom --input resources.csv --cr-no GECHG1234567 --delete

# Force deletion mode (will delete resources and their dependencies)
aws-decom --input resources.csv --cr-no GECHG1234567 --delete --force
```

### Input File Format

The input file should contain one ARN per line. The following formats are supported:

- CSV: First column should contain ARNs
- JSON: List of ARNs or objects with 'arn' field
- TXT: One ARN per line

Example CSV:
```
arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0
arn:aws:s3:::my-bucket
arn:aws:lambda:us-east-1:************:function:my-function
```

## Configuration

Configuration is stored in YAML files in the `config` directory:

- `config.yaml`: Main configuration file
- `logging_config.yaml`: Logging configuration

You can override configuration settings using environment variables prefixed with `AWS_DECOM_`.

## AWS Profiles

The tool uses AWS profiles for authentication. Profiles should follow the naming convention:

```
support-{account_id}
```

For example, for account `************`, the profile should be named `support-************`.

## Supported Resource Types

- EC2: Instances, Volumes, Snapshots, AMIs, Security Groups
- S3: Buckets
- Lambda: Functions
- CloudWatch: Alarms
- Secrets Manager: Secrets
- SNS: Topics, Subscriptions
- Step Functions: State Machines
- EventBridge: Rules
- CloudFormation: Stacks
- ELB: Load Balancers, Target Groups, Listeners, Rules
- SSM: Parameters
- AWS Backup: Backup Plans, Backup Vaults

## Reports

Reports are generated in the `reports` directory in CSV and JSON formats. They include:

- Resource details (ARN, type, ID, etc.)
- Operation results (success/failure)
- Error messages
- Timing information
- Dependency information

## Logs

Logs are stored in the `logs` directory. The logging level can be controlled via the `--verbose` flag.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
