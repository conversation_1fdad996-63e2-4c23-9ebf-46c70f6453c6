"""
CloudFormation Stack handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class CloudFormationStackHandler(BaseResourceHandler):
    """
    Handler for CloudFormation Stacks.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the CloudFormation Stack handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a CloudFormation Stack exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Check if stack exists
            response = cfn.describe_stacks(StackName=stack_name)
            
            # Check if there are any stacks
            if not response.get('Stacks'):
                return False, f"Stack {stack_name} not found"
            
            # Get stack status
            stack = response['Stacks'][0]
            status = stack.get('StackStatus')
            
            # Check if stack is in a deletable state
            if status in ['CREATE_IN_PROGRESS', 'ROLLBACK_IN_PROGRESS', 'DELETE_IN_PROGRESS', 
                          'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS', 
                          'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS',
                          'REVIEW_IN_PROGRESS', 'IMPORT_IN_PROGRESS', 'IMPORT_ROLLBACK_IN_PROGRESS']:
                return False, f"Stack {stack_name} is in {status} state and cannot be deleted"
            
            # Check if stack has termination protection
            if stack.get('EnableTerminationProtection', False):
                return False, f"Stack {stack_name} has termination protection enabled"
            
            # Check if stack has nested stacks
            try:
                resources = cfn.list_stack_resources(StackName=stack_name)
                
                for resource in resources.get('StackResourceSummaries', []):
                    if resource.get('ResourceType') == 'AWS::CloudFormation::Stack':
                        self.logger.info(f"Stack {stack_name} has nested stack {resource.get('PhysicalResourceId')}")
            except ClientError as e:
                self.logger.warning(f"Error checking stack resources: {e}")
            
            return True, f"Stack {stack_name} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating CloudFormation Stack")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent CloudFormation Stack deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Check for nested stacks
            try:
                resources = cfn.list_stack_resources(StackName=stack_name)
                
                for resource in resources.get('StackResourceSummaries', []):
                    if resource.get('ResourceType') == 'AWS::CloudFormation::Stack':
                        nested_stack_id = resource.get('PhysicalResourceId')
                        dependencies.append(ResourceDependency(
                            resource_type="cloudformation:stack",
                            resource_id=nested_stack_id,
                            relationship="parent_of",
                            blocking=False  # Not blocking because nested stacks are deleted with the parent
                        ))
            except ClientError as e:
                self.logger.warning(f"Error checking stack resources: {e}")
            
            # Check for stacks that depend on this stack (exports)
            try:
                exports = cfn.list_exports()
                
                for export in exports.get('Exports', []):
                    if export.get('ExportingStackId') == stack_name:
                        export_name = export.get('Name')
                        
                        # Check if any stack imports this export
                        imports = cfn.list_imports(ExportName=export_name)
                        
                        for importing_stack in imports.get('Imports', []):
                            dependencies.append(ResourceDependency(
                                resource_type="cloudformation:stack",
                                resource_id=importing_stack,
                                relationship="imports_from",
                                blocking=True  # Blocking because importing stacks must be deleted first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking stack exports: {e}")
            
            # Check for stack sets that include this stack
            try:
                stack_sets = cfn.list_stack_sets()
                
                for stack_set in stack_sets.get('Summaries', []):
                    stack_set_name = stack_set.get('StackSetName')
                    
                    # Check if this stack is part of the stack set
                    stack_instances = cfn.list_stack_instances(StackSetName=stack_set_name)
                    
                    for instance in stack_instances.get('Summaries', []):
                        if instance.get('StackId') == stack_name:
                            dependencies.append(ResourceDependency(
                                resource_type="cloudformation:stackset",
                                resource_id=stack_set_name,
                                relationship="instance_of",
                                blocking=True  # Blocking because stack set must be updated first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking stack sets: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for CloudFormation Stack {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of CloudFormation Stack deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Check if stack exists
            response = cfn.describe_stacks(StackName=stack_name)
            
            # Check if there are any stacks
            if not response.get('Stacks'):
                return False, f"Stack {stack_name} not found"
            
            # Get stack status
            stack = response['Stacks'][0]
            status = stack.get('StackStatus')
            
            # Check if stack is in a deletable state
            if status in ['CREATE_IN_PROGRESS', 'ROLLBACK_IN_PROGRESS', 'DELETE_IN_PROGRESS', 
                          'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS', 
                          'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS',
                          'REVIEW_IN_PROGRESS', 'IMPORT_IN_PROGRESS', 'IMPORT_ROLLBACK_IN_PROGRESS']:
                return False, f"Stack {stack_name} is in {status} state and cannot be deleted"
            
            # Check if stack has termination protection
            if stack.get('EnableTerminationProtection', False):
                return False, f"Stack {stack_name} has termination protection enabled"
            
            # Check for resources to retain
            retain_resources = self.config.get("resources.services.cloudformation.retain_resources", [])
            
            if retain_resources:
                self.logger.info(f"Dry run: Would delete stack {stack_name} with {len(retain_resources)} resources retained")
            else:
                self.logger.info(f"Dry run: Would delete stack {stack_name}")
            
            return True, f"Stack {stack_name} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of CloudFormation Stack")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a CloudFormation Stack.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Check if stack exists
            response = cfn.describe_stacks(StackName=stack_name)
            
            # Check if there are any stacks
            if not response.get('Stacks'):
                return False, f"Stack {stack_name} not found"
            
            # Get stack status
            stack = response['Stacks'][0]
            status = stack.get('StackStatus')
            
            # Check if stack is in a deletable state
            if status in ['CREATE_IN_PROGRESS', 'ROLLBACK_IN_PROGRESS', 'DELETE_IN_PROGRESS', 
                          'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS', 
                          'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS',
                          'REVIEW_IN_PROGRESS', 'IMPORT_IN_PROGRESS', 'IMPORT_ROLLBACK_IN_PROGRESS']:
                return False, f"Stack {stack_name} is in {status} state and cannot be deleted"
            
            # Check if stack has termination protection
            if stack.get('EnableTerminationProtection', False):
                return False, f"Stack {stack_name} has termination protection enabled"
            
            # Check for resources to retain
            retain_resources = self.config.get("resources.services.cloudformation.retain_resources", [])
            
            # Delete the stack
            if retain_resources:
                cfn.delete_stack(StackName=stack_name, RetainResources=retain_resources)
                return True, f"Stack {stack_name} deletion initiated with {len(retain_resources)} resources retained"
            else:
                cfn.delete_stack(StackName=stack_name)
                return True, f"Stack {stack_name} deletion initiated"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting CloudFormation Stack")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of a CloudFormation Stack.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Get stack details
            response = cfn.describe_stacks(StackName=stack_name)
            
            if not response.get('Stacks'):
                return None
            
            stack = response['Stacks'][0]
            creation_time = stack.get('CreationTime')
            
            if creation_time:
                return creation_time.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for CloudFormation Stack {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a CloudFormation Stack.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            cfn = session.client('cloudformation', region_name=resource.region)
            
            # Extract stack name from ARN or resource ID
            stack_name = resource.resource_id
            if '/' in stack_name:
                stack_name = stack_name.split('/')[-1]
            
            # Get stack details
            response = cfn.describe_stacks(StackName=stack_name)
            
            if not response.get('Stacks'):
                return {}
            
            stack = response['Stacks'][0]
            tags = stack.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for CloudFormation Stack {resource.resource_id}: {e}")
            return {}
