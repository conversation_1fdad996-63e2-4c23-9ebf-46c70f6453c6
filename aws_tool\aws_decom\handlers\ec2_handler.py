"""
EC2 resource handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tu<PERSON>, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EC2InstanceHandler(BaseResourceHandler):
    """
    Handler for EC2 instances.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EC2 instance handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EC2 instance exists and can be terminated.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if instance exists
            response = ec2.describe_instances(InstanceIds=[resource.resource_id])
            
            # Check if there are any reservations
            if not response.get('Reservations'):
                return False, f"Instance {resource.resource_id} not found"
            
            # Get instance state
            instance = response['Reservations'][0]['Instances'][0]
            state = instance.get('State', {}).get('Name')
            
            # Check if instance is already terminated
            if state == 'terminated':
                return False, f"Instance {resource.resource_id} is already terminated"
            
            # Check if instance is shutting down
            if state == 'shutting-down':
                return False, f"Instance {resource.resource_id} is already shutting down"
            
            # Check termination protection
            try:
                attr_response = ec2.describe_instance_attribute(
                    InstanceId=resource.resource_id,
                    Attribute='disableApiTermination'
                )
                termination_protection = attr_response.get('DisableApiTermination', {}).get('Value', False)
                
                if termination_protection:
                    can_disable = self.config.get("resources.services.ec2.terminate_protected_instances", False)
                    if not can_disable:
                        return False, f"Instance {resource.resource_id} has termination protection enabled"
            except ClientError as e:
                return self.handle_client_error(e, "checking termination protection")
            
            return True, f"Instance {resource.resource_id} exists and can be terminated"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EC2 instance")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EC2 instance termination.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Get instance details
            response = ec2.describe_instances(InstanceIds=[resource.resource_id])
            
            if not response.get('Reservations'):
                return dependencies
            
            instance = response['Reservations'][0]['Instances'][0]
            
            # Check for EBS volumes
            for volume in instance.get('BlockDeviceMappings', []):
                if 'Ebs' in volume:
                    volume_id = volume['Ebs']['VolumeId']
                    dependencies.append(ResourceDependency(
                        resource_type="ec2:volume",
                        resource_id=volume_id,
                        relationship="attached_to",
                        blocking=False  # Not blocking because volumes are automatically detached
                    ))
            
            # Check for network interfaces
            for eni in instance.get('NetworkInterfaces', []):
                eni_id = eni['NetworkInterfaceId']
                dependencies.append(ResourceDependency(
                    resource_type="ec2:network-interface",
                    resource_id=eni_id,
                    relationship="attached_to",
                    blocking=False  # Not blocking because ENIs are automatically detached
                ))
            
            # Check if instance is part of an Auto Scaling group
            asg_client = session.client('autoscaling', region_name=resource.region)
            try:
                asg_response = asg_client.describe_auto_scaling_instances(InstanceIds=[resource.resource_id])
                if asg_response.get('AutoScalingInstances'):
                    asg_name = asg_response['AutoScalingInstances'][0]['AutoScalingGroupName']
                    dependencies.append(ResourceDependency(
                        resource_type="autoscaling:group",
                        resource_id=asg_name,
                        relationship="member_of",
                        blocking=True  # Blocking because ASG will replace the instance
                    ))
            except ClientError:
                # Ignore errors from ASG API
                pass
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EC2 instance {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EC2 instance termination.
        
        Args:
            resource: Resource to terminate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Try to terminate with DryRun=True
            ec2.terminate_instances(
                InstanceIds=[resource.resource_id],
                DryRun=True
            )
            
            # If we get here, something went wrong (DryRun should raise an exception)
            return False, "Unexpected success in dry run mode"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run termination of EC2 instance")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Terminate an EC2 instance.
        
        Args:
            resource: Resource to terminate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if termination protection is enabled
            try:
                attr_response = ec2.describe_instance_attribute(
                    InstanceId=resource.resource_id,
                    Attribute='disableApiTermination'
                )
                termination_protection = attr_response.get('DisableApiTermination', {}).get('Value', False)
                
                # Disable termination protection if configured to do so
                if termination_protection:
                    can_disable = self.config.get("resources.services.ec2.terminate_protected_instances", False)
                    if can_disable:
                        ec2.modify_instance_attribute(
                            InstanceId=resource.resource_id,
                            DisableApiTermination={'Value': False}
                        )
                        self.logger.info(f"Disabled termination protection for instance {resource.resource_id}")
                    else:
                        return False, f"Instance {resource.resource_id} has termination protection enabled"
            except ClientError as e:
                return self.handle_client_error(e, "checking termination protection")
            
            # Terminate the instance
            response = ec2.terminate_instances(
                InstanceIds=[resource.resource_id]
            )
            
            # Check the response
            for instance in response.get('TerminatingInstances', []):
                if instance['InstanceId'] == resource.resource_id:
                    prev_state = instance.get('PreviousState', {}).get('Name', 'unknown')
                    new_state = instance.get('CurrentState', {}).get('Name', 'unknown')
                    return True, f"Instance state changed from {prev_state} to {new_state}"
            
            return False, "Instance not found in termination response"
            
        except ClientError as e:
            return self.handle_client_error(e, "terminating EC2 instance")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the launch time of an EC2 instance.
        
        Args:
            resource: Resource to get launch time for.
            session: boto3 session.
            
        Returns:
            Launch time as string or None if not available.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_instances(InstanceIds=[resource.resource_id])
            
            if not response.get('Reservations'):
                return None
            
            instance = response['Reservations'][0]['Instances'][0]
            launch_time = instance.get('LaunchTime')
            
            if launch_time:
                return launch_time.strftime("%Y-%m-%d %H:%M:%S")
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting launch time for EC2 instance {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EC2 instance.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_instances(InstanceIds=[resource.resource_id])
            
            if not response.get('Reservations'):
                return {}
            
            instance = response['Reservations'][0]['Instances'][0]
            tags = instance.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EC2 instance {resource.resource_id}: {e}")
            return {}
