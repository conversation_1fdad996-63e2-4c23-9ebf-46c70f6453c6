{"asctime": "2025-04-29 08:23:07,891", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:23:07,901", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:23:07,908", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:23:07,912", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:23:07,922", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:23:07,924", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:23:07,927", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:23:07,930", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:23:07,932", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:23:07,933", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:23:07,936", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:23:07,939", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:23:07,939", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:23:07,939", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:23:07,948", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:23:07,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:23:07,954", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:23:07,956", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:23:07,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:23:07,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:23:07,963", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:23:07,966", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:23:07,966", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:23:07,966", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:23:07,972", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:23:07,972", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:23:07,972", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:23:07,979", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:23:07,979", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:23:07,981", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:23:07,987", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:23:07,987", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:23:07,989", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:23:07,989", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:23:07,992", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:23:07,992", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:23:07,997", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:23:07,999", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:23:07,999", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:23:08,004", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:23:08,004", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:23:08,009", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:23:08,009", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:23:08,014", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:23:08,016", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:23:08,036", "name": "aws_decom.utils.input_manager", "levelname": "ERROR", "message": "Error loading ARNs from CSV file test_resources.csv: Could not determine delimiter"}
{"asctime": "2025-04-29 08:23:08,039", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 0 ARNs from CSV file test_resources.csv"}
{"asctime": "2025-04-29 08:23:08,044", "name": "__main__", "levelname": "ERROR", "message": "No ARNs found in input file: test_resources.csv"}
{"asctime": "2025-04-29 08:24:17,539", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:24:17,539", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:24:17,539", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:24:17,539", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:24:17,539", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:24:17,543", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:24:17,545", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:24:17,549", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:24:17,555", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:24:17,560", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:24:17,561", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:24:17,562", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:24:17,563", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:24:17,564", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:24:17,569", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:24:17,569", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:24:17,569", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:24:17,577", "name": "aws_decom.utils.input_manager", "levelname": "ERROR", "message": "Error loading ARNs from CSV file test_handlers.csv: Could not determine delimiter"}
{"asctime": "2025-04-29 08:24:17,579", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 0 ARNs from CSV file test_handlers.csv"}
{"asctime": "2025-04-29 08:24:17,580", "name": "__main__", "levelname": "ERROR", "message": "No ARNs found in input file: test_handlers.csv"}
{"asctime": "2025-04-29 08:24:39,676", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:24:39,676", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:24:39,679", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:24:39,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:24:39,689", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:24:39,690", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:24:39,692", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:24:39,692", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:24:39,692", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:24:39,694", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:24:39,696", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:24:39,697", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:24:39,699", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:24:39,707", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:24:39,707", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:24:39,707", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:24:39,707", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:24:39,709", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:24:39,709", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:24:39,709", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:24:39,709", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:24:39,712", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:24:39,713", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:24:39,719", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_handlers.txt"}
{"asctime": "2025-04-29 08:24:39,722", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:24:39,728", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:24:39,728", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:24:39,731", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:24:39,732", "name": "aws_decom.core.validator", "levelname": "WARNING", "message": "Unsupported resource type: backup:backup-vault for ARN: arn:aws:backup:us-east-1:************:backup-vault:test-vault"}
{"asctime": "2025-04-29 08:24:39,734", "name": "aws_decom.core.validator", "levelname": "WARNING", "message": "Unsupported resource type: backup:backup-plan for ARN: arn:aws:backup:us-east-1:************:backup-plan:1234567890abcdef"}
{"asctime": "2025-04-29 08:24:39,735", "name": "__main__", "levelname": "WARNING", "message": "Found 2 unsupported resource types"}
{"asctime": "2025-04-29 08:24:39,736", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:24:39,737", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:25:15,793", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:25:15,793", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:25:15,840", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,840", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,849", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:25:15,850", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:15,852", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:25:15,907", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,908", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,910", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:25:15,912", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:15,914", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:25:15,968", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:15,970", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:15,972", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:25:15,974", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:15,976", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:25:16,022", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,023", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,023", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:25:16,023", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:16,028", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:25:16,087", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,089", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,090", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:25:16,091", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:25:16,092", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:25:16,094", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:25:16,134", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:25:16,139", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:25:16,139", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:25:16,139", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:26:18,761", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:26:18,768", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:26:18,769", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:26:18,769", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:26:18,769", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:26:18,769", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:26:18,773", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:26:18,774", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:26:18,776", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:26:18,777", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:26:18,778", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:26:18,779", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:26:18,788", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:26:18,790", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:26:18,791", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:26:18,792", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:26:18,793", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:26:18,794", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:26:18,795", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:26:18,796", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:26:18,796", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:26:18,797", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:26:18,798", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:26:18,798", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:26:18,799", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:26:18,800", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:26:18,800", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:26:18,801", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:26:18,802", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:26:18,808", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 7 ARNs from text file test_handlers.txt"}
{"asctime": "2025-04-29 08:26:56,469", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:26:56,469", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:26:56,469", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:26:56,469", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:26:56,472", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:26:56,477", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:26:56,477", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:26:56,477", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:26:56,479", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:26:56,487", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:26:56,487", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:26:56,487", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:26:56,489", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:26:56,493", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:26:56,494", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:26:56,494", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:26:56,494", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:26:56,494", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:26:56,494", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:26:56,500", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:26:56,504", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:26:56,504", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:26:56,508", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:26:56,510", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:26:56,512", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:26:56,514", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:27:20,341", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:27:20,342", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:27:20,343", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:27:20,344", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:27:20,345", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:27:20,346", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:27:20,347", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:27:20,350", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:27:20,350", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:27:20,351", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:27:20,351", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:27:20,352", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:27:20,353", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:27:20,354", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:27:20,355", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:27:20,356", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:27:20,356", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:27:20,357", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:27:20,357", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:27:20,358", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:27:20,358", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:27:20,360", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:27:20,360", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:27:20,361", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:27:20,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:27:20,363", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:27:20,364", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:27:20,365", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:27:20,365", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:27:20,366", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:27:20,367", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:27:20,367", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:27:20,368", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:27:20,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:27:20,370", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:27:20,371", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:27:20,372", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:27:20,373", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:27:20,374", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:27:20,375", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:27:20,376", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:27:20,377", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:27:20,378", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:27:20,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:27:20,380", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:27:20,386", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:27:20,390", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:27:20,392", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:27:20,394", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:27:20,395", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:27:20,396", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:27:20,398", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:27:23,430", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:27:23,430", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:27:23,479", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,480", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,480", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:27:23,480", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,488", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:27:23,523", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,531", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,531", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:27:23,531", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,535", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:27:23,574", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,576", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,580", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:27:23,580", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,582", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:27:23,628", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,630", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,631", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:27:23,631", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,635", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:27:23,700", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,708", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:27:23,709", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:27:23,769", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,769", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,775", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:27:23,777", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,780", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:27:23,832", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,839", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,842", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:27:23,847", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:27:23,850", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:27:23,899", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:27:23,904", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:27:23,904", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:27:23,904", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:13,300", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:31:13,300", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:31:13,300", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:31:13,304", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:31:13,304", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:31:13,306", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:31:13,306", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:31:13,306", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:31:13,306", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:31:13,306", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:31:13,309", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:31:13,314", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:31:13,315", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:31:13,316", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:31:13,316", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:31:13,316", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:31:13,320", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:31:13,321", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:31:13,329", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:31:13,330", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:31:13,337", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:31:13,339", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:31:13,339", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:31:13,349", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:31:13,349", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:31:13,351", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:31:13,352", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:31:13,354", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:31:48,636", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:31:48,637", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:31:48,670", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,670", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,675", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:31:48,676", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,677", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:31:48,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,719", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:31:48,778", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,780", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,782", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:31:48,784", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,786", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:31:48,884", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,889", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,894", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:31:48,895", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:48,898", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:31:48,984", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:48,990", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:48,994", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:31:48,994", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,018", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:31:49,021", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:31:49,142", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,142", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,150", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:31:49,150", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,153", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:31:49,200", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,200", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,208", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:31:49,209", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:31:49,212", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:31:49,255", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:31:49,261", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:31:49,261", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:31:49,261", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:26,360", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:32:26,360", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:32:26,362", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:32:26,369", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:32:26,374", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:32:26,374", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:32:26,374", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:32:26,374", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:32:26,378", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:32:26,378", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:32:26,379", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:32:26,389", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:32:26,390", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:32:26,392", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:32:26,392", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:32:26,394", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:32:26,399", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:32:26,399", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:32:26,401", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:32:26,401", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 11 ARNs from CSV file input.csv"}
{"asctime": "2025-04-29 08:32:33,892", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0c7b6d9a6a2ed1028 in us-east-1"}
{"asctime": "2025-04-29 08:32:40,049", "name": "aws_decom.core.session_manager", "levelname": "INFO", "message": "Created session for account ************ using profile support-************"}
{"asctime": "2025-04-29 08:32:42,799", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0c7b6d9a6a2ed1028' does not exist"}
{"asctime": "2025-04-29 08:32:42,799", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0c7b6d9a6a2ed1028 in us-east-1"}
{"asctime": "2025-04-29 08:32:42,799", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:42,808", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-09b8ea45284479875 in us-east-1"}
{"asctime": "2025-04-29 08:32:42,808", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-09b8ea45284479875' does not exist"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-09b8ea45284479875 in us-east-1"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-087886ad913505771 in us-east-1"}
{"asctime": "2025-04-29 08:32:45,359", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-087886ad913505771' does not exist"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-087886ad913505771 in us-east-1"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:47,913", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-06f9ef925e37efd17 in us-east-1"}
{"asctime": "2025-04-29 08:32:47,919", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:50,434", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-06f9ef925e37efd17' does not exist"}
{"asctime": "2025-04-29 08:32:50,434", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-06f9ef925e37efd17 in us-east-1"}
{"asctime": "2025-04-29 08:32:50,440", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:50,442", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-00e0cdac1a0eb0a21 in us-east-1"}
{"asctime": "2025-04-29 08:32:50,444", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:52,954", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-00e0cdac1a0eb0a21' does not exist"}
{"asctime": "2025-04-29 08:32:52,959", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-00e0cdac1a0eb0a21 in us-east-1"}
{"asctime": "2025-04-29 08:32:52,959", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:52,964", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-022732e968cba9142 in us-east-1"}
{"asctime": "2025-04-29 08:32:52,965", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:55,489", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-022732e968cba9142' does not exist"}
{"asctime": "2025-04-29 08:32:55,489", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-022732e968cba9142 in us-east-1"}
{"asctime": "2025-04-29 08:32:55,500", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:55,500", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0a8e2d1d949a06cd0 in us-east-1"}
{"asctime": "2025-04-29 08:32:55,500", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0a8e2d1d949a06cd0' does not exist"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0a8e2d1d949a06cd0 in us-east-1"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-041570276467c4850 in us-east-1"}
{"asctime": "2025-04-29 08:32:57,989", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:33:00,589", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-041570276467c4850' does not exist"}
{"asctime": "2025-04-29 08:33:00,589", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-041570276467c4850 in us-east-1"}
{"asctime": "2025-04-29 08:33:00,589", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:00,600", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0744b90d043ddddff in us-east-1"}
{"asctime": "2025-04-29 08:33:00,602", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:33:03,139", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0744b90d043ddddff' does not exist"}
{"asctime": "2025-04-29 08:33:03,142", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0744b90d043ddddff in us-east-1"}
{"asctime": "2025-04-29 08:33:03,143", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:03,144", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0fb6c6121c0c0411c in us-east-1"}
{"asctime": "2025-04-29 08:33:03,145", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:33:05,700", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0fb6c6121c0c0411c' does not exist"}
{"asctime": "2025-04-29 08:33:05,709", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0fb6c6121c0c0411c in us-east-1"}
{"asctime": "2025-04-29 08:33:05,712", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:33:05,712", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0e9f24f2def2eed52 in us-east-1"}
{"asctime": "2025-04-29 08:33:05,712", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:33:08,234", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0e9f24f2def2eed52' does not exist"}
{"asctime": "2025-04-29 08:33:08,239", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0e9f24f2def2eed52 in us-east-1"}
{"asctime": "2025-04-29 08:33:08,239", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:27,126", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:39:27,127", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:39:27,127", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:39:27,127", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:39:27,127", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:39:27,129", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:39:27,137", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:39:27,138", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:39:27,139", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:39:27,140", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:39:27,141", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:39:27,141", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:39:27,142", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:39:27,143", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:39:27,150", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:39:27,150", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:39:27,151", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:39:27,151", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:39:27,153", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:39:27,153", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:39:27,153", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:39:27,153", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:39:27,156", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:39:27,157", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:39:27,158", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:39:27,160", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:39:27,160", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:39:27,160", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:39:27,170", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:39:27,171", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:39:27,172", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:39:27,174", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:39:31,759", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:39:31,829", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:31,832", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:39:31,893", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:31,903", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:31,910", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:39:31,913", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:31,976", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:31,978", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:39:32,029", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,098", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,100", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:39:32,140", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,142", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,142", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:39:32,147", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,184", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,186", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:39:32,220", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,223", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,229", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:39:32,229", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,269", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,272", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,309", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:39:32,349", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,352", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:39:32,389", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,392", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,392", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:39:32,392", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,500", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,514", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:39:32,559", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:39:32,599", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,599", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:39:32,631", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:39:32,631", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:39:32,631", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:39:32,640", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:06,589", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:40:06,589", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:40:06,596", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:40:06,596", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:40:06,599", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:40:06,599", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:40:06,601", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:40:06,601", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:40:06,603", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:40:06,608", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:40:06,609", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:40:06,619", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:40:06,630", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:40:06,630", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:40:06,630", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:40:06,630", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:40:06,630", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 11 ARNs from CSV file input.csv"}
{"asctime": "2025-04-29 08:40:15,265", "name": "aws_decom.core.session_manager", "levelname": "INFO", "message": "Created session for account ************ using profile support-************"}
{"asctime": "2025-04-29 08:40:17,929", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0c7b6d9a6a2ed1028 in us-east-1"}
{"asctime": "2025-04-29 08:40:17,930", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:20,624", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0c7b6d9a6a2ed1028' does not exist"}
{"asctime": "2025-04-29 08:40:20,629", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0c7b6d9a6a2ed1028 in us-east-1"}
{"asctime": "2025-04-29 08:40:20,629", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:20,635", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:23,276", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-09b8ea45284479875 in us-east-1"}
{"asctime": "2025-04-29 08:40:23,276", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:25,859", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-09b8ea45284479875' does not exist"}
{"asctime": "2025-04-29 08:40:25,867", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-09b8ea45284479875 in us-east-1"}
{"asctime": "2025-04-29 08:40:25,870", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:25,872", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:28,474", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-087886ad913505771 in us-east-1"}
{"asctime": "2025-04-29 08:40:28,474", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:31,229", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-087886ad913505771' does not exist"}
{"asctime": "2025-04-29 08:40:31,229", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-087886ad913505771 in us-east-1"}
{"asctime": "2025-04-29 08:40:31,234", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:31,234", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:33,800", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-06f9ef925e37efd17 in us-east-1"}
{"asctime": "2025-04-29 08:40:33,800", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:36,386", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-06f9ef925e37efd17' does not exist"}
{"asctime": "2025-04-29 08:40:36,390", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-06f9ef925e37efd17 in us-east-1"}
{"asctime": "2025-04-29 08:40:36,390", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:36,390", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:38,943", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-00e0cdac1a0eb0a21 in us-east-1"}
{"asctime": "2025-04-29 08:40:38,946", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:41,539", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-00e0cdac1a0eb0a21' does not exist"}
{"asctime": "2025-04-29 08:40:41,544", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-00e0cdac1a0eb0a21 in us-east-1"}
{"asctime": "2025-04-29 08:40:41,547", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:41,547", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:44,099", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-022732e968cba9142 in us-east-1"}
{"asctime": "2025-04-29 08:40:44,099", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:46,769", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-022732e968cba9142' does not exist"}
{"asctime": "2025-04-29 08:40:46,774", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-022732e968cba9142 in us-east-1"}
{"asctime": "2025-04-29 08:40:46,777", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:46,780", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:49,262", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0a8e2d1d949a06cd0 in us-east-1"}
{"asctime": "2025-04-29 08:40:49,264", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0a8e2d1d949a06cd0' does not exist"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0a8e2d1d949a06cd0 in us-east-1"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:51,849", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:54,389", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-041570276467c4850 in us-east-1"}
{"asctime": "2025-04-29 08:40:54,389", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:56,999", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-041570276467c4850' does not exist"}
{"asctime": "2025-04-29 08:40:57,006", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-041570276467c4850 in us-east-1"}
{"asctime": "2025-04-29 08:40:57,007", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:40:57,009", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:40:59,564", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0744b90d043ddddff in us-east-1"}
{"asctime": "2025-04-29 08:40:59,569", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:41:02,364", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0744b90d043ddddff' does not exist"}
{"asctime": "2025-04-29 08:41:02,370", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0744b90d043ddddff in us-east-1"}
{"asctime": "2025-04-29 08:41:02,370", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:41:02,370", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:41:04,973", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0fb6c6121c0c0411c in us-east-1"}
{"asctime": "2025-04-29 08:41:04,973", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:41:07,659", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0fb6c6121c0c0411c' does not exist"}
{"asctime": "2025-04-29 08:41:07,663", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0fb6c6121c0c0411c in us-east-1"}
{"asctime": "2025-04-29 08:41:07,663", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:41:07,670", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:41:10,239", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ec2:security-group sg-0e9f24f2def2eed52 in us-east-1"}
{"asctime": "2025-04-29 08:41:10,239", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "Using cached session for account ************"}
{"asctime": "2025-04-29 08:41:12,814", "name": "aws_decom.handlers.base_handler.EC2SecurityGroupHandler", "levelname": "ERROR", "message": "Error during validating EC2 Security Group: InvalidGroup.NotFound - The security group 'sg-0e9f24f2def2eed52' does not exist"}
{"asctime": "2025-04-29 08:41:12,814", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ec2:security-group sg-0e9f24f2def2eed52 in us-east-1"}
{"asctime": "2025-04-29 08:41:12,819", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:43:38,641", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:43:38,641", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:43:38,649", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:43:38,649", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:43:38,649", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:43:38,652", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:43:38,659", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:43:38,659", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:43:38,659", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:43:38,662", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:43:38,662", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:43:38,665", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:43:38,670", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:43:38,675", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:43:38,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:43:38,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:43:38,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:43:38,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:43:38,680", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:43:38,685", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:43:38,685", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:43:38,690", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 11 ARNs from CSV file input.csv"}
{"asctime": "2025-04-29 08:43:43,150", "name": "__main__", "levelname": "INFO", "message": "Operation cancelled by user"}
{"asctime": "2025-04-29 08:51:21,880", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:51:21,881", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:51:21,882", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:51:21,883", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:51:21,884", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:51:21,886", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:51:21,887", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:51:21,887", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:51:21,888", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:51:21,889", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:51:21,890", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:51:21,891", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:51:21,892", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:51:21,893", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:51:21,893", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:51:21,894", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:51:21,895", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:51:21,895", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:51:21,896", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:51:21,897", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:51:21,899", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:51:21,901", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:51:21,902", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:51:21,903", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:51:21,904", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:51:21,906", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:51:21,908", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:51:21,909", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:51:21,911", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:51:21,911", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:51:21,912", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:51:21,913", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:51:21,914", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:51:21,915", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:51:21,916", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:51:21,916", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:51:21,917", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:51:21,918", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:51:21,919", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:51:21,919", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:51:21,922", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:51:21,923", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:51:21,924", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:51:21,924", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:51:21,925", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:51:21,932", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:51:21,935", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:51:21,939", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:51:21,940", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:51:21,942", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:51:21,943", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:51:21,945", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:51:25,211", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:51:25,275", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,278", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:51:25,319", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,364", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,364", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:51:25,398", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,399", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,399", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:51:25,399", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,451", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,458", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:51:25,499", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,531", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,540", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:51:25,581", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,620", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,623", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:51:25,662", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,664", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,666", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:51:25,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,670", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:51:25,706", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,709", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:51:25,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,789", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,789", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:51:25,831", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:51:25,873", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,873", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:51:25,924", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:51:25,930", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:51:25,931", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:51:25,931", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:56:45,944", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:56:45,944", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:56:45,947", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:56:45,949", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:56:45,950", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:56:45,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:56:45,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:56:45,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:56:45,962", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:56:45,962", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:56:45,965", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:56:45,969", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:56:45,970", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:56:45,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:56:45,980", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:56:45,981", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:56:45,983", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:56:45,983", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:56:45,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:56:45,989", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:56:45,989", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:56:45,989", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:56:45,997", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:56:46,002", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:56:46,004", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:56:46,004", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:56:46,007", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:56:46,009", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:56:46,009", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:57:03,391", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:57:03,421", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,422", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:57:03,461", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,461", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,465", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:57:03,466", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,509", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,510", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:57:03,549", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,624", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,625", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:57:03,666", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,671", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:57:03,671", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,732", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,732", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:57:03,793", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,793", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,793", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:57:03,800", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,864", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,866", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:57:03,919", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,919", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:03,922", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:57:03,924", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:03,927", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:57:03,970", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:03,974", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:57:04,010", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,012", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,013", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:57:04,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:04,049", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,049", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:57:04,083", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,083", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,083", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:57:04,089", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:04,129", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,130", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:57:04,164", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:57:04,166", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:57:04,166", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:57:04,169", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:57:36,956", "name": "aws_decom.core.session_manager", "levelname": "DEBUG", "message": "SSL verification enabled with certifi"}
{"asctime": "2025-04-29 08:57:36,957", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:57:36,957", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:57:36,958", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:57:36,959", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:57:36,960", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:57:36,960", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:57:36,961", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:57:36,962", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:57:36,963", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:57:36,964", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:57:36,964", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:57:36,965", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:57:36,966", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:57:36,966", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:57:36,967", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:57:36,967", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:57:36,967", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:57:36,969", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:57:36,970", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:57:36,971", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:57:36,972", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:57:36,974", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:57:36,974", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:instance: EC2InstanceHandler"}
{"asctime": "2025-04-29 08:57:36,975", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:volume: EC2VolumeHandler"}
{"asctime": "2025-04-29 08:57:36,975", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:snapshot: EC2SnapshotHandler"}
{"asctime": "2025-04-29 08:57:36,976", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:image: EC2AMIHandler"}
{"asctime": "2025-04-29 08:57:36,977", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ec2:security-group: EC2SecurityGroupHandler"}
{"asctime": "2025-04-29 08:57:36,977", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for s3:bucket: S3BucketHandler"}
{"asctime": "2025-04-29 08:57:36,978", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for lambda:function: LambdaFunctionHandler"}
{"asctime": "2025-04-29 08:57:36,978", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudwatch:alarm: CloudWatchAlarmHandler"}
{"asctime": "2025-04-29 08:57:36,978", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for secretsmanager:secret: SecretsManagerSecretHandler"}
{"asctime": "2025-04-29 08:57:36,979", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:topic: SNSTopicHandler"}
{"asctime": "2025-04-29 08:57:36,980", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for sns:subscription: SNSSubscriptionHandler"}
{"asctime": "2025-04-29 08:57:36,980", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for states:stateMachine: StepFunctionsStateMachineHandler"}
{"asctime": "2025-04-29 08:57:36,981", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for events:rule: EventBridgeRuleHandler"}
{"asctime": "2025-04-29 08:57:36,981", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for cloudformation:stack: CloudFormationStackHandler"}
{"asctime": "2025-04-29 08:57:36,982", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:loadbalancer: ELBLoadBalancerHandler"}
{"asctime": "2025-04-29 08:57:36,982", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:targetgroup: ELBTargetGroupHandler"}
{"asctime": "2025-04-29 08:57:36,983", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener: ELBListenerHandler"}
{"asctime": "2025-04-29 08:57:36,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for elasticloadbalancing:listener-rule: ELBListenerRuleHandler"}
{"asctime": "2025-04-29 08:57:36,984", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:parameter: SSMParameterHandler"}
{"asctime": "2025-04-29 08:57:36,985", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for ssm:document: SSMDocumentHandler"}
{"asctime": "2025-04-29 08:57:36,986", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-vault: BackupVaultHandler"}
{"asctime": "2025-04-29 08:57:36,987", "name": "aws_decom.handlers.handler_factory", "levelname": "DEBUG", "message": "Registered handler for backup:backup-plan: BackupPlanHandler"}
{"asctime": "2025-04-29 08:57:36,992", "name": "aws_decom.utils.input_manager", "levelname": "INFO", "message": "Loaded 8 ARNs from text file test_new_handlers.txt"}
{"asctime": "2025-04-29 08:57:36,995", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:57:36,995", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:57:36,996", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:57:36,997", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:57:36,997", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:57:36,998", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:58:06,330", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/test-lb/1234567890abcdef -> elasticloadbalancing:loadbalancer"}
{"asctime": "2025-04-29 08:58:06,368", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,370", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:58:06,409", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,410", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: elasticloadbalancing:loadbalancer app/test-lb/1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:58:06,440", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,450", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:58:06,482", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,482", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,489", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:58:06,489", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,489", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: elasticloadbalancing:targetgroup test-tg in us-east-1"}
{"asctime": "2025-04-29 08:58:06,546", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,548", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,572", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,579", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,579", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,579", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,582", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: elasticloadbalancing:listener app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,619", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,619", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,659", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,663", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,666", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,668", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,669", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: elasticloadbalancing:listener-rule app in us-east-1"}
{"asctime": "2025-04-29 08:58:06,709", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,714", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:58:06,749", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,749", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,755", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:58:06,755", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,755", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: ssm:parameter test-parameter in us-east-1"}
{"asctime": "2025-04-29 08:58:06,755", "name": "aws_decom.utils.arn_parser", "levelname": "INFO", "message": "Parsed generic ARN: arn:aws:ssm:us-east-1:************:document/test-document -> ssm:document"}
{"asctime": "2025-04-29 08:58:06,817", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,819", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:58:06,888", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:06,890", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: ssm:document test-document in us-east-1"}
{"asctime": "2025-04-29 08:58:06,961", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:06,963", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:58:07,009", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:07,014", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: backup:backup-vault test-vault in us-east-1"}
{"asctime": "2025-04-29 08:58:07,059", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,059", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Validating resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:58:07,099", "name": "aws_decom.core.session_manager", "levelname": "ERROR", "message": "AWS profile support-************ not found"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Failed to create session for account ************"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "INFO", "message": "Performing dry run for resource: backup:backup-plan 1234567890abcdef in us-east-1"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "ERROR", "message": "Resource not validated"}
{"asctime": "2025-04-29 08:58:07,100", "name": "aws_decom.core.resource_manager", "levelname": "WARNING", "message": "Skipping deletion of resource that failed dry run: backup:backup-plan 1234567890abcdef in us-east-1"}
