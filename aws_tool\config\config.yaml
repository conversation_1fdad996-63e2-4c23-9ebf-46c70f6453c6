# AWS Resource Decommissioning Tool Configuration

# General settings
general:
  profile_prefix: "support-"  # Prefix for AWS profile names
  default_region: "us-east-1"
  ssl_verification: true
  max_retries: 3
  retry_delay: 5  # seconds
  timeout: 60  # seconds

# Paths
paths:
  logs_dir: "logs"
  reports_dir: "reports"

# Reporting
reporting:
  formats:
    - csv
    - json
  email:
    enabled: false
    smtp_server: "smtp.example.com"
    smtp_port: 587
    sender: "<EMAIL>"
    recipients: []

# Resource handling
resources:
  # Default behavior for handling resource dependencies
  dependencies:
    auto_detach: false  # Whether to automatically detach resources
    cascade_delete: false  # Whether to delete dependent resources

  # Service-specific settings
  services:
    ec2:
      terminate_protected_instances: true
      force_snapshot_deletion: false
      force_detach_volumes: true
    s3:
      empty_buckets_before_deletion: false
    cloudformation:
      retain_resources: []  # Resources to retain when deleting stacks

# Validation
validation:
  cr_number_pattern: "^GECHG\\d{7}$"

# Security
security:
  encrypt_reports: false
  encrypt_logs: false
