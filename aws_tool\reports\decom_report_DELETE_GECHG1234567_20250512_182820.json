{"summary": {"CR_Number": "GECHG1234567", "Operation_Type": "Deletion", "Timestamp": "2025-05-12 18:28:20", "Total_Resources": 66, "Validated_Resources": 22, "Dry_Run_Success": 17, "Dry_Run_Failed": 49, "Deletion_Success": 13, "Deletion_Failed": 4, "Resources_With_Dependencies": 10}, "operation_type": "DELETE", "resources": [{"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "UAI2004829-sparks-prd-i5", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-sparks-prd-i5/********-ee03-11ea-b35a-02dbc193ed28", "Created_Date": "2020-09-03 16:32:06", "Validated": "Yes", "Validation_Message": "Stack UAI2004829-sparks-prd-i5 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack UAI2004829-sparks-prd-i5 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack UAI2004829-sparks-prd-i5 deletion initiated", "Deleted_Date": "2025-05-12 18:28:50", "Dependencies": "None", "Validation_Duration": "3.74s", "Dry_Run_Duration": "3.09s", "Deletion_Duration": "3.56s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5s-sparks-prd-UAI2004829 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2", "Deletion_Success": "Yes", "Deletion_Message": "Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-prd-UAI2004829/0f9f75b50ace9ef2", "Deleted_Date": "2025-05-12 18:30:43", "Dependencies": "registered_with generic:resource 10.222.99.115", "Validation_Duration": "4.02s", "Dry_Run_Duration": "35.22s", "Deletion_Duration": "36.31s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-prd-UAI2004829/8ba285cac7d39a9d", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5p-sparks-prd-UAI2004829 exists and can be deleted", "Dry_Run_Success": "No", "Dry_Run_Message": "Target group i5p-sparks-prd-UAI2004829 not found", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.70s", "Dry_Run_Duration": "3.52s", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-stg-UAI2004829/4dee790b9220ed70", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5p-sparks-stg-UAI2004829 exists and can be deleted", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929", "Validation_Duration": "4.34s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5s-sparks-stg-UAI2004829 exists and can be deleted", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52", "Validation_Duration": "4.75s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-npi-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5s-npi-sparks-stg-UAI2004829 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480", "Deletion_Success": "Yes", "Deletion_Message": "Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-npi-sparks-stg-UAI2004829/76ee869e0b163480", "Deleted_Date": "2025-05-12 18:34:20", "Dependencies": "registered_with generic:resource 10.222.117.130", "Validation_Duration": "4.07s", "Dry_Run_Duration": "33.67s", "Deletion_Duration": "35.94s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-npi-sparks-stg-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-npi-sparks-stg-UAI2004829/7d0c809bd9e2dc90", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5p-npi-sparks-stg-UAI2004829 exists and can be deleted", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "registered_with generic:resource 10.222.117.130, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4", "Validation_Duration": "3.82s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5p-gs-npi-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5p-gs-npi-sparks-prd-UAI2004829 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Would deregister 1 targets and delete target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb", "Deletion_Success": "Yes", "Deletion_Message": "Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-gs-npi-sparks-prd-UAI2004829/e4ad3cfa25b960bb", "Deleted_Date": "2025-05-12 18:36:49", "Dependencies": "registered_with generic:resource 10.222.99.115", "Validation_Duration": "4.10s", "Dry_Run_Duration": "33.64s", "Deletion_Duration": "34.68s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "targetgroup", "Resource_ID": "i5s-gs-npi-sparks-prd-UAI2004829", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Target group i5s-gs-npi-sparks-prd-UAI2004829 exists and can be deleted", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource has blocking dependencies: associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "registered_with generic:resource 10.222.99.115, associated_with elasticloadbalancing:loadbalancer arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4, forwards_to elasticloadbalancing:listener arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6, forwards_to elasticloadbalancing:listener-rule arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f", "Validation_Duration": "3.74s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - The load balancer name 'app/i5-sparks-prd-UAI2004829/134384004e42bdaf' cannot be longer than '32' characters", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.99s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - The load balancer name 'app/i5-sparks-stg-UAI2004829/ee1511167d12c523' cannot be longer than '32' characters", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.88s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - The load balancer name 'app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133' cannot be longer than '32' characters", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.93s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "loadbalancer", "Resource_ID": "app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - The load balancer name 'app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db' cannot be longer than '32' characters", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.88s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "test-instance-emgs-sparks-qa-aws-app-xLorRk", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:test-instance-emgs-sparks-qa-aws-app-xLorRk", "Created_Date": null, "Validated": "No", "Validation_Message": "Resource not found: Secrets Manager can't find the specified secret.", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.27s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials_for_i-05eb965ac3fd8fb23-5oODKX", "Created_Date": null, "Validated": "No", "Validation_Message": "Resource not found: Secrets Manager can't find the specified secret.", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "5.71s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-02e58313d98d848b6", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-02e58313d98d848b6", "Created_Date": null, "Validated": "No", "Validation_Message": "Security Group sg-02e58313d98d848b6 is being used by instances: i-05eb965ac3fd8fb23", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.50s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "secretsmanager", "Resource_Type": "secret", "Resource_ID": "credentials-for-i-002be9747294e8877-V87TL4", "Region": "eu-west-1", "ARN": "arn:aws:secretsmanager:eu-west-1:************:secret:credentials-for-i-002be9747294e8877-V87TL4", "Created_Date": null, "Validated": "No", "Validation_Message": "Resource not found: Secrets Manager can't find the specified secret.", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.64s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Alarm UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Alarm UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 can be deleted", "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.11s", "Dry_Run_Duration": "2.82s", "Deletion_Duration": "3.00s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Alarm UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Alarm UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 can be deleted", "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.91s", "Dry_Run_Duration": "2.99s", "Deletion_Duration": "2.81s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0199ae65cc9c2346b", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0199ae65cc9c2346b", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0199ae65cc9c2346b is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.93s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Alarm UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Alarm UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW can be deleted", "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.88s", "Dry_Run_Duration": "2.84s", "Deletion_Duration": "3.08s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-09df14aa14c7e6408", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-09df14aa14c7e6408", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-09df14aa14c7e6408 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.06s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.98s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudwatch", "Resource_Type": "alarm", "Resource_ID": "UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Region": "eu-west-1", "ARN": "arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B", "Created_Date": null, "Validated": "Yes", "Validation_Message": "Alarm UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Alarm UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B can be deleted", "Deletion_Success": "No", "Deletion_Message": "Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/********* is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B because no identity-based policy allows the cloudwatch:DeleteAlarms action", "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.92s", "Dry_Run_Duration": "2.82s", "Deletion_Duration": "2.90s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.90s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.86s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.84s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-00a54eeb559a244ba", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-00a54eeb559a244ba", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-00a54eeb559a244ba is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.09s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.86s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "UAI2004829-sparks-stg-i5", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-sparks-stg-i5/c35431d0-ec5d-11ea-aa84-06badcce5684", "Created_Date": "2020-09-01 14:16:44", "Validated": "Yes", "Validation_Message": "Stack UAI2004829-sparks-stg-i5 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack UAI2004829-sparks-stg-i5 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack UAI2004829-sparks-stg-i5 deletion initiated", "Deleted_Date": "2025-05-12 18:40:48", "Dependencies": "None", "Validation_Duration": "4.67s", "Dry_Run_Duration": "3.32s", "Deletion_Duration": "3.85s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-0c13321fab05fc3a2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-0c13321fab05fc3a2", "Created_Date": null, "Validated": "No", "Validation_Message": "Security Group sg-0c13321fab05fc3a2 is being used by network interfaces: eni-044801dafa58870a7, eni-09d2729757d9b0baa", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.54s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-03255a7c76bfd0a7c", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-03255a7c76bfd0a7c", "Created_Date": null, "Validated": "No", "Validation_Message": "Security Group sg-03255a7c76bfd0a7c is being used by network interfaces: eni-09d9fd9342a8403bb, eni-03d88784681662040", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.38s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08c1f59c7edbadcd9", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08c1f59c7edbadcd9", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-08c1f59c7edbadcd9 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.86s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.02s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "UAI2004829-npi-sparks-stg-i5", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-npi-sparks-stg-i5/8afd5a20-a376-11eb-9496-066f6230c66b", "Created_Date": "2021-04-22 14:25:10", "Validated": "Yes", "Validation_Message": "Stack UAI2004829-npi-sparks-stg-i5 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack UAI2004829-npi-sparks-stg-i5 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack UAI2004829-npi-sparks-stg-i5 deletion initiated", "Deleted_Date": "2025-05-12 18:41:37", "Dependencies": "None", "Validation_Duration": "3.39s", "Dry_Run_Duration": "2.87s", "Deletion_Duration": "3.58s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.78s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-087df90f8ba056c60", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-087df90f8ba056c60", "Created_Date": null, "Validated": "No", "Validation_Message": "Security Group sg-087df90f8ba056c60 is being used by network interfaces: eni-037112f1a1d94d95a, eni-06fb22907186a9c83", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.21s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "UAI2004829-gs-npi-sparks-prd-i5", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/UAI2004829-gs-npi-sparks-prd-i5/739afec0-a379-11eb-8760-0a1c071ebc27", "Created_Date": "2021-04-22 14:45:59", "Validated": "Yes", "Validation_Message": "Stack UAI2004829-gs-npi-sparks-prd-i5 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack UAI2004829-gs-npi-sparks-prd-i5 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack UAI2004829-gs-npi-sparks-prd-i5 deletion initiated", "Deleted_Date": "2025-05-12 18:42:11", "Dependencies": "None", "Validation_Duration": "3.43s", "Dry_Run_Duration": "2.86s", "Deletion_Duration": "3.48s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.59s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f/324b6ea53bdb69c2", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.79s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "ren-uai2004829-emgs-sparks-qa-win2019EC2", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/ren-uai2004829-emgs-sparks-qa-win2019EC2/aa1c7b80-d17b-11ee-abcc-026ee85557df", "Created_Date": "2024-02-22 12:12:36", "Validated": "Yes", "Validation_Message": "Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack ren-uai2004829-emgs-sparks-qa-win2019EC2 deletion initiated", "Deleted_Date": "2025-05-12 18:42:44", "Dependencies": "None", "Validation_Duration": "3.43s", "Dry_Run_Duration": "2.75s", "Deletion_Duration": "3.39s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.79s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f/58545befb00779d0", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.76s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "security-group", "Resource_ID": "sg-09d098800c98f744a", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:security-group/sg-09d098800c98f744a", "Created_Date": null, "Validated": "No", "Validation_Message": "Security Group sg-09d098800c98f744a is being used by network interfaces: eni-0fa7cecc085475be4, eni-0a7af7611b2943369", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "4.56s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.95s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "cloudformation", "Resource_Type": "stack", "Resource_ID": "ren-test-instance-uai2004829-emgs-sparks-ec2", "Region": "eu-west-1", "ARN": "arn:aws:cloudformation:eu-west-1:************:stack/ren-test-instance-uai2004829-emgs-sparks-ec2/bc0d1ef0-d6e7-11ee-a39b-06537f2d69af", "Created_Date": "2024-02-29 09:48:47", "Validated": "Yes", "Validation_Message": "Stack ren-test-instance-uai2004829-emgs-sparks-ec2 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Stack ren-test-instance-uai2004829-emgs-sparks-ec2 can be deleted", "Deletion_Success": "Yes", "Deletion_Message": "Stack ren-test-instance-uai2004829-emgs-sparks-ec2 deletion initiated", "Deleted_Date": "2025-05-12 18:43:31", "Dependencies": "None", "Validation_Duration": "3.34s", "Dry_Run_Duration": "2.79s", "Deletion_Duration": "3.57s"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.81s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "elasticloadbalancing", "Resource_Type": "listener-rule", "Resource_ID": "app", "Region": "eu-west-1", "ARN": "arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f", "Created_Date": null, "Validated": "No", "Validation_Message": "Error: ValidationError - 'app' is not a valid listener rule ARN", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.84s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-002be9747294e8877", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-002be9747294e8877", "Created_Date": null, "Validated": "No", "Validation_Message": "Instance i-002be9747294e8877 is already terminated", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.82s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-05eb965ac3fd8fb23", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-05eb965ac3fd8fb23", "Created_Date": null, "Validated": "No", "Validation_Message": "Instance i-05eb965ac3fd8fb23 is already shutting down", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.83s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0321aa3341012dc5d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0321aa3341012dc5d", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0321aa3341012dc5d is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.96s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08292e054f597a702", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08292e054f597a702", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-08292e054f597a702 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.91s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0f23739b81fce377d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0f23739b81fce377d", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0f23739b81fce377d is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.89s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0a61a13260c3b8461", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0a61a13260c3b8461", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0a61a13260c3b8461 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.85s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0b51ed17665aabeae", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0b51ed17665aabeae", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0b51ed17665aabeae is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.07s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-002d9c016266809f2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-002d9c016266809f2", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-002d9c016266809f2 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.85s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0cd56d66953e6c11f", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0cd56d66953e6c11f", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0cd56d66953e6c11f is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.81s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-05b68331cc9022940", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-05b68331cc9022940", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-05b68331cc9022940 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.81s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a25ca18b9d2ffbe", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a25ca18b9d2ffbe", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-08a25ca18b9d2ffbe is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.82s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-0d221edcc77bf155d", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-0d221edcc77bf155d", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-0d221edcc77bf155d is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.82s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "volume", "Resource_ID": "vol-08a697f12101a69f5", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:volume/vol-08a697f12101a69f5", "Created_Date": null, "Validated": "No", "Validation_Message": "Volume vol-08a697f12101a69f5 is in use and force_detach_volumes is not enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "2.88s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "ent-emea-energy", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-01ff028a35ce74562", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-01ff028a35ce74562", "Created_Date": null, "Validated": "No", "Validation_Message": "Instance i-01ff028a35ce74562 has termination protection enabled", "Dry_Run_Success": "No", "Dry_Run_Message": "Resource not validated", "Deletion_Success": "N/A", "Deletion_Message": null, "Deleted_Date": null, "Dependencies": "None", "Validation_Duration": "3.50s", "Dry_Run_Duration": "N/A", "Deletion_Duration": "N/A"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-0915c5900e50f84c8", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-0915c5900e50f84c8", "Created_Date": "2023-04-19 05:35:03", "Validated": "Yes", "Validation_Message": "Instance i-0915c5900e50f84c8 exists and can be terminated", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Request would have succeeded, but DryRun flag is set.", "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from running to shutting-down", "Deleted_Date": "2025-05-12 18:45:23", "Dependencies": "attached_to ec2:volume vol-08a697f12101a69f5, attached_to ec2:volume vol-0321aa3341012dc5d, attached_to ec2:volume vol-08292e054f597a702, attached_to ec2:volume vol-0d221edcc77bf155d, attached_to ec2:volume vol-002d9c016266809f2, attached_to ec2:network-interface eni-f4c533f1", "Validation_Duration": "3.57s", "Dry_Run_Duration": "2.75s", "Deletion_Duration": "3.58s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-05c4fe16dcfb7f9ad", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-05c4fe16dcfb7f9ad", "Created_Date": "2025-05-09 22:30:28", "Validated": "Yes", "Validation_Message": "Instance i-05c4fe16dcfb7f9ad exists and can be terminated", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Request would have succeeded, but DryRun flag is set.", "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from running to shutting-down", "Deleted_Date": "2025-05-12 18:45:46", "Dependencies": "attached_to ec2:volume vol-0cd56d66953e6c11f, attached_to ec2:volume vol-08a25ca18b9d2ffbe, attached_to ec2:network-interface eni-36b77a35", "Validation_Duration": "3.59s", "Dry_Run_Duration": "2.79s", "Deletion_Duration": "3.58s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "ec2", "Resource_Type": "instance", "Resource_ID": "i-0eddd32173f4a26c2", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:instance/i-0eddd32173f4a26c2", "Created_Date": "2025-05-09 22:30:28", "Validated": "Yes", "Validation_Message": "Instance i-0eddd32173f4a26c2 exists and can be terminated", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Request would have succeeded, but DryRun flag is set.", "Deletion_Success": "Yes", "Deletion_Message": "Instance state changed from running to shutting-down", "Deleted_Date": "2025-05-12 18:46:11", "Dependencies": "attached_to ec2:volume vol-0a61a13260c3b8461, attached_to ec2:volume vol-0f23739b81fce377d, attached_to ec2:network-interface eni-a67643a6", "Validation_Duration": "3.61s", "Dry_Run_Duration": "2.94s", "Deletion_Duration": "3.73s"}, {"CR_Number": "GECHG1234567", "Account_Name": "Unknown", "Account_ID": "************", "Service": "ec2", "Resource_Type": "snapshot", "Resource_ID": "snap-071322acf3e25c156", "Region": "eu-west-1", "ARN": "arn:aws:ec2:eu-west-1:************:snapshot/snap-071322acf3e25c156", "Created_Date": "2025-02-21 16:30:39", "Validated": "Yes", "Validation_Message": "Snapshot snap-071322acf3e25c156 exists and can be deleted", "Dry_Run_Success": "Yes", "Dry_Run_Message": "Dry run successful: Request would have succeeded, but DryRun flag is set.", "Deletion_Success": "Yes", "Deletion_Message": "Snapshot snap-071322acf3e25c156 deleted successfully", "Deleted_Date": "2025-05-12 18:46:36", "Dependencies": "None", "Validation_Duration": "4.80s", "Dry_Run_Duration": "2.72s", "Deletion_Duration": "4.80s"}]}