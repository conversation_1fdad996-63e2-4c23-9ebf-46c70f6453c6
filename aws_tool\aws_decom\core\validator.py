"""
Validation utilities for AWS Resource Decommissioning Tool.
"""

import re
import logging
from typing import Tuple, List

from ..core.config_manager import ConfigManager
from ..utils.arn_parser import ArnParser

class Validator:
    """
    Validates inputs for AWS resource decommissioning.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the validator.
        
        Args:
            config_manager: Configuration manager instance.
        """
        self.logger = logging.getLogger(__name__)
        self.config = config_manager
        self.arn_parser = ArnParser()
    
    def validate_cr_number(self, cr_number: str) -> Tuple[bool, str]:
        """
        Validate a change request number.
        
        Args:
            cr_number: Change request number to validate.
            
        Returns:
            Tuple of (valid, message).
        """
        # Get pattern from config
        pattern = self.config.get("validation.cr_number_pattern", "^GECHG\\d{7}$")
        
        # Validate against pattern
        if re.match(pattern, cr_number):
            return True, f"Valid CR number: {cr_number}"
        else:
            return False, f"Invalid CR number: {cr_number}. Must match pattern: {pattern}"
    
    def validate_arns(self, arns: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate a list of ARNs.
        
        Args:
            arns: List of ARNs to validate.
            
        Returns:
            Tuple of (valid_arns, invalid_arns).
        """
        valid_arns = []
        invalid_arns = []
        
        for arn in arns:
            if self.arn_parser.is_valid_arn(arn):
                valid_arns.append(arn)
            else:
                invalid_arns.append(arn)
                self.logger.warning(f"Invalid ARN: {arn}")
        
        return valid_arns, invalid_arns
    
    def validate_supported_resource_types(self, arns: List[str], supported_types: List[str]) -> Tuple[List[str], List[str]]:
        """
        Validate that ARNs are of supported resource types.
        
        Args:
            arns: List of ARNs to validate.
            supported_types: List of supported resource types.
            
        Returns:
            Tuple of (supported_arns, unsupported_arns).
        """
        supported_arns = []
        unsupported_arns = []
        
        for arn in arns:
            parsed_arn = self.arn_parser.parse_arn(arn)
            resource_type_key = f"{parsed_arn['service']}:{parsed_arn['resource_type']}"
            
            if resource_type_key in supported_types:
                supported_arns.append(arn)
            else:
                unsupported_arns.append(arn)
                self.logger.warning(f"Unsupported resource type: {resource_type_key} for ARN: {arn}")
        
        return supported_arns, unsupported_arns
