# Logging configuration for AWS Resource Decommissioning Tool

version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  json:
    format: "%(asctime)s %(name)s %(levelname)s %(message)s"
    class: pythonjsonlogger.jsonlogger.JsonFormatter

handlers:
  # Disabled console handler to suppress logs in terminal
  console:
    class: logging.NullHandler
    level: INFO

  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: json
    filename: logs/aws_decom.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

loggers:
  aws_decom:
    level: DEBUG
    handlers: [file, error_file]
    propagate: false

  # Third-party libraries
  boto3:
    level: WARNING
    handlers: [file]
    propagate: false

  botocore:
    level: WARNING
    handlers: [file]
    propagate: false

root:
  level: INFO
  handlers: [file]
  propagate: false
