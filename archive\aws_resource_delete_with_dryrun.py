import boto3
import csv
import os
import re
import sys
import time
import logging
import certifi
import warnings
import concurrent.futures
from datetime import datetime
from botocore.exceptions import ClientError, NoCredentialsError
from colorama import init, Fore, Style

# Environment and warning config
warnings.filterwarnings('ignore')
os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
init(autoreset=True)
now_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
# Constants
INPUT_CSV = 'input.csv'
DRY_RUN_REPORT = f'dry_run_summary-{now_time}.csv'
DELETE_REPORT = f'deletion_summary-{now_time}.csv'
LOG_FILE = f'aws_cleanup-{now_time}.log'
ROLE_REGION = 'us-east-1'  # Change if needed

# Logger setup
logging.basicConfig(
    filename=LOG_FILE,
    filemode='a',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Output headers
CSV_HEADERS = [
    'Account_Name', 'Account_ID', 'Resource_Type', 'Resource_ID',
    'Launch/Created Date', 'Deleted Date', 'Status', 'Error', 'Duration(sec)'
]

# Store reports
dry_run_rows = []
deletion_rows = []

# Parse AWS ARN into components
def parse_arn(arn):
    parts = arn.split(':')
    if len(parts) < 6:
        return None
    service = parts[2]
    region = parts[3]
    account_id = parts[4]
    resource = parts[5]

    resource_type, resource_id = None, None
    if '/' in resource:
        tokens = resource.split('/')
        if len(tokens) >= 2:
            resource_type, resource_id = tokens[0], '/'.join(tokens[1:])
        else:
            resource_id = tokens[0]
    elif ':' in resource:
        tokens = resource.split(':')
        if len(tokens) >= 2:
            resource_type, resource_id = tokens[0], ':'.join(tokens[1:])
        else:
            resource_id = tokens[0]
    else:
        resource_id = resource
        resource_type = 'unknown'

    return {
        'arn': arn,
        'service': service,
        'region': region,
        'account_id': account_id,
        'resource_type': resource_type.lower(),
        'resource_id': resource_id
    }

# Get boto3 session from account ID
def get_session(account_id):
    profile_name = f"support-{account_id}"
    try:
        session = boto3.session.Session(profile_name=profile_name, region_name=ROLE_REGION)
        return session
    except Exception as e:
        logging.error(f"Failed to create session for {profile_name}: {e}")
        return None

# Get account alias
def get_account_name(session):
    try:
        iam = session.client('iam', verify=False)
        aliases = iam.list_account_aliases().get('AccountAliases', [])
        return aliases[0] if aliases else 'Unknown'
    except Exception as e:
        logging.warning(f"Unable to fetch account alias: {e}")
        return 'Unknown'

# Delete objects in an S3 bucket
def delete_s3_objects(s3, bucket_name):
    try:
        objects = s3.list_objects_v2(Bucket=bucket_name)
        if 'Contents' in objects:
            for obj in objects['Contents']:
                s3.delete_object(Bucket=bucket_name, Key=obj['Key'])
    except Exception as e:
        logging.error(f"Error deleting objects in {bucket_name}: {e}")

# Dry-run or delete operation
def process_resource(session, account_id, account_name, details, dry_run=True):
    start_time = time.time()
    service = details['service']
    region = details['region']
    resource_type = details['resource_type']
    resource_id = details['resource_id']
    arn = details['arn']
    status = 'Success'
    error = ''
    created_date = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    deleted_date = 'Dry-Run' if dry_run else datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')

    try:
        if service == 'ec2':
            ec2 = session.client('ec2', region_name=region, verify=False)
            if resource_type == 'instance':
                ec2.modify_instance_attribute(InstanceId=resource_id, DisableApiTermination={'Value': False})
                if dry_run:
                    ec2.describe_instances(InstanceIds=[resource_id], DryRun=True)
                else:
                    ec2.terminate_instances(InstanceIds=[resource_id])
            elif resource_type == 'volume':
                if dry_run:
                    ec2.describe_volumes(VolumeIds=[resource_id], DryRun=True)
                else:
                    ec2.delete_volume(VolumeId=resource_id)
            elif resource_type == 'snapshot':
                if dry_run:
                    ec2.describe_snapshots(SnapshotIds=[resource_id], DryRun=True)
                else:
                    ec2.delete_snapshot(SnapshotId=resource_id)

        elif service == 'lambda' and resource_type == 'function':
            client = session.client('lambda', region_name=region, verify=False)
            if dry_run:
                client.get_function(FunctionName=resource_id)
            else:
                client.delete_function(FunctionName=resource_id)

        elif service == 'events' and resource_type == 'rule':
            client = session.client('events', region_name=region, verify=False)
            if dry_run:
                client.describe_rule(Name=resource_id)
            else:
                client.delete_rule(Name=resource_id, Force=True)

        elif service == 'cloudformation' and resource_type == 'stack':
            client = session.client('cloudformation', region_name=region, verify=False)
            if dry_run:
                client.describe_stacks(StackName=resource_id)
            else:
                client.delete_stack(StackName=resource_id)

        elif service == 'backup':
            client = session.client('backup', region_name=region, verify=False)
            if 'vault' in resource_type:
                if dry_run:
                    client.describe_backup_vault(BackupVaultName=resource_id)
                else:
                    client.delete_backup_vault(BackupVaultName=resource_id)
            elif 'plan' in resource_type:
                if dry_run:
                    client.get_backup_plan(BackupPlanId=resource_id)
                else:
                    client.delete_backup_plan(BackupPlanId=resource_id)

        elif service == 'secretsmanager':
            client = session.client('secretsmanager', region_name=region, verify=False)
            if dry_run:
                client.describe_secret(SecretId=resource_id)
            else:
                client.delete_secret(SecretId=resource_id, ForceDeleteWithoutRecovery=True)

        elif service == 'ssm' and resource_type == 'parameter':
            client = session.client('ssm', region_name=region, verify=False)
            if dry_run:
                client.get_parameter(Name=resource_id)
            else:
                client.delete_parameter(Name=resource_id)

        elif service == 'sns':
            client = session.client('sns', region_name=region, verify=False)
            if resource_type == 'topic':
                if dry_run:
                    client.get_topic_attributes(TopicArn=arn)
                else:
                    client.delete_topic(TopicArn=arn)
            elif resource_type == 'subscription':
                if dry_run:
                    client.get_subscription_attributes(SubscriptionArn=arn)
                else:
                    client.unsubscribe(SubscriptionArn=arn)

        elif service == 'elasticloadbalancing':
            client = session.client('elbv2', region_name=region, verify=False)
            if resource_type == 'loadbalancer':
                if dry_run:
                    client.describe_load_balancers(LoadBalancerArns=[arn])
                else:
                    client.delete_load_balancer(LoadBalancerArn=arn)
            elif resource_type == 'targetgroup':
                if dry_run:
                    client.describe_target_groups(TargetGroupArns=[arn])
                else:
                    client.delete_target_group(TargetGroupArn=arn)
            elif resource_type == 'listener':
                if dry_run:
                    client.describe_listeners(ListenerArns=[arn])
                else:
                    client.delete_listener(ListenerArn=arn)
            elif resource_type == 'listener-rule':
                if dry_run:
                    client.describe_rules(RuleArns=[arn])
                else:
                    client.delete_rule(RuleArn=arn)

        elif service == 's3':
            s3 = session.client('s3', region_name=region, verify=False)
            if dry_run:
                s3.head_bucket(Bucket=resource_id)
            else:
                delete_s3_objects(s3, resource_id)
                s3.delete_bucket(Bucket=resource_id)

        else:
            status = 'Unknown'
            error = f"Unsupported service/resource: {service}:{resource_type}"
            logging.warning(f"Unknown: {arn}")

    except ClientError as ce:
        status = 'Error'
        error = str(ce)
        logging.error(f"ClientError for {arn}: {error}")
    except Exception as e:
        status = 'Error'
        error = str(e)
        logging.error(f"General error for {arn}: {error}")

    duration = round(time.time() - start_time, 2)
    row = [
        account_name, account_id, resource_type, resource_id,
        created_date, deleted_date, status, error, str(duration)  # Ensure duration is a string
    ]

    if dry_run:
        dry_run_rows.append(row)
    else:
        deletion_rows.append(row)

    color = Fore.GREEN if status == 'Success' else Fore.YELLOW if status == 'Unknown' else Fore.RED
    print(color + ','.join(row))  # Print colorized row



# Main execution
def main(dry_run=True):
    if not os.path.exists(INPUT_CSV):
        print(f"{INPUT_CSV} not found!")
        return

    with open(INPUT_CSV, 'r') as f:
        reader = csv.reader(f)
        resources = []
        for row in reader:
            if not row:
                continue
            arn = row[0].strip()
            parsed = parse_arn(arn)
            if not parsed:
                logging.warning(f"Invalid ARN skipped: {arn}")
                continue

            session = get_session(parsed['account_id'])
            if not session:
                continue

            account_name = get_account_name(session)
            resources.append((session, parsed['account_id'], account_name, parsed))

    # Using ThreadPoolExecutor to run deletions concurrently
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for resource in resources:
            executor.submit(process_resource, *resource, dry_run=dry_run)

    # Write reports
    with open(DRY_RUN_REPORT if dry_run else DELETE_REPORT, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(CSV_HEADERS)
        writer.writerows(dry_run_rows if dry_run else deletion_rows)

    logging.info("Cleanup process completed.")

if __name__ == "__main__":
    main(dry_run=True)  # Set dry_run=False for actual deletion
