"""
Resource models for AWS Resource Decommissioning Tool.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime

@dataclass
class ResourceDependency:
    """
    Represents a dependency between AWS resources.
    """
    resource_type: str
    resource_id: str
    relationship: str  # e.g., "attached_to", "contains", "references"
    blocking: bool = True  # Whether this dependency blocks deletion

    def __str__(self) -> str:
        """String representation of the dependency."""
        return f"{self.relationship} {self.resource_type} {self.resource_id}"

@dataclass
class Resource:
    """
    Represents an AWS resource to be decommissioned.
    """
    # Resource identification
    service: str
    resource_type: str
    resource_id: str
    region: str
    account_id: str
    full_arn: str

    # Resource metadata
    created_date: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)

    # Dependencies
    dependencies: List[ResourceDependency] = field(default_factory=list)

    # Operation status
    validated: bool = False
    validation_message: Optional[str] = None

    dry_run_success: Optional[bool] = None
    dry_run_message: Optional[str] = None

    deletion_success: Optional[bool] = None
    deletion_message: Optional[str] = None
    deleted_date: Optional[str] = None

    # Performance metrics
    validation_duration: Optional[float] = None
    dry_run_duration: Optional[float] = None
    deletion_duration: Optional[float] = None

    # Change management
    cr_number: Optional[str] = None

    def __str__(self) -> str:
        """String representation of the resource."""
        return f"{self.service}:{self.resource_type} {self.resource_id} in {self.region}"

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the resource to a dictionary for reporting.

        Returns:
            Dict representation of the resource.
        """
        # Get account name from tags (populated by resource manager)
        account_name = self.tags.get("Account_Name", "Unknown")

        return {
            "CR_Number": self.cr_number,
            "Account_Name": account_name,
            "Account_ID": self.account_id,
            "Service": self.service,
            "Resource_Type": self.resource_type,
            "Resource_ID": self.resource_id,
            "Region": self.region,
            "ARN": self.full_arn,
            "Created_Date": self.created_date,
            "Validated": "Yes" if self.validated else "No",
            "Validation_Message": self.validation_message,
            "Dry_Run_Success": "Yes" if self.dry_run_success else "No" if self.dry_run_success is False else "N/A",
            "Dry_Run_Message": self.dry_run_message,
            "Deletion_Success": "Yes" if self.deletion_success else "No" if self.deletion_success is False else "N/A",
            "Deletion_Message": self.deletion_message,
            "Deleted_Date": self.deleted_date,
            "Dependencies": ", ".join(str(dep) for dep in self.dependencies) if self.dependencies else "None",
            "Validation_Duration": f"{self.validation_duration:.2f}s" if self.validation_duration else "N/A",
            "Dry_Run_Duration": f"{self.dry_run_duration:.2f}s" if self.dry_run_duration else "N/A",
            "Deletion_Duration": f"{self.deletion_duration:.2f}s" if self.deletion_duration else "N/A",
        }

    def has_blocking_dependencies(self) -> bool:
        """
        Check if the resource has blocking dependencies.

        Returns:
            True if the resource has blocking dependencies, False otherwise.
        """
        return any(dep.blocking for dep in self.dependencies)
