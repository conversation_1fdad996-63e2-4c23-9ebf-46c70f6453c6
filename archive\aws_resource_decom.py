import csv
import time
import boto3
import argparse
from colorama import Fore
from tabulate import tabulate

# Set up boto3 session for AWS account
session = boto3.Session(profile_name="support-{account_id}")
sts_client = session.client('sts')

# Fetch Account Name and Account ID from AWS using sts.get_caller_identity
def get_account_info():
    try:
        identity = sts_client.get_caller_identity()
        account_id = identity['Account']
        
        # Fetching account alias (name) using the AWS SDK
        iam_client = session.client('iam')
        aliases = iam_client.list_account_aliases()
        account_name = aliases['AccountAliases'][0] if aliases['AccountAliases'] else 'Unknown'

        return account_name, account_id
    except Exception as e:
        print(f"Error fetching account info: {e}")
        return 'Unknown', 'Unknown'


# Read resources from input CSV file
def read_resources_from_csv(input_file):
    resources = []
    with open(input_file, mode='r') as file:
        reader = csv.reader(file)
        for row in reader:
            if row:
                resources.append({'arn': row[0]})
    return resources


# Color mapping for each service
SERVICE_COLORS = {
    'ec2': Fore.GREEN,
    's3': Fore.CYAN,
    'lambda': Fore.MAGENTA,
    'secretsmanager': Fore.YELLOW,
    'states': Fore.RED,
    'events': Fore.BLUE,
    'cloudwatch': Fore.WHITE,
    'sns': Fore.YELLOW,
    'elasticloadbalancing': Fore.LIGHTYELLOW_EX,
    'cloudformation': Fore.LIGHTGREEN_EX,
    'ssm': Fore.LIGHTBLUE_EX,
    'backup': Fore.LIGHTCYAN_EX
}

CSV_HEADERS = [
    'Account_Name', 'Account_ID', 'Service', 'Resource_Type', 'Resource_ID',
    'Launch/Created Date', 'Deleted Date', 'Status', 'Error', 'Duration(sec)'
]


def extract_service_and_resource_type(resource):
    arn = resource.get('arn', '')
    parts = arn.split(':')

    service = parts[2] if len(parts) > 2 else 'unknown'

    # Deriving resource type from service
    if service == 'ec2':
        if 'instance/' in arn:
            resource_type = 'instance'
            resource_id = arn.split(':')[5].split('/')[1]
        elif 'volume/' in arn:
            resource_type = 'volume'
            resource_id = arn.split(':')[5].split('/')[1]
        elif 'snapshot/' in arn:
            resource_type = 'snapshot'
            resource_id = arn.split(':')[5].split('/')[1]
        else:
            resource_type = 'unknown'
            resource_id = arn

    elif service == 's3':
        resource_type = 'bucket'
        resource_id = arn.split(':')[5]

    elif service == 'lambda':
        resource_type = 'function'
        resource_id = arn.split(':')[6]

    elif service == 'secretsmanager':
        resource_type = 'secret'
        resource_id = arn.split(':')[5]

    elif service == 'states':
        resource_type = 'stateMachine'
        resource_id = arn.split(':')[6]

    elif service == 'events':
        resource_type = 'rule'
        resource_id = arn.split(':')[6]

    elif service == 'cloudwatch':
        resource_type = 'alarm'
        resource_id = arn.split(':')[5]

    elif service == 'sns':
        resource_type = 'topic'
        resource_id = arn.split(':')[5]

    elif service == 'elasticloadbalancing':
        if 'targetgroup/' in arn:
            resource_type = 'target-group'
            resource_id = arn.split(':')[5].split('/')[1]
        elif 'listener/' in arn:
            resource_type = 'listener'
            resource_id = arn.split(':')[5].split('/')[1]
        else:
            resource_type = 'elb-other'
            resource_id = arn

    elif service == 'cloudformation':
        resource_type = 'stack'
        resource_id = arn.split(':')[5]

    elif service == 'ssm':
        resource_type = 'parameter'
        resource_id = arn.split(':')[5]

    elif service == 'backup':
        if 'backup-plan/' in arn:
            resource_type = 'backup-plan'
            resource_id = arn.split(':')[5].split('/')[1]
        elif 'backup-vault/' in arn:
            resource_type = 'backup-vault'
            resource_id = arn.split(':')[5].split('/')[1]
        else:
            resource_type = 'backup-other'
            resource_id = arn

    else:
        resource_type = 'unknown'
        resource_id = arn

    return service, resource_type, resource_id


def fetch_resource_created_date(service, resource_id):
    try:
        if service == 'ec2':
            ec2_client = session.client('ec2')
            instances = ec2_client.describe_instances(InstanceIds=[resource_id])
            created_date = instances['Reservations'][0]['Instances'][0]['LaunchTime'].strftime('%Y-%m-%dT%H:%M:%SZ')
        elif service == 'lambda':
            lambda_client = session.client('lambda')
            func = lambda_client.get_function(FunctionName=resource_id)
            created_date = func['Configuration']['LastModified']
        elif service == 's3':
            s3_client = session.client('s3')
            head = s3_client.head_bucket(Bucket=resource_id)
            created_date = head['ResponseMetadata']['HTTPHeaders']['date']
        else:
            created_date = 'N/A'  # Default fallback
    except Exception as e:
        print(f"Error fetching created date for {resource_id}: {e}")
        created_date = 'N/A'
    return created_date


def write_csv(file_name, data):
    with open(file_name, mode='a', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(data)


def process_resource_for_deletion(account_name, account_id, resource, dry_run=True):
    service, resource_type, resource_id = extract_service_and_resource_type(resource)
    
    # Fetch launch date (Created Date)
    launch_date = fetch_resource_created_date(service, resource_id)
    
    # Perform dry-run or deletion operation
    if dry_run:
        status = 'Dry-Run'
        deleted_date = 'N/A'
        error = 'None'
        duration = 0
    else:
        # Actual deletion logic here (e.g., using Boto3 to delete the resource)
        try:
            # Simulate deletion operation (you can call actual boto3 delete functions here)
            time.sleep(1)  # Simulating time taken for deletion
            deleted_date = time.strftime('%Y-%m-%dT%H:%M:%SZ')
            status = 'Deleted'
            error = 'None'
            duration = 1  # Example duration
        except Exception as e:
            deleted_date = 'N/A'
            status = 'Failed'
            error = str(e)
            duration = 0

    # Write CSV for dry-run or actual deletion
    data = [
        account_name, account_id, service, resource_type, resource_id, 
        launch_date, deleted_date, status, error, duration
    ]
    return data


def print_help():
    print("""
    Usage: script.py [options]

    Supported services and resource types:
    1. EC2
        - instance
        - volume
        - snapshot
    2. S3
        - bucket
    3. Lambda
        - function
    4. Secrets Manager
        - secret
    5. Step Functions
        - stateMachine
    6. EventBridge
        - rule
    7. CloudWatch
        - alarm
    8. SNS
        - topic
    9. Elastic Load Balancing
        - target-group
        - listener
    10. CloudFormation
        - stack
    11. SSM
        - parameter
    12. Backup
        - backup-plan
        - backup-vault

    Options:
    --help              Show this help message
    --input-file        Specify the input CSV file containing ARNs (default: resources.csv)
    --dry-run           Perform a dry-run without deleting resources
    --delete            Perform actual deletion of resources

    Example: 
          --input-file resources.csv --dry-run # For Simulation.
          --input-file resources.csv --delete  # for actual delete of resource.
    """)


def main():
    parser = argparse.ArgumentParser(description='AWS Resource Cleanup Script')
    parser.add_argument('--help', action='help', help='Show this help message')
    parser.add_argument('--input-file', type=str, default='resources.csv', help='Input CSV file containing ARNs')
    parser.add_argument('--dry-run', action='store_true', help='Perform a dry-run (no deletion)')
    parser.add_argument('--delete', action='store_true', help='Perform actual deletion')

    args = parser.parse_args()

    # Fetch account info
    account_name, account_id = get_account_info()

    # Read resources from input CSV
    resources = read_resources_from_csv(args.input_file)

    # Create and write to CSVs
    dry_run_file = 'dry_run_output.csv'
    deletion_file = 'deletion_output.csv'
    
    # Writing headers to CSV files (for both dry-run and actual deletion)
    write_csv(dry_run_file, CSV_HEADERS)
    write_csv(deletion_file, CSV_HEADERS)
    
    # Process each resource for dry-run or actual deletion
    for resource in resources:
        if args.dry_run:
            dry_run_data = process_resource_for_deletion(account_name, account_id, resource, dry_run=True)
            write_csv(dry_run_file, dry_run_data)
        elif args.delete:
            deletion_data = process_resource_for_deletion(account_name, account_id, resource, dry_run=False)
            write_csv(deletion_file, deletion_data)

    print("Dry-run and deletion CSVs have been generated!")


if __name__ == "__main__":
    main()
