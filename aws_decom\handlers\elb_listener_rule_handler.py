"""
ELB Listener Rule handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class ELBListenerRuleHandler(BaseResourceHandler):
    """
    Handler for Elastic Load Balancer Listener Rules.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the ELB Listener Rule handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that a listener rule exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract rule ARN from resource ID
            rule_id = resource.resource_id
            
            # Check if rule exists
            response = elbv2.describe_rules(RuleArns=[rule_id])
            
            if not response.get('Rules'):
                return False, f"Listener rule {rule_id} not found"
            
            # Check if it's a default rule (cannot be deleted)
            rule = response['Rules'][0]
            if rule.get('IsDefault', False):
                return False, f"Listener rule {rule_id} is a default rule and cannot be deleted"
            
            return True, f"Listener rule {rule_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating listener rule")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent deletion of a listener rule.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract rule ARN from resource ID
            rule_id = resource.resource_id
            
            # Get rule details
            response = elbv2.describe_rules(RuleArns=[rule_id])
            
            if not response.get('Rules'):
                return dependencies
            
            rule = response['Rules'][0]
            
            # Get the listener this rule belongs to
            listener_arn = rule.get('ListenerArn')
            if listener_arn:
                dependencies.append(ResourceDependency(
                    resource_type="elasticloadbalancing:listener",
                    resource_id=listener_arn,
                    relationship="belongs_to",
                    blocking=True  # Blocking because the listener must exist
                ))
            
            # Check for target groups used by this rule
            for action in rule.get('Actions', []):
                if action.get('Type') == 'forward' and 'TargetGroupArn' in action:
                    dependencies.append(ResourceDependency(
                        resource_type="elasticloadbalancing:targetgroup",
                        resource_id=action['TargetGroupArn'],
                        relationship="forwards_to",
                        blocking=False  # Not blocking because the rule can be deleted regardless
                    ))
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for listener rule {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of listener rule deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract rule ARN from resource ID
            rule_id = resource.resource_id
            
            # Check if rule exists
            response = elbv2.describe_rules(RuleArns=[rule_id])
            
            if not response.get('Rules'):
                return False, f"Listener rule {rule_id} not found"
            
            # Check if it's a default rule (cannot be deleted)
            rule = response['Rules'][0]
            if rule.get('IsDefault', False):
                return False, f"Listener rule {rule_id} is a default rule and cannot be deleted"
            
            return True, f"Dry run successful: Would delete listener rule {rule_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of listener rule")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete a listener rule.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract rule ARN from resource ID
            rule_id = resource.resource_id
            
            # Check if rule exists
            response = elbv2.describe_rules(RuleArns=[rule_id])
            
            if not response.get('Rules'):
                return False, f"Listener rule {rule_id} not found"
            
            # Check if it's a default rule (cannot be deleted)
            rule = response['Rules'][0]
            if rule.get('IsDefault', False):
                return False, f"Listener rule {rule_id} is a default rule and cannot be deleted"
            
            # Delete the rule
            elbv2.delete_rule(RuleArn=rule_id)
            
            return True, f"Successfully deleted listener rule {rule_id}"
            
        except ClientError as e:
            return self.handle_client_error(e, "deletion of listener rule")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation date of a listener rule.
        
        Args:
            resource: Resource to get creation date for.
            session: boto3 session.
            
        Returns:
            Creation date as string or None if not available.
        """
        # Listener rules don't have a creation date available through the API
        return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for a listener rule.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            elbv2 = session.client('elbv2', region_name=resource.region)
            
            # Extract rule ARN from resource ID
            rule_id = resource.resource_id
            
            # Get tags for the rule
            response = elbv2.describe_tags(ResourceArns=[rule_id])
            
            tags = {}
            for tag_desc in response.get('TagDescriptions', []):
                if tag_desc.get('ResourceArn') == rule_id:
                    for tag in tag_desc.get('Tags', []):
                        tags[tag['Key']] = tag['Value']
            
            return tags
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for listener rule {resource.resource_id}: {e}")
            return {}
