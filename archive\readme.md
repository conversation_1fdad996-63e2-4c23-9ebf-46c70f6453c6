# AWS Cleanup CLI

This is a command-line interface (CLI) tool to help you **clean up** AWS resources by their ARNs (Amazon Resource Names). It can simulate (dry-run) or perform actual deletions of various AWS resources like EC2 instances, Lambda functions, S3 buckets, and more.

## Prerequisites

Before you run the script, ensure you have the following:

- AWS CLI configured with **multiple profiles** (one per account) using `aws configure`.
- Python 3.6+ installed.
- Required Python libraries:
  - boto3
  - pandas
  - certifi
  - logging
  - re
  - argparse

You can install the dependencies via pip:
```bash
    pip install boto3 pandas certifi
```
## Usage
Prepare Input File:
  The input file should be a CSV containing only ARNs (one ARN per line). Example:
 ```csv
 arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0
 arn:aws:s3:::mybucket
 ```

## Run the Script: 
Execute the script via the command line. You can specify the input file and whether to do a dry-run or actual deletion:

```bash

python aws_cleanup_cli.py --file path_to_arn_file.csv --dry-run
```
To actually delete the resources, run the command without --dry-run:
```bash
python aws_cleanup_cli.py --file path_to_arn_file.csv
```
## Confirmation: If you're running the script to delete resources, it will ask you to confirm before proceeding. Type YES to proceed with deletion, or NO to cancel.

## Supported AWS Resources
 * EC2: Instances, Volumes, Snapshots, AMIs

 * Lambda Functions

 * SNS Topics

 * S3 Buckets

 * CloudWatch Alarms

 * Secrets Manager Secrets

 * Step Functions State Machines

 * EventBridge Rules

 * CloudFormation Stacks

 * ELB Listeners, Target Groups, Listener Rules

 * SSM Parameters

 * Backup Plans and Vaults

 * SNS Subscriptions

## Logging
The script will create a logs/cleanup.log file where it will log the actions performed. It will also save a CSV summary (cleanup_summary_<timestamp>.csv) of the resources that were processed, showing whether they were deleted or failed.