CR_Number,Account_Name,Account_ID,Service,Resource_Type,Resource_ID,Region,ARN,Created_Date,Validated,Validation_Message,Dry_Run_Success,Dry_Run_Message,Deletion_Success,Deletion_Message,Deleted_Date,Dependencies,Validation_Duration,Dry_Run_Duration,Deletion_Duration
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,targetgroup,i5p-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-prd-UAI2004829/8ba285cac7d39a9d,,Yes,Validation skipped,N/A,,No,Target group i5p-sparks-prd-UAI2004829 not found,,None,N/A,N/A,3.12s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,targetgroup,i5p-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-sparks-stg-UAI2004829/4dee790b9220ed70,,Yes,Validation skipped,N/A,,No,Target group i5p-sparks-stg-UAI2004829 not found,,None,N/A,N/A,3.00s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,targetgroup,i5s-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-sparks-stg-UAI2004829/1e3aebb71b09c947,,Yes,Validation skipped,N/A,,No,Target group i5s-sparks-stg-UAI2004829 not found,,None,N/A,N/A,3.28s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,targetgroup,i5p-npi-sparks-stg-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5p-npi-sparks-stg-UAI2004829/7d0c809bd9e2dc90,,Yes,Validation skipped,N/A,,No,Target group i5p-npi-sparks-stg-UAI2004829 not found,,None,N/A,N/A,3.19s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,targetgroup,i5s-gs-npi-sparks-prd-UAI2004829,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680,,Yes,Validation skipped,N/A,,Yes,Successfully deleted target group arn:aws:elasticloadbalancing:eu-west-1:************:targetgroup/i5s-gs-npi-sparks-prd-UAI2004829/86f6370810849680,2025-05-12 19:53:48,None,N/A,N/A,29.95s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-sparks-prd-UAI2004829/134384004e42bdaf,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-prd-UAI2004829/134384004e42bdaf,,Yes,Validation skipped,N/A,,No,Error: LoadBalancerNotFound - One or more load balancers not found,,None,N/A,N/A,3.01s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-sparks-stg-UAI2004829/ee1511167d12c523,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-sparks-stg-UAI2004829/ee1511167d12c523,,Yes,Validation skipped,N/A,,No,Error: LoadBalancerNotFound - One or more load balancers not found,,None,N/A,N/A,3.04s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133,,Yes,Validation skipped,N/A,,No,Error: LoadBalancerNotFound - One or more load balancers not found,,None,N/A,N/A,2.85s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,loadbalancer,app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db,,Yes,Validation skipped,N/A,,No,Error: LoadBalancerNotFound - One or more load balancers not found,,None,N/A,N/A,2.99s
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,test-instance-emgs-sparks-qa-aws-app-xLorRk,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:test-instance-emgs-sparks-qa-aws-app-xLorRk,,Yes,Validation skipped,N/A,,No,Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: secretsmanager:DeleteSecret on resource: test-instance-emgs-sparks-qa-aws-app-xLorRk because no identity-based policy allows the secretsmanager:DeleteSecret action,,None,N/A,N/A,2.92s
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,credentials_for_i-05eb965ac3fd8fb23-5oODKX,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:credentials_for_i-05eb965ac3fd8fb23-5oODKX,,Yes,Validation skipped,N/A,,No,Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials_for_i-05eb965ac3fd8fb23-5oODKX because no identity-based policy allows the secretsmanager:DeleteSecret action,,None,N/A,N/A,4.50s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-02e58313d98d848b6,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-02e58313d98d848b6,,Yes,Validation skipped,N/A,,No,Error: InvalidGroup.NotFound - The security group 'sg-02e58313d98d848b6' does not exist,,None,N/A,N/A,3.54s
GECHG1234567,ent-emea-energy,************,secretsmanager,secret,credentials-for-i-002be9747294e8877-V87TL4,eu-west-1,arn:aws:secretsmanager:eu-west-1:************:secret:credentials-for-i-002be9747294e8877-V87TL4,,Yes,Validation skipped,N/A,,No,Error: AccessDeniedException - User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: secretsmanager:DeleteSecret on resource: credentials-for-i-002be9747294e8877-V87TL4 because no identity-based policy allows the secretsmanager:DeleteSecret action,,None,N/A,N/A,2.95s
GECHG1234567,ent-emea-energy,************,cloudwatch,alarm,UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7,,Yes,Validation skipped,N/A,,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-stg-i5-LoadBalancerAlarm-8NM12N8G8QC7 because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,N/A,N/A,3.36s
GECHG1234567,ent-emea-energy,************,cloudwatch,alarm,UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5,,Yes,Validation skipped,N/A,,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-sparks-prd-i5-LoadBalancerAlarm-18SFQ5V518QT5 because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,N/A,N/A,3.22s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0199ae65cc9c2346b,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0199ae65cc9c2346b,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0199ae65cc9c2346b' does not exist.,,None,N/A,N/A,3.29s
GECHG1234567,ent-emea-energy,************,cloudwatch,alarm,UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW,,Yes,Validation skipped,N/A,,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-npi-sparks-stg-i5-LoadBalancerAlarm-1PUL7G9K7IGIW because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,N/A,N/A,3.38s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-09df14aa14c7e6408,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-09df14aa14c7e6408,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-09df14aa14c7e6408' does not exist.,,None,N/A,N/A,3.38s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.32s
GECHG1234567,ent-emea-energy,************,cloudwatch,alarm,UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B,eu-west-1,arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B,,Yes,Validation skipped,N/A,,No,Permission denied: User: arn:aws:sts::************:assumed-role/AWSReservedSSO_support_4666bc1d74cf499c/503282581 is not authorized to perform: cloudwatch:DeleteAlarms on resource: arn:aws:cloudwatch:eu-west-1:************:alarm:UAI2004829-gs-npi-sparks-prd-i5-LoadBalancerAlarm-QAB0ZCZ3VS2B because no identity-based policy allows the cloudwatch:DeleteAlarms action,,None,N/A,N/A,3.19s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.36s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.29s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.34s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-00a54eeb559a244ba,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-00a54eeb559a244ba,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-00a54eeb559a244ba' does not exist.,,None,N/A,N/A,3.46s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.34s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-0c13321fab05fc3a2,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-0c13321fab05fc3a2,,Yes,Validation skipped,N/A,,No,Error: InvalidGroup.NotFound - The security group 'sg-0c13321fab05fc3a2' does not exist,,None,N/A,N/A,3.46s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-03255a7c76bfd0a7c,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-03255a7c76bfd0a7c,,Yes,Validation skipped,N/A,,No,Error: InvalidGroup.NotFound - The security group 'sg-03255a7c76bfd0a7c' does not exist,,None,N/A,N/A,3.45s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08c1f59c7edbadcd9,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08c1f59c7edbadcd9,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-08c1f59c7edbadcd9' does not exist.,,None,N/A,N/A,3.34s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.53s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4,,Yes,Validation skipped,N/A,,No,Error finding listeners for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.24s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-087df90f8ba056c60,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-087df90f8ba056c60,,Yes,Validation skipped,N/A,,No,Resource has dependencies: resource sg-087df90f8ba056c60 has a dependent object,,None,N/A,N/A,3.40s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/5606ff6703536e61/6e28e7f2bd679929,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.32s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/23563f9d9576302f/324b6ea53bdb69c2,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.44s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-stg-UAI2004829/ee1511167d12c523/8b4fa82f226dc520/f06aade3700bbd52,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.36s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-sparks-prd-UAI2004829/134384004e42bdaf/9d301eb730a3e47f/58545befb00779d0,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.27s
GECHG1234567,ent-emea-energy,************,ec2,security-group,sg-09d098800c98f744a,eu-west-1,arn:aws:ec2:eu-west-1:************:security-group/sg-09d098800c98f744a,,Yes,Validation skipped,N/A,,No,Resource has dependencies: resource sg-09d098800c98f744a has a dependent object,,None,N/A,N/A,3.68s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-npi-sparks-stg-UAI2004829/2e90d1b83189a133/de88284e7ed88503/7920600d6e0631b4,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.34s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b54ef7801864acc4/d873a9010df29ed4,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.28s
GECHG1234567,ent-emea-energy,************,elasticloadbalancing,listener-rule,app,eu-west-1,arn:aws:elasticloadbalancing:eu-west-1:************:listener-rule/app/i5-gs-npi-sparks-prd-UAI2004829/92cd718beee7f2db/b98fb27d5a1a1ad6/e6032aaee439966f,,Yes,Validation skipped,N/A,,No,Error finding rules for load balancer app: An error occurred (ValidationError) when calling the DescribeListeners operation: 'arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/app' is not a valid load balancer ARN,,None,N/A,N/A,3.64s
GECHG1234567,ent-emea-energy,************,ec2,instance,i-002be9747294e8877,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-002be9747294e8877,,Yes,Validation skipped,N/A,,No,Error: InvalidInstanceID.NotFound - The instance ID 'i-002be9747294e8877' does not exist,,None,N/A,N/A,3.71s
GECHG1234567,ent-emea-energy,************,ec2,instance,i-05eb965ac3fd8fb23,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-05eb965ac3fd8fb23,,Yes,Validation skipped,N/A,,No,Error: InvalidInstanceID.NotFound - The instance ID 'i-05eb965ac3fd8fb23' does not exist,,None,N/A,N/A,3.35s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0321aa3341012dc5d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0321aa3341012dc5d,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0321aa3341012dc5d' does not exist.,,None,N/A,N/A,3.54s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08292e054f597a702,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08292e054f597a702,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-08292e054f597a702' does not exist.,,None,N/A,N/A,3.39s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0f23739b81fce377d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0f23739b81fce377d,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0f23739b81fce377d' does not exist.,,None,N/A,N/A,2.98s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0a61a13260c3b8461,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0a61a13260c3b8461,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0a61a13260c3b8461' does not exist.,,None,N/A,N/A,3.42s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0b51ed17665aabeae,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0b51ed17665aabeae,,Yes,Validation skipped,N/A,,Yes,Volume vol-0b51ed17665aabeae deleted successfully,2025-05-12 19:58:47,None,N/A,N/A,39.33s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-002d9c016266809f2,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-002d9c016266809f2,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-002d9c016266809f2' does not exist.,,None,N/A,N/A,3.25s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0cd56d66953e6c11f,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0cd56d66953e6c11f,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0cd56d66953e6c11f' does not exist.,,None,N/A,N/A,3.32s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-05b68331cc9022940,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-05b68331cc9022940,,Yes,Validation skipped,N/A,,No,Error: IncorrectState - Unable to detach root volume 'vol-05b68331cc9022940' from instance 'i-01ff028a35ce74562',,None,N/A,N/A,3.69s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08a25ca18b9d2ffbe,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08a25ca18b9d2ffbe,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-08a25ca18b9d2ffbe' does not exist.,,None,N/A,N/A,3.30s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-0d221edcc77bf155d,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-0d221edcc77bf155d,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-0d221edcc77bf155d' does not exist.,,None,N/A,N/A,3.04s
GECHG1234567,ent-emea-energy,************,ec2,volume,vol-08a697f12101a69f5,eu-west-1,arn:aws:ec2:eu-west-1:************:volume/vol-08a697f12101a69f5,,Yes,Validation skipped,N/A,,No,Error: InvalidVolume.NotFound - The volume 'vol-08a697f12101a69f5' does not exist.,,None,N/A,N/A,3.06s
GECHG1234567,ent-emea-energy,************,ec2,instance,i-01ff028a35ce74562,eu-west-1,arn:aws:ec2:eu-west-1:************:instance/i-01ff028a35ce74562,,Yes,Validation skipped,N/A,,Yes,Instance state changed from running to shutting-down,2025-05-12 19:59:30,None,N/A,N/A,4.34s
