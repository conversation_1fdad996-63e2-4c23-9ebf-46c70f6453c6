"""
S3 handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tu<PERSON>, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class S3BucketHandler(BaseResourceHandler):
    """
    Handler for S3 buckets.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the S3 bucket handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an S3 bucket exists and can be deleted.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            s3 = session.client('s3', region_name=resource.region)
            
            # Check if bucket exists
            s3.head_bucket(Bucket=resource.resource_id)
            
            # Check if bucket is empty
            paginator = s3.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(Bucket=resource.resource_id, MaxKeys=1)
            
            for page in page_iterator:
                if page.get('KeyCount', 0) > 0:
                    # Bucket is not empty
                    empty_buckets = self.config.get("resources.services.s3.empty_buckets_before_deletion", False)
                    if not empty_buckets:
                        return False, f"Bucket {resource.resource_id} is not empty and empty_buckets_before_deletion is not enabled"
            
            # Check for bucket policy
            try:
                s3.get_bucket_policy(Bucket=resource.resource_id)
                self.logger.info(f"Bucket {resource.resource_id} has a bucket policy that will be deleted")
            except ClientError as e:
                if e.response['Error']['Code'] != 'NoSuchBucketPolicy':
                    self.logger.warning(f"Error checking bucket policy: {e}")
            
            # Check for bucket versioning
            try:
                versioning = s3.get_bucket_versioning(Bucket=resource.resource_id)
                if versioning.get('Status') == 'Enabled':
                    self.logger.info(f"Bucket {resource.resource_id} has versioning enabled")
            except ClientError as e:
                self.logger.warning(f"Error checking bucket versioning: {e}")
            
            return True, f"Bucket {resource.resource_id} exists and can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating S3 bucket")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent S3 bucket deletion.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            s3 = session.client('s3', region_name=resource.region)
            
            # Check for objects in the bucket
            paginator = s3.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(Bucket=resource.resource_id)
            
            object_count = 0
            for page in page_iterator:
                object_count += page.get('KeyCount', 0)
                
                # Add a sample of objects as dependencies
                if 'Contents' in page and len(dependencies) < 10:  # Limit to 10 sample objects
                    for obj in page.get('Contents', []):
                        if len(dependencies) < 10:
                            dependencies.append(ResourceDependency(
                                resource_type="s3:object",
                                resource_id=f"{resource.resource_id}/{obj['Key']}",
                                relationship="contains",
                                blocking=True  # Blocking because bucket must be empty
                            ))
            
            if object_count > 0:
                self.logger.info(f"Bucket {resource.resource_id} contains {object_count} objects")
                
                # If we have more objects than we showed in dependencies, add a note
                if object_count > 10:
                    self.logger.info(f"Showing only 10 of {object_count} objects as dependencies")
            
            # Check for bucket policy
            try:
                s3.get_bucket_policy(Bucket=resource.resource_id)
                dependencies.append(ResourceDependency(
                    resource_type="s3:bucket-policy",
                    resource_id=f"{resource.resource_id}/policy",
                    relationship="has",
                    blocking=False  # Not blocking because policy is deleted with bucket
                ))
            except ClientError as e:
                if e.response['Error']['Code'] != 'NoSuchBucketPolicy':
                    self.logger.warning(f"Error checking bucket policy: {e}")
            
            # Check for CloudFront distributions using this bucket
            try:
                cloudfront = session.client('cloudfront')
                distributions = cloudfront.list_distributions().get('DistributionList', {}).get('Items', [])
                
                for distribution in distributions:
                    origins = distribution.get('Origins', {}).get('Items', [])
                    for origin in origins:
                        if origin.get('DomainName', '').startswith(f"{resource.resource_id}.s3."):
                            dependencies.append(ResourceDependency(
                                resource_type="cloudfront:distribution",
                                resource_id=distribution['Id'],
                                relationship="origin_for",
                                blocking=True  # Blocking because CloudFront distribution must be updated first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking CloudFront dependencies: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for S3 bucket {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of S3 bucket deletion.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            s3 = session.client('s3', region_name=resource.region)
            
            # Check if bucket exists
            s3.head_bucket(Bucket=resource.resource_id)
            
            # Check if bucket is empty
            paginator = s3.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(Bucket=resource.resource_id, MaxKeys=1)
            
            for page in page_iterator:
                if page.get('KeyCount', 0) > 0:
                    # Bucket is not empty
                    empty_buckets = self.config.get("resources.services.s3.empty_buckets_before_deletion", False)
                    if not empty_buckets:
                        return False, f"Bucket {resource.resource_id} is not empty and empty_buckets_before_deletion is not enabled"
                    else:
                        self.logger.info(f"Dry run: Would empty bucket {resource.resource_id} before deletion")
            
            # Check for bucket policy
            try:
                s3.get_bucket_policy(Bucket=resource.resource_id)
                self.logger.info(f"Dry run: Would delete bucket policy for {resource.resource_id}")
            except ClientError as e:
                if e.response['Error']['Code'] != 'NoSuchBucketPolicy':
                    self.logger.warning(f"Error checking bucket policy: {e}")
            
            self.logger.info(f"Dry run: Would delete bucket {resource.resource_id}")
            return True, f"Bucket {resource.resource_id} can be deleted"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deletion of S3 bucket")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Delete an S3 bucket.
        
        Args:
            resource: Resource to delete.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            s3 = session.client('s3', region_name=resource.region)
            s3_resource = session.resource('s3', region_name=resource.region)
            bucket = s3_resource.Bucket(resource.resource_id)
            
            # Check if bucket is empty
            paginator = s3.get_paginator('list_objects_v2')
            page_iterator = paginator.paginate(Bucket=resource.resource_id)
            
            has_objects = False
            for page in page_iterator:
                if page.get('KeyCount', 0) > 0:
                    has_objects = True
                    break
            
            if has_objects:
                # Bucket is not empty
                empty_buckets = self.config.get("resources.services.s3.empty_buckets_before_deletion", False)
                if not empty_buckets:
                    return False, f"Bucket {resource.resource_id} is not empty and empty_buckets_before_deletion is not enabled"
                else:
                    self.logger.info(f"Emptying bucket {resource.resource_id} before deletion")
                    
                    # Check if versioning is enabled
                    versioning = s3.get_bucket_versioning(Bucket=resource.resource_id)
                    versioning_enabled = versioning.get('Status') == 'Enabled'
                    
                    if versioning_enabled:
                        # Delete all versions and delete markers
                        self.logger.info(f"Deleting all object versions from bucket {resource.resource_id}")
                        bucket.object_versions.delete()
                    else:
                        # Delete all objects
                        self.logger.info(f"Deleting all objects from bucket {resource.resource_id}")
                        bucket.objects.all().delete()
            
            # Delete bucket policy if it exists
            try:
                s3.delete_bucket_policy(Bucket=resource.resource_id)
                self.logger.info(f"Deleted bucket policy for {resource.resource_id}")
            except ClientError as e:
                if e.response['Error']['Code'] != 'NoSuchBucketPolicy':
                    self.logger.warning(f"Error deleting bucket policy: {e}")
            
            # Delete the bucket
            s3.delete_bucket(Bucket=resource.resource_id)
            
            return True, f"Bucket {resource.resource_id} deleted successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deleting S3 bucket")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an S3 bucket.
        
        Note: S3 API doesn't provide bucket creation date directly.
        We'll try to estimate it from the earliest object creation date.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            s3 = session.client('s3', region_name=resource.region)
            
            # Try to get bucket creation date from bucket lifecycle configuration
            try:
                response = s3.get_bucket_lifecycle_configuration(Bucket=resource.resource_id)
                # Unfortunately, lifecycle configuration doesn't contain creation date
            except ClientError:
                pass
            
            # S3 doesn't provide bucket creation date directly
            # We could try to estimate it from the earliest object creation date,
            # but that's not reliable and could be expensive for large buckets
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for S3 bucket {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an S3 bucket.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            s3 = session.client('s3', region_name=resource.region)
            
            try:
                response = s3.get_bucket_tagging(Bucket=resource.resource_id)
                tag_set = response.get('TagSet', [])
                
                return {tag['Key']: tag['Value'] for tag in tag_set}
            except ClientError as e:
                if e.response['Error']['Code'] == 'NoSuchTagSet':
                    return {}
                raise
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for S3 bucket {resource.resource_id}: {e}")
            return {}
