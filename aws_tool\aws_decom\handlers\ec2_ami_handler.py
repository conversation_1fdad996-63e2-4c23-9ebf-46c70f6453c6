"""
EC2 AMI handler for AWS Resource Decommissioning Tool.
"""

import logging
from typing import List, Optional, Tuple, Dict, Any
import boto3
from botocore.exceptions import ClientError

from .base_handler import BaseResourceHandler
from ..models.resource import Resource, ResourceDependency
from ..core.config_manager import ConfigManager

class EC2AMIHandler(BaseResourceHandler):
    """
    Handler for EC2 AMIs (Amazon Machine Images).
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the EC2 AMI handler.
        
        Args:
            config_manager: Configuration manager instance.
        """
        super().__init__(config_manager)
    
    def validate_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Validate that an EC2 AMI exists and can be deregistered.
        
        Args:
            resource: Resource to validate.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if AMI exists
            response = ec2.describe_images(ImageIds=[resource.resource_id])
            
            # Check if there are any images
            if not response.get('Images'):
                return False, f"AMI {resource.resource_id} not found"
            
            # Get image state
            image = response['Images'][0]
            state = image.get('State')
            
            # Check if image is in a valid state for deregistration
            if state not in ['available', 'failed', 'error']:
                return False, f"AMI {resource.resource_id} is in {state} state and cannot be deregistered"
            
            # Check if image is being used by any instances
            try:
                instance_response = ec2.describe_instances(Filters=[
                    {'Name': 'image-id', 'Values': [resource.resource_id]}
                ])
                
                instances = []
                for reservation in instance_response.get('Reservations', []):
                    for instance in reservation.get('Instances', []):
                        if instance.get('State', {}).get('Name') not in ['terminated', 'shutting-down']:
                            instances.append(instance['InstanceId'])
                
                if instances:
                    return False, f"AMI {resource.resource_id} is being used by instances: {', '.join(instances)}"
            except ClientError as e:
                self.logger.warning(f"Error checking instance usage: {e}")
            
            # Check if image is public
            if image.get('Public', False):
                self.logger.warning(f"AMI {resource.resource_id} is public")
            
            return True, f"AMI {resource.resource_id} exists and can be deregistered"
            
        except ClientError as e:
            return self.handle_client_error(e, "validating EC2 AMI")
    
    def check_dependencies(self, resource: Resource, session: boto3.Session) -> List[ResourceDependency]:
        """
        Check for dependencies that might prevent EC2 AMI deregistration.
        
        Args:
            resource: Resource to check.
            session: boto3 session.
            
        Returns:
            List of dependencies.
        """
        dependencies = []
        
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if image is being used by any instances
            try:
                instance_response = ec2.describe_instances(Filters=[
                    {'Name': 'image-id', 'Values': [resource.resource_id]}
                ])
                
                for reservation in instance_response.get('Reservations', []):
                    for instance in reservation.get('Instances', []):
                        if instance.get('State', {}).get('Name') not in ['terminated', 'shutting-down']:
                            dependencies.append(ResourceDependency(
                                resource_type="ec2:instance",
                                resource_id=instance['InstanceId'],
                                relationship="launched_from",
                                blocking=True  # Blocking because instances must be terminated first
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking instance usage: {e}")
            
            # Check for snapshots associated with the AMI
            try:
                image_response = ec2.describe_images(ImageIds=[resource.resource_id])
                
                if image_response.get('Images'):
                    image = image_response['Images'][0]
                    
                    for block_device in image.get('BlockDeviceMappings', []):
                        if 'Ebs' in block_device and 'SnapshotId' in block_device['Ebs']:
                            snapshot_id = block_device['Ebs']['SnapshotId']
                            dependencies.append(ResourceDependency(
                                resource_type="ec2:snapshot",
                                resource_id=snapshot_id,
                                relationship="uses",
                                blocking=False  # Not blocking because snapshots can be deleted after AMI
                            ))
            except ClientError as e:
                self.logger.warning(f"Error checking snapshot dependencies: {e}")
            
            # Check if image is shared with other accounts
            try:
                image_response = ec2.describe_images(ImageIds=[resource.resource_id])
                
                if image_response.get('Images'):
                    image = image_response['Images'][0]
                    
                    # Check for public sharing
                    if image.get('Public', False):
                        dependencies.append(ResourceDependency(
                            resource_type="ec2:image-permission",
                            resource_id=f"{resource.resource_id}/public",
                            relationship="shared_with",
                            blocking=False  # Not blocking because permissions are removed with image
                        ))
                    
                    # Check for specific account sharing
                    try:
                        permission_response = ec2.describe_image_attribute(
                            ImageId=resource.resource_id,
                            Attribute='launchPermission'
                        )
                        
                        for permission in permission_response.get('LaunchPermissions', []):
                            if 'UserId' in permission:
                                dependencies.append(ResourceDependency(
                                    resource_type="ec2:image-permission",
                                    resource_id=f"{resource.resource_id}/{permission['UserId']}",
                                    relationship="shared_with",
                                    blocking=False  # Not blocking because permissions are removed with image
                                ))
                    except ClientError as e:
                        self.logger.warning(f"Error checking image permissions: {e}")
            except ClientError as e:
                self.logger.warning(f"Error checking image details: {e}")
            
            return dependencies
            
        except ClientError as e:
            self.logger.error(f"Error checking dependencies for EC2 AMI {resource.resource_id}: {e}")
            return dependencies
    
    def dry_run(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Perform a dry run of EC2 AMI deregistration.
        
        Args:
            resource: Resource to deregister.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if AMI exists
            response = ec2.describe_images(ImageIds=[resource.resource_id])
            
            if not response.get('Images'):
                return False, f"AMI {resource.resource_id} not found"
            
            # Get image details
            image = response['Images'][0]
            
            # Check if image is being used by any instances
            try:
                instance_response = ec2.describe_instances(Filters=[
                    {'Name': 'image-id', 'Values': [resource.resource_id]}
                ])
                
                instances = []
                for reservation in instance_response.get('Reservations', []):
                    for instance in reservation.get('Instances', []):
                        if instance.get('State', {}).get('Name') not in ['terminated', 'shutting-down']:
                            instances.append(instance['InstanceId'])
                
                if instances:
                    return False, f"AMI {resource.resource_id} is being used by instances: {', '.join(instances)}"
            except ClientError as e:
                self.logger.warning(f"Error checking instance usage: {e}")
            
            # Get snapshots associated with the AMI
            snapshots = []
            for block_device in image.get('BlockDeviceMappings', []):
                if 'Ebs' in block_device and 'SnapshotId' in block_device['Ebs']:
                    snapshots.append(block_device['Ebs']['SnapshotId'])
            
            # Check if we should delete snapshots
            delete_snapshots = self.config.get("resources.services.ec2.delete_ami_snapshots", False)
            
            self.logger.info(f"Dry run: Would deregister AMI {resource.resource_id}")
            if delete_snapshots and snapshots:
                self.logger.info(f"Dry run: Would delete associated snapshots: {', '.join(snapshots)}")
            
            return True, f"AMI {resource.resource_id} can be deregistered"
            
        except ClientError as e:
            return self.handle_client_error(e, "dry run deregistration of EC2 AMI")
    
    def delete_resource(self, resource: Resource, session: boto3.Session) -> Tuple[bool, str]:
        """
        Deregister an EC2 AMI.
        
        Args:
            resource: Resource to deregister.
            session: boto3 session.
            
        Returns:
            Tuple of (success, message).
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            # Check if AMI exists
            response = ec2.describe_images(ImageIds=[resource.resource_id])
            
            if not response.get('Images'):
                return False, f"AMI {resource.resource_id} not found"
            
            # Get image details
            image = response['Images'][0]
            
            # Get snapshots associated with the AMI
            snapshots = []
            for block_device in image.get('BlockDeviceMappings', []):
                if 'Ebs' in block_device and 'SnapshotId' in block_device['Ebs']:
                    snapshots.append(block_device['Ebs']['SnapshotId'])
            
            # Deregister the AMI
            ec2.deregister_image(ImageId=resource.resource_id)
            
            # Check if we should delete snapshots
            delete_snapshots = self.config.get("resources.services.ec2.delete_ami_snapshots", False)
            
            if delete_snapshots and snapshots:
                self.logger.info(f"Deleting snapshots associated with AMI {resource.resource_id}: {', '.join(snapshots)}")
                
                for snapshot_id in snapshots:
                    try:
                        ec2.delete_snapshot(SnapshotId=snapshot_id)
                        self.logger.info(f"Deleted snapshot {snapshot_id}")
                    except ClientError as e:
                        self.logger.error(f"Error deleting snapshot {snapshot_id}: {e}")
            
            return True, f"AMI {resource.resource_id} deregistered successfully"
            
        except ClientError as e:
            return self.handle_client_error(e, "deregistering EC2 AMI")
    
    def get_created_date(self, resource: Resource, session: boto3.Session) -> Optional[str]:
        """
        Get the creation time of an EC2 AMI.
        
        Args:
            resource: Resource to get creation time for.
            session: boto3 session.
            
        Returns:
            Creation time as string or None if not available.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_images(ImageIds=[resource.resource_id])
            
            if not response.get('Images'):
                return None
            
            image = response['Images'][0]
            creation_date = image.get('CreationDate')
            
            if creation_date:
                # AWS returns creation date in a specific format, we'll return it as is
                return creation_date
            
            return None
            
        except ClientError as e:
            self.logger.error(f"Error getting creation time for EC2 AMI {resource.resource_id}: {e}")
            return None
    
    def get_tags(self, resource: Resource, session: boto3.Session) -> Dict[str, str]:
        """
        Get tags for an EC2 AMI.
        
        Args:
            resource: Resource to get tags for.
            session: boto3 session.
            
        Returns:
            Dict of tags.
        """
        try:
            ec2 = session.client('ec2', region_name=resource.region)
            
            response = ec2.describe_images(ImageIds=[resource.resource_id])
            
            if not response.get('Images'):
                return {}
            
            image = response['Images'][0]
            tags = image.get('Tags', [])
            
            return {tag['Key']: tag['Value'] for tag in tags}
            
        except ClientError as e:
            self.logger.error(f"Error getting tags for EC2 AMI {resource.resource_id}: {e}")
            return {}
