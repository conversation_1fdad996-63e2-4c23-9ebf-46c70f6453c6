import boto3
import argparse
import logging
import csv
import re
import os
import sys
import time
import json
from datetime import datetime
from botocore.exceptions import ClientError
from tqdm import tqdm

# AWS Resource ARN Parsing Patterns
ARN_PATTERNS = {
    'ec2': r'arn:aws:ec2:(.*?):(.*?):instance/(.*?)$',
    'ec2:image': r'arn:aws:ec2:(.*?):(.*?):image/(.*?)$',
    'ec2:volume': r'arn:aws:ec2:(.*?):(.*?):volume/(.*?)$',
    'ec2:snapshot': r'arn:aws:ec2:(.*?):(.*?):snapshot/(.*?)$',
    'ec2:securitygroup': r'arn:aws:ec2:(.*?):(.*?):security-group/(.*?)$',
    'lambda': r'arn:aws:lambda:(.*?):(.*?):function:(.*?)$',
    'sns': r'arn:aws:sns:(.*?):(.*?):(.*?)$',
    's3': r'arn:aws:s3:::(.*?)$',
    'cloudwatch:alarm': r'arn:aws:cloudwatch:(.*?):(.*?):alarm:(.*?)$',
    'secretsmanager:secret': r'arn:aws:secretsmanager:(.*?):(.*?):secret:(.*?)$',
    'states:stateMachine': r'arn:aws:states:(.*?):(.*?):stateMachine:(.*?)$',
    'events:rule': r'arn:aws:events:(.*?):(.*?):rule/(.*?)$',
    'cloudformation:stack': r'arn:aws:cloudformation:(.*?):(.*?):stack/(.*?)(?:/.*)?$',
    'elbv2:listener': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener/(.*?)/(.*?)$',
    'elbv2:targetgroup': r'arn:aws:elasticloadbalancing:(.*?):(.*?):targetgroup/(.*?)/(.*?)$',
    'elbv2:listener-rule': r'arn:aws:elasticloadbalancing:(.*?):(.*?):listener-rule/(.*?)/(.*?)$',
    'ssm:parameter': r'arn:aws:ssm:(.*?):(.*?):parameter/(.*?)$',
    'backup:backup-plan': r'arn:aws:backup:(.*?):(.*?):backup-plan:(.*?)$',
    'backup:backup-vault': r'arn:aws:backup:(.*?):(.*?):backup-vault:(.*?)$',
    'sns:subscription': r'arn:aws:sns:(.*?):(.*?):.*?:.*?$',
}

# Initialize Logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Parse the ARN from input
def parse_arn(arn):
    parts = arn.split(':')
    if len(parts) < 6:
        return 'unknown', arn, None, None

    service = parts[2]
    region = parts[3]
    account_id = parts[4]
    resource = parts[5]

    # Match against patterns for more specific resource types
    for rtype, pattern in ARN_PATTERNS.items():
        match = re.match(pattern, arn)
        if match:
            resource_id = match.groups()[-1]
            return rtype, resource_id, region, account_id

    # Default parsing if no pattern matches
    resource_type = 'unknown'
    resource_id = resource

    # Try to extract resource type and ID based on common formats
    if '/' in resource:
        tokens = resource.split('/')
        if len(tokens) >= 2:
            resource_type = f"{service}:{tokens[0]}"
            resource_id = '/'.join(tokens[1:])
    elif ':' in resource:
        tokens = resource.split(':')
        if len(tokens) >= 2:
            resource_type = f"{service}:{tokens[0]}"
            resource_id = ':'.join(tokens[1:])
    else:
        resource_type = service

    return resource_type, resource_id, region, account_id

# Function to validate CR number format
def validate_cr_number(cr_no):
    pattern = r"^GECHG\d{7}$"
    if re.match(pattern, cr_no):
        return True
    else:
        error_msg = f"Invalid CR number '{cr_no}'. It must start with 'GECHG' followed by 7 digits."
        logging.error(error_msg)
        print(f"{error_msg}")
        return False

# Function to check if the resource exists
def validate_resource_existence(arn, resource_type, session):
    try:
        client = session.client(resource_type.split(':')[0])
        if resource_type.startswith('ec2'):
            if resource_type == 'ec2:instance':
                instance_id = arn.split(':')[-1]
                client.describe_instances(InstanceIds=[instance_id])
            elif resource_type == 'ec2:volume':
                volume_id = arn.split(':')[-1]
                client.describe_volumes(VolumeIds=[volume_id])
            elif resource_type == 'ec2:snapshot':
                snapshot_id = arn.split(':')[-1]
                client.describe_snapshots(SnapshotIds=[snapshot_id])
            elif resource_type == 'ec2:securitygroup':
                security_group_id = arn.split(':')[-1]
                client.describe_security_groups(GroupIds=[security_group_id])
        elif resource_type.startswith('s3'):
            bucket_name = arn.split(':')[-1]
            client.head_bucket(Bucket=bucket_name)
        elif resource_type.startswith('lambda'):
            function_name = arn.split(':')[-1]
            client.get_function(FunctionName=function_name)
        elif resource_type.startswith('sns'):
            topic_arn = arn.split(':')[-1]
            client.get_topic_attributes(TopicArn=topic_arn)
        elif resource_type.startswith('cloudwatch:alarm'):
            alarm_name = arn.split(':')[-1]
            client.describe_alarms(AlarmNames=[alarm_name])
        elif resource_type.startswith('secretsmanager:secret'):
            secret_name = arn.split(':')[-1]
            client.describe_secret(SecretId=secret_name)
        elif resource_type.startswith('states:stateMachine'):
            state_machine_arn = arn.split(':')[-1]
            client.describe_state_machine(stateMachineArn=state_machine_arn)
        elif resource_type.startswith('events:rule'):
            rule_name = arn.split(':')[-1]
            client.describe_rule(Name=rule_name)
        elif resource_type.startswith('cloudformation:stack'):
            stack_name = arn.split(':')[-1]
            client.describe_stacks(StackName=stack_name)
        elif resource_type.startswith('elbv2'):
            load_balancer_name = arn.split(':')[-2]
            client.describe_load_balancers(Names=[load_balancer_name])
        elif resource_type.startswith('ssm:parameter'):
            parameter_name = arn.split(':')[-1]
            client.get_parameter(Name=parameter_name)
        elif resource_type.startswith('backup:backup-plan'):
            backup_plan_name = arn.split(':')[-1]
            client.describe_backup_plan(BackupPlanName=backup_plan_name)
        return True
    except ClientError as e:
        logging.error(f"Error validating resource {arn}: {str(e)}")
        return False

# Function to handle the dry-run operation (Simulation)
def dry_run_operation(arn, resource_type, session, cr_no):
    logging.info(f"Dry-run: Validating deletion of {resource_type} with ARN {arn}")

    try:
        # Extract resource ID from ARN
        resource_id = arn.split(':')[-1]
        service = resource_type.split(':')[0] if ':' in resource_type else resource_type
        region = arn.split(':')[3]

        # Create appropriate client based on service
        client = session.client(service, region_name=region, verify=False)

        # Validate resource can be deleted based on resource type
        if resource_type == 'ec2:instance':
            # First disable termination protection
            client.modify_instance_attribute(
                InstanceId=resource_id,
                DisableApiTermination={'Value': False}
            )
            # Then check if we can terminate it
            client.describe_instances(InstanceIds=[resource_id], DryRun=True)
            logging.info(f"Validated EC2 instance can be terminated: {resource_id}")

        elif resource_type == 'ec2:volume':
            client.describe_volumes(VolumeIds=[resource_id], DryRun=True)
            logging.info(f"Validated EC2 volume can be deleted: {resource_id}")

        elif resource_type == 'ec2:snapshot':
            client.describe_snapshots(SnapshotIds=[resource_id], DryRun=True)
            logging.info(f"Validated EC2 snapshot can be deleted: {resource_id}")

        elif resource_type == 'ec2:image' or resource_type == 'ec2:ami':
            client.describe_images(ImageIds=[resource_id], DryRun=True)
            logging.info(f"Validated EC2 image can be deregistered: {resource_id}")

        elif resource_type == 'ec2:securitygroup':
            client.describe_security_groups(GroupIds=[resource_id], DryRun=True)
            logging.info(f"Validated EC2 security group can be deleted: {resource_id}")

        elif resource_type == 'lambda':
            lambda_client = session.client('lambda', region_name=region, verify=False)
            lambda_client.get_function(FunctionName=resource_id)
            logging.info(f"Validated Lambda function can be deleted: {resource_id}")

        elif resource_type == 'sns':
            sns_client = session.client('sns', region_name=region, verify=False)
            sns_client.get_topic_attributes(TopicArn=arn)
            logging.info(f"Validated SNS topic can be deleted: {resource_id}")

        elif resource_type == 's3':
            s3_client = session.client('s3', region_name=region, verify=False)
            s3_client.head_bucket(Bucket=resource_id)
            logging.info(f"Validated S3 bucket can be deleted: {resource_id}")

        elif resource_type == 'cloudwatch:alarm':
            cw_client = session.client('cloudwatch', region_name=region, verify=False)
            cw_client.describe_alarms(AlarmNames=[resource_id])
            logging.info(f"Validated CloudWatch alarm can be deleted: {resource_id}")

        elif resource_type == 'secretsmanager:secret':
            sm_client = session.client('secretsmanager', region_name=region, verify=False)
            sm_client.describe_secret(SecretId=resource_id)
            logging.info(f"Validated Secrets Manager secret can be deleted: {resource_id}")

        elif resource_type == 'states:stateMachine':
            sf_client = session.client('stepfunctions', region_name=region, verify=False)
            sf_client.describe_state_machine(stateMachineArn=arn)
            logging.info(f"Validated Step Functions state machine can be deleted: {resource_id}")

        elif resource_type == 'events:rule':
            events_client = session.client('events', region_name=region, verify=False)
            events_client.describe_rule(Name=resource_id)
            logging.info(f"Validated EventBridge rule can be deleted: {resource_id}")

        elif resource_type == 'cloudformation:stack':
            cf_client = session.client('cloudformation', region_name=region, verify=False)
            cf_client.describe_stacks(StackName=resource_id)
            logging.info(f"Validated CloudFormation stack can be deleted: {resource_id}")

        elif resource_type.startswith('elbv2:'):
            elb_client = session.client('elbv2', region_name=region, verify=False)
            if resource_type == 'elbv2:listener':
                elb_client.describe_listeners(ListenerArns=[resource_id])
                logging.info(f"Validated ELB listener can be deleted: {resource_id}")
            elif resource_type == 'elbv2:targetgroup':
                elb_client.describe_target_groups(TargetGroupArns=[resource_id])
                logging.info(f"Validated ELB target group can be deleted: {resource_id}")
            elif resource_type == 'elbv2:listener-rule':
                elb_client.describe_rules(RuleArns=[resource_id])
                logging.info(f"Validated ELB listener rule can be deleted: {resource_id}")

        elif resource_type == 'ssm:parameter':
            ssm_client = session.client('ssm', region_name=region, verify=False)
            ssm_client.get_parameter(Name=resource_id)
            logging.info(f"Validated SSM parameter can be deleted: {resource_id}")

        elif resource_type == 'backup:backup-plan':
            backup_client = session.client('backup', region_name=region, verify=False)
            backup_client.get_backup_plan(BackupPlanId=resource_id)
            logging.info(f"Validated AWS Backup plan can be deleted: {resource_id}")

        elif resource_type == 'backup:backup-vault':
            backup_client = session.client('backup', region_name=region, verify=False)
            backup_client.describe_backup_vault(BackupVaultName=resource_id)
            logging.info(f"Validated AWS Backup vault can be deleted: {resource_id}")

        elif resource_type == 'sns:subscription':
            sns_client = session.client('sns', region_name=region, verify=False)
            sns_client.get_subscription_attributes(SubscriptionArn=arn)
            logging.info(f"Validated SNS subscription can be deleted: {resource_id}")

        else:
            logging.warning(f"Unsupported resource type {resource_type} for ARN {arn} - skipping dry-run validation")
            # Return true for unsupported types to avoid failing the entire process
            return True

        return True

    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        # DryRunOperation error means the operation would have succeeded
        if error_code == 'DryRunOperation':
            logging.info(f"Dry run successful for {resource_type} with ARN {arn}")
            return True
        else:
            logging.error(f"Error in dry-run for resource {arn}: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Error in dry-run for resource {arn}: {str(e)}")
        return False


# Function to handle the actual deletion
def delete_operation(arn, resource_type, session, cr_no):
    logging.info(f"Deleting resource {resource_type} with ARN {arn}")
    try:
        client = session.client(resource_type.split(':')[0])
        if resource_type == 'ec2:instance':
            instance_id = arn.split(':')[-1]
            client.terminate_instances(InstanceIds=[instance_id])
        elif resource_type == 'ec2:volume':
            volume_id = arn.split(':')[-1]
            client.delete_volume(VolumeId=volume_id)
        elif resource_type == 'ec2:snapshot':
            snapshot_id = arn.split(':')[-1]
            client.delete_snapshot(SnapshotId=snapshot_id)
        elif resource_type == 'ec2:securitygroup':
            security_group_id = arn.split(':')[-1]
            client.delete_security_group(GroupId=security_group_id)
        elif resource_type == 'lambda':
            function_name = arn.split(':')[-1]
            client.delete_function(FunctionName=function_name)
        elif resource_type == 'sns':
            topic_arn = arn.split(':')[-1]
            client.delete_topic(TopicArn=topic_arn)
        elif resource_type == 's3':
            bucket_name = arn.split(':')[-1]
            client.delete_bucket(Bucket=bucket_name)
        elif resource_type == 'cloudwatch:alarm':
            alarm_name = arn.split(':')[-1]
            client.delete_alarms(AlarmNames=[alarm_name])
        elif resource_type == 'secretsmanager:secret':
            secret_name = arn.split(':')[-1]
            client.delete_secret(SecretId=secret_name)
        elif resource_type == 'states:stateMachine':
            state_machine_arn = arn.split(':')[-1]
            client.delete_state_machine(stateMachineArn=state_machine_arn)
        elif resource_type == 'events:rule':
            rule_name = arn.split(':')[-1]
            client.remove_targets(Rule=rule_name, Ids=[rule_name])
            client.delete_rule(Name=rule_name)
        elif resource_type == 'cloudformation:stack':
            stack_name = arn.split(':')[-1]
            client.delete_stack(StackName=stack_name)
        elif resource_type == 'elbv2:listener':
            listener_arn = arn.split(':')[-1]
            client.delete_listener(ListenerArn=listener_arn)
        elif resource_type == 'elbv2:targetgroup':
            target_group_arn = arn.split(':')[-1]
            client.delete_target_group(TargetGroupArn=target_group_arn)
        elif resource_type == 'elbv2:listener-rule':
            listener_rule_arn = arn.split(':')[-1]
            client.delete_listener_rule(ListenerRuleArn=listener_rule_arn)
        elif resource_type == 'ssm:parameter':
            parameter_name = arn.split(':')[-1]
            client.delete_parameter(Name=parameter_name)
        elif resource_type == 'backup:backup-plan':
            backup_plan_id = arn.split(':')[-1]
            client.delete_backup_plan(BackupPlanId=backup_plan_id)
        return True
    except ClientError as e:
        logging.error(f"Error deleting resource {arn}: {str(e)}")
        return False

# Function to process and delete resources from CSV
def process_resources_from_csv(input_file, cr_no, dry_run=False):
    successful_deletions = []
    failed_deletions = []

    with open(input_file, 'r') as file:
        reader = csv.reader(file)
        for row in reader:
            if not row:
                continue

            arn = row[0].strip()  # Clean the ARN
            if not arn:
                continue

            # Parse ARN to get components
            resource_type, resource_id, region, account_id = parse_arn(arn)

            if resource_type == 'unknown':
                logging.warning(f"Unsupported ARN format: {arn}")
                failed_deletions.append((arn, "Unsupported ARN format"))
                continue

            # Create session for this specific account
            try:
                profile_name = f"support-{account_id}"
                session = boto3.Session(profile_name=profile_name, region_name=region)
            except Exception as e:
                logging.error(f"Failed to create session for account {account_id}: {str(e)}")
                failed_deletions.append((arn, f"Session creation failed: {str(e)}"))
                continue

            # Validate resource existence
            valid_resource = validate_resource_existence(arn, resource_type, session)

            if valid_resource:
                if dry_run:
                    if dry_run_operation(arn, resource_type, session, cr_no):
                        successful_deletions.append((arn, "Dry run successful"))
                    else:
                        failed_deletions.append((arn, "Dry run failed"))
                else:
                    if delete_operation(arn, resource_type, session, cr_no):
                        successful_deletions.append((arn, "Deleted successfully"))
                    else:
                        failed_deletions.append((arn, "Deletion failed"))
            else:
                failed_deletions.append((arn, "Resource validation failed"))

    return successful_deletions, failed_deletions

# Main execution
def main():
    parser = argparse.ArgumentParser(description="AWS Resource Cleanup Tool")
    parser.add_argument('--cr-no', type=str, required=True, help="Change Request number (format: GECHG1234567)")
    parser.add_argument('--dry-run', action='store_true', help="Perform a dry-run without deletion")
    parser.add_argument('--input', type=str, required=True, help="CSV file containing ARNs")
    parser.add_argument('--version', action='version', version="AWS Cleanup Tool v1.0", help="Show version")
    args = parser.parse_args()

    if not validate_cr_number(args.cr_no):
        sys.exit(1)

    # Create output directories if they don't exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)

    # Process resources
    successful_deletions, failed_deletions = process_resources_from_csv(args.input, args.cr_no, args.dry_run)

    # Output to CSV
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    output_file = f"results/cleanup_results_{args.cr_no}_{timestamp}.csv"
    with open(output_file, 'w', newline='') as csvfile:
        fieldnames = ['ARN', 'Status', 'Message']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for arn, message in successful_deletions:
            writer.writerow({'ARN': arn, 'Status': 'Success', 'Message': message})
        for arn, error in failed_deletions:
            writer.writerow({'ARN': arn, 'Status': 'Failed', 'Message': error})

    print(f"Output CSV file generated: {output_file}")
    print(f"Successfully processed: {len(successful_deletions)} resources.")
    print(f"Failed to process: {len(failed_deletions)} resources.")

    # Print summary by resource type
    resource_summary = {}
    for arn, _ in successful_deletions:
        resource_type, _, _, _ = parse_arn(arn)
        resource_summary.setdefault(resource_type, {'success': 0, 'failed': 0})
        resource_summary[resource_type]['success'] += 1

    for arn, _ in failed_deletions:
        resource_type, _, _, _ = parse_arn(arn)
        resource_summary.setdefault(resource_type, {'success': 0, 'failed': 0})
        resource_summary[resource_type]['failed'] += 1

    print("\nResource Type Summary:")
    for resource_type, counts in resource_summary.items():
        print(f"{resource_type}: {counts['success']} successful, {counts['failed']} failed")

if __name__ == "__main__":
    main()
